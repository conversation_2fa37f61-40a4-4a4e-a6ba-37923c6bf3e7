{"@sage/xtrem-upgrade-test-base": [{"topic": "NodeForCustomSqlScript/asyncExport/start", "queue": "import-export", "sourceFileName": "node-for-custom-sql-script.ts"}, {"topic": "NodeForDataPatches/asyncExport/start", "queue": "import-export", "sourceFileName": "node-for-data-patches.ts"}, {"topic": "UpdateConstructorWithSubNodeLevel2New/asyncExport/start", "queue": "import-export", "sourceFileName": "update-constructor-with-sub-node-level-2-new.ts"}, {"topic": "UpgradeAddColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-add-columns.ts"}, {"topic": "UpgradeAlterColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-alter-columns.ts"}, {"topic": "UpgradeCanBulk/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-can-bulk.ts"}, {"topic": "UpgradeCanBulk/bulkDelete/start", "queue": "upgrade-test", "sourceFileName": "upgrade-can-bulk.ts"}, {"topic": "UpgradeCanBulkAdded/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-can-bulk-added.ts"}, {"topic": "UpgradeCanBulkAdded/bulkDelete/start", "queue": "upgrade-test", "sourceFileName": "upgrade-can-bulk-added.ts"}, {"topic": "UpgradeChangeBaseNode/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-change-base-node.ts"}, {"topic": "UpgradeCustomSqlUpdated/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-custom-sql-updated.ts"}, {"topic": "UpgradeCustomSqlV2/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-custom-sql-v2.ts"}, {"topic": "UpgradeDataCustomSqlAction/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-data-custom-sql-action.ts"}, {"topic": "UpgradeDatatypes/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-datatypes.ts"}, {"topic": "UpgradeDeleteColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-delete-columns.ts"}, {"topic": "UpgradeEnumArray/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-enum-array.ts"}, {"topic": "UpgradeNodeToExtend/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-node-to-extend.ts"}, {"topic": "UpgradeNotifyUpdated/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-notify-updated.ts"}, {"topic": "UpgradePropertyToEncrypt/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-property-to-encrypt.ts"}, {"topic": "UpgradeReferenced/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-referenced.ts"}, {"topic": "UpgradeRefToCustomSqlUpdated/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-ref-to-custom-sql-updated.ts"}, {"topic": "UpgradeReloadCsvVendorChild/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-csv-vendor-child.ts"}, {"topic": "UpgradeReloadCsvVendorParent/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-csv-vendor-parent.ts"}, {"topic": "UpgradeReloadFromCsv/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-from-csv.ts"}, {"topic": "UpgradeReloadFromVendorCsv/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-reload-from-vendor-csv.ts"}, {"topic": "UpgradeRemoveBaseNode/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-remove-base-node.ts"}, {"topic": "UpgradeRenameColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-columns.ts"}, {"topic": "UpgradeRenameEnum/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-enum.ts"}, {"topic": "UpgradeRenameEnumSecondLocation/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-enum-second-location.ts"}, {"topic": "UpgradeRenameTableV2/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-table.ts"}, {"topic": "UpgradeRenameTableWithAttachments2/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-table-with-attachments-2.ts"}, {"topic": "UpgradeRenameTableWithNotifyV2/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-table.ts"}, {"topic": "UpgradeRenameVitalChildV2/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-vital-child.ts"}, {"topic": "UpgradeRenameVitalParent/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-rename-vital-parent.ts"}, {"topic": "UpgradeSubNode1/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-sub-node-1.ts"}, {"topic": "UpgradeUpdateColumns/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-update-columns.ts"}, {"topic": "UpgradeVitalChild/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-vital-child.ts"}, {"topic": "UpgradeVitalParent/asyncExport/start", "queue": "import-export", "sourceFileName": "upgrade-vital-parent.ts"}]}