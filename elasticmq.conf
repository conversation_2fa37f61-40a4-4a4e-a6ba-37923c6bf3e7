include classpath("application.conf")

queues {
	ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	demo-queue {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	dev-dead-letter-queue {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
	}
	finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	glossary--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	platform--workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	sdmo--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	shopfloor--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-sales--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-stock--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-dashboard-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-main-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-master-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-shopfloor-tracking-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-main-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-master-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-sales-sales-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-main-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-master-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-showcase-stock-stock-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-main-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-x-3-connector-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-ap-automation-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-auditing-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-authorization-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-avalara-gateway-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-cake-hr-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-communication-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-customization-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-dashboard-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-declarations-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-distribution-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-finance-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-gateway-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-frp-1000-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-glossary-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-import-export-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-finance-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-gateway-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-intacct-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-integration-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-interop-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-landed-cost-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mailer-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-manufacturing-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-master-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-metadata-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-mrp-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-purchasing-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-reporting-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-restaurant-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-routing-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sage-network-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-sales-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-scheduler-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-service-fabric-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-services-main-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-shopify-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-bundle-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-show-case-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-so-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-blend-po-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-stock-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-structure-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-supply-chain-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-synchronization-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-system-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-tax-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-technical-data-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-upload-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-ap-automation {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-finance {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-glossary {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-intacct {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-manufacturing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-purchasing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-reference {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-sage-network {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-shopfloor {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-showcase {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-showcase-sales {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-showcase-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-showcase-test-specific {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-stock {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-test-import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-test-routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-test-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-test-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-test-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	test-xtrem-workflow-workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	unit-test-message-queue {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	unit-test-notification-queue {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	unit-test-visibility-multiple-listeners-queue {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	workflow-test {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--authorization {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--import-export {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--infrastructure-event {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--intacctReceive {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--interop {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--reporting {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--routing {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--workflow {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
	x3-connector--workflow-notifications {
		defaultVisibilityTimeout = 30 seconds
		delay = 0 seconds
		receiveMessageWait = 0 seconds
		fifo = true
		deadLetterQueue {
			name = dev-dead-letter-queue
			maxReceiveCount = 5
		}
	}
}
