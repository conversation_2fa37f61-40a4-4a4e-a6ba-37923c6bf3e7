{
    "atp-step-time-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-time-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} time field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"${4}\" in the time field",
            "And the user clicks the \"${5|AM,PM|}\" toggle button of the time field",
            "Then the value of the time field is \"${6}\"",
        ],
        "description": "Select a time field, write in it, click the AM/PM toggle, and verify the value",
    },
    "atp-step-time-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-time-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} time field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the time field is ${4|enabled,disabled|}",
            "Then the value of the time field is \"${5}\"",
        ],
        "description": "Select a time field, verify if it's enabled/disabled, and check its value",
    },
    "atp-step-time-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-time-state-read-only-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} time field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the time field is read-only",
            "Then the value of the time field is \"${4}\"",
        ],
        "description": "Select a time field, verify if it's read-only, and check its value",
    },
    "atp-step-time-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-time-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} time field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a time field is displayed or hidden",
    },
}
