{
    "atp-step-nested-grid-card-control-a-value": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-control-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "Then the value of the \"${4}\" ${5|bound,labelled|} nested ${6|date,label,numeric,reference,select,text,link|} field of the card ${7} in the nested grid field is \"${8}\"",
        ],
        "description": "Select a nested grid field and control/verify the value of nested fields of cards in the nested grid",
    },
    "atp-step-nested-grid-card-control-header": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-control-header",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "Then the value in the header of the mobile nested grid field is \"${4}\"",
            "And the level in the header of the mobile nested grid field is \"${5}\"",
        ],
        "description": "Select a nested grid field and control/verify header values and navigation in mobile nested grid",
    },
    "atp-step-nested-grid-card-add-new-row": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-add-new-row",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "When the user adds a new row to the nested grid field",
        ],
        "description": "Select a nested grid field and add a new row to the nested grid field",
    },
    "atp-step-nested-grid-card-click-card": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-click-card",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "When the user clicks the card ${4} in the nested grid field",
        ],
        "description": "Select a nested grid field and click on a specific card in the nested grid",
    },
    "atp-step-nested-grid-card-select-main-checkbox": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-select-main-checkbox",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "When the user ${4|ticks,unticks|} the main checkbox of the card ${5} in the nested grid field",
            "And the user ${6|ticks,unticks|} the main checkbox of the card with the text \"${7}\" in the nested grid field",
        ],
        "description": "Select a nested grid field and tick/untick main checkbox of cards by card number or by text content, and verify checkbox state",
    },
    "atp-step-nested-grid-card-dropdown-action-selection": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-dropdown-action-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "When the user clicks the \"${4}\" dropdown action of the card ${5} in the nested grid field",
        ],
        "description": "Select a nested grid field and click on a dropdown action of a specific card",
    },
    "atp-step-nested-grid-card-dropdown-action-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-dropdown-action-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "Then the \"${4}\" dropdown action of the card ${5} in the nested grid field is ${6|enabled,disabled|}",
        ],
        "description": "Select a nested grid field and verify if a dropdown action of a card is enabled or disabled",
    },
    "atp-step-nested-grid-card-control-cards-selection-state": {
        "scope": "feature",
        "prefix": "atp-step-nested-grid-card-control-cards-selection-state",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} nested grid field on ${3|the main page,a modal,a full width modal,the helper panel,the sidebar,the navigation panel|}",
            "Then all the cards in the nested grid field are ${4|selected,unselected|}",
        ],
        "description": "Select a nested grid field and verify if all cards are selected/unselected",
    },
}
