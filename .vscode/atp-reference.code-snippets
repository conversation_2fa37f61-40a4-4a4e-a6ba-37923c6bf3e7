{
    "atp-step-reference-option-selection-after-write": {
        "scope": "feature",
        "prefix": "atp-step-reference-option-selection-after-write",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"$4\" in the reference field",
            "Then at least the following list of options is displayed for the reference field:${5:\"Option 1 | Option 2 | Option 3\"}",
            "And the user selects \"$6\" in the reference field",
            "Then the value of the reference field is \"$7\"",
        ],
        "description": "Steps to select a reference field, write text, select option and verify the value",
    },
    "atp-step-reference-option-selection-via-lookup": {
        "scope": "feature",
        "prefix": "atp-step-reference-option-selection-via-lookup",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks the lookup button of the reference field",
            "And the user selects \"$4\" in the reference field",
            "Then the value of the reference field is \"$4\"",
        ],
        "description": "reference field operations via lookup button: select field, click lookup, select option, and verify value",
    },
    "atp-step-reference-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-reference-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the reference field is ${4|enabled,disabled|}",
            "And the value of the reference field is \"$5\"",
        ],
        "description": "Steps to select a reference field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-reference-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-reference-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the reference field is read-only",
            "And the value of the reference field is \"$4\"",
        ],
        "description": "reference field read-only verification: select field, verify read-only state, and check value",
    },
    "atp-step-reference-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-reference-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} reference field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a reference field is displayed or hidden",
    },
}
