{
    "atp-step-pod-collection-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "When the user writes \"${6}\" in the \"${7}\" ${8|bound,labelled|} nested ${9|reference,select,numeric,text area,text|} field of the selected pod collection item",
            "Then the value of the \"${10}\" ${11|bound,labelled|} nested ${12|multi dropdown,multi reference,reference,select,numeric,progress,text area,label,text|} field of the selected pod collection item is \"${13}\"",
            "When the user clicks the \"${14}\" ${15|bound,labelled|} nested switch field of the selected pod collection item",
            "Then the value of the \"${16}\" ${17|bound,labelled|} nested switch field of the selected pod collection item is set to \"${18}\"",
        ],
        "description": "Select a pod collection field and item, write in nested fields, verify values, and handle switch fields",
    },
    "atp-step-pod-collection-select-multi-dropdown-values": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-select-multi-dropdown-values",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "When the user clicks in the \"${6}\" ${7|bound,labelled|} nested multi dropdown field of the selected pod collection item",
            "Then at least the following list of options is displayed ${8:\"Option 1 | Option 2 | Option 3\"} in the \"${9}\" ${10|bound,labelled|} nested multi dropdown field of the selected pod collection item",
            "When the user selects ${11:\"Option 1 | Option 2\"} in the \"${12}\" ${13|bound,labelled|} nested multi dropdown field of the selected pod collection item",
            "Then the value of the \"${14}\" ${15|bound,labelled|} nested multi dropdown field of the selected pod collection item is ${16:\"Option 1,Option 2\"}",
        ],
        "description": "Select a pod collection field and item, interact with multi dropdown fields, and verify options and values",
    },
    "atp-step-pod-collection-select-multi-reference-values": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-select-multi-reference-values",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "When the user clicks in the \"${6}\" ${7|bound,labelled|} nested multi reference field of the selected pod collection item",
            "Then at least the following list of options is displayed ${8:\"Option 1 | Option 2 | Option 3\"} in the \"${9}\" ${10|bound,labelled|} nested multi reference field of the selected pod collection item",
            "When the user selects ${11:\"Option 1 | Option 2\"} in the \"${12}\" ${13|bound,labelled|} nested multi reference field of the selected pod collection item",
            "Then the value of the \"${14}\" ${15|bound,labelled|} nested multi reference field of the selected pod collection item is ${16:\"Option 1|Option 2\"}",
        ],
        "description": "Select a pod collection field and item, interact with multi reference fields, and verify options and values",
    },
    "atp-step-pod-collection-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "Then the selected pod collection field is ${6|enabled,disabled|}",
            "Then the value of the \"${7}\" ${8|bound,labelled|} nested ${9|multi dropdown,multi reference,reference,select,numeric,progress,text area,label,text|} field of the selected pod collection item is \"${10}\"",
        ],
        "description": "Select a pod collection field and item, verify its state (enabled/disabled), and verify the value of a nested field",
    },
    "atp-step-pod-collection-read-only-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-read-only-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "Then the selected pod collection field is read-only",
            "Then the value of the \"${6}\" ${7|bound,labelled|} nested ${8|multi dropdown,multi reference,reference,select,numeric,progress,text area,label,text|} field of the selected pod collection item is \"${9}\"",
        ],
        "description": "Select a pod collection field and item, verify if it's read-only, and verify the value of a nested field",
    },
    "atp-step-pod-collection-select-main-checkbox": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-select-main-checkbox",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "When the user ${6|selects,unselects|} the main checkbox of the selected pod collection item",
        ],
        "description": "Select a pod collection field and item, select/unselect main checkbox",
    },
    "atp-step-pod-collection-select-nested-checkbox": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-select-nested-checkbox",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "And the user ${7|selects,unselects|} the \"${8}\" ${9|bound,labelled|} nested checkbox field of the selected pod collection item",
            "Then the \"${10}\" ${11|bound,labelled|} nested checkbox field of the selected pod collection item is ${12|selected,unselected|}",
        ],
        "description": "Select a pod collection field and item, select/unselect nested checkbox fields, and verify checkbox states",
    },
    "atp-step-pod-collection-pod-add-remove-pod": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-pod-add-remove-pod",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" button of the selected pod collection field",
            "And the user selects the \"${5}\" icon of the selected pod collection item",
        ],
        "description": "Select a pod collection field, click buttons, and interact with pod collection item icons",
    },
    "atp-step-pod-collection-pod-action": {
        "scope": "feature",
        "prefix": "atp-step-pod-collection-pod-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} pod collection field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "And the user selects the \"${4}\" ${5|id,labelled|} pod collection item of the selected pod collection field",
            "When the user clicks the \"${6}\" action of the selected pod collection item",
        ],
        "description": "Select a pod collection field and item, then click an action on the selected pod collection item",
    },
}
