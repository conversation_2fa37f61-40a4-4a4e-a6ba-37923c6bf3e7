{
    "atp-step-label-control-value": {
        "scope": "feature",
        "prefix": "atp-step-label-control-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} label field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the value of the label field is \"$4\"",
        ],
        "description": "Steps to select a label field and verify its value",
    },
    "atp-step-label-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-label-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} label field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the label field is ${4|enabled,disabled|}",
            "And the value of the label field is \"$5\"",
        ],
        "description": "Steps to select a label field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-label-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-label-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} label field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a label field is displayed or hidden",
    },
}
