{
    "atp-step-dynamic-pod-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user writes \"${4}\" in the \"${5}\" ${6|bound,labelled|} nested ${7|button,calendar,checkbox,count,date,relative date,dynamic-select,icon,label,link,numeric,progress,radio,rich text,scan,text,text area|} field of the dynamic-pod field",
            "Then the value of the \"${8}\" ${9|bound,labelled|} nested ${10|button,calendar,checkbox,count,date,dynamic-select,icon,label,link,numeric,progress,radio,rich text,scan,text,text area|} field in the dynamic-pod field is \"${11}\"",
            "When the user clicks the \"${12}\" ${13|bound,labelled|} nested switch field of the dynamic-pod field",
            "Then the \"${14}\" ${15|bound,labelled|} nested switch field in the dynamic-pod field is set to \"${16}\"",
        ],
        "description": "Select a dynamic pod field, write in nested fields, verify values, and handle switch fields",
    },
    "atp-step-dynamic-pod-write-and-select-a-value": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-write-and-select-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user writes \"${4}\" in the \"${5}\" ${6|bound,labelled|} nested ${7|filter select,reference,select|} field of the dynamic-pod field",
            "Then at least the following list of options is displayed \"${8}\" in the \"${9}\" ${10|bound,labelled|} nested ${11|reference,select|} field of the dynamic-pod field",
            "When the user selects \"${12}\" in the \"${13}\" ${14|bound,labelled|} nested ${15|filter select,reference,select|} field of the dynamic-pod field",
            "Then the value of the \"${16}\" ${17|bound,labelled|} nested ${18|filter select,reference,select|} field in the dynamic-pod field is \"${19}\"",
        ],
        "description": "Select a dynamic pod field, write in filter select/reference/select fields, verify options, and verify selected values",
    },
    "atp-step-dynamic-pod-dynamic-select-value-method1": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-dynamic-select-value-method1",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" action button of nested \"${5}\" ${6|bound,labelled|} dynamic-select field of the dynamic-pod field",
            "Then at least the following list of options is displayed ${7:\"Option 1 | Option 2 | Option 3\"} in the \"${8}\" ${9|bound,labelled|} nested dynamic-select field of the dynamic-pod field",
            "And the user selects \"${10}\" in the \"${11}\" ${12|bound,labelled|} nested dynamic-select field of the dynamic-pod field",
            "Then the value of the \"${13}\" ${14|bound,labelled|} nested dynamic-select field in the dynamic-pod field is \"${15}\"",
        ],
        "description": "Select a dynamic pod field, click dynamic-select action button, select value, and verify the selection",
    },
    "atp-step-dynamic-pod-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the dynamic-pod field is ${4|enabled,disabled|}",
            "Then the value of the \"${5}\" ${6|bound,labelled|} nested ${7|button,calendar,checkbox,count,date,filter select,icon,label,link,multi dropdown,multi reference,numeric,progress,radio,reference,rich text,scan,select,dynamic-select,text,text area|} field in the dynamic-pod field is \"${8}\"",
        ],
        "description": "Select a dynamic pod field, verify its state (enabled/disabled), and verify the value of a nested field",
    },
    "atp-step-dynamic-pod-read-only-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-read-only-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the dynamic-pod field is read-only",
            "Then the value of the \"${4}\" ${5|bound,labelled|} nested ${6|button,calendar,checkbox,count,date,filter select,icon,label,link,multi dropdown,multi reference,numeric,progress,radio,reference,rich text,scan,select,dynamic-select,text,text area|} field in the dynamic-pod field is \"${7}\"",
        ],
        "description": "Select a dynamic pod field, verify if it's read-only, and verify the value of a nested field",
    },
    "atp-step-dynamic-pod-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-state-visibility-verification",
        "body": [
            "Then the \"${1}\" ${2|bound,labelled|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Verify if a dynamic pod field is displayed or hidden on a specific location",
    },
    "atp-step-dynamic-pod-add-pod": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-add-pod",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" button of the dynamic-pod field",
        ],
        "description": "Select a dynamic pod field and click a button for adding dynamic pod items",
    },
    "atp-step-dynamic-pod-action-state-empty-verification": {
        "scope": "feature",
        "prefix": "atp-step-dynamic-pod-action-state-empty-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} dynamic-pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the dynamic-pod field is ${4|empty,not empty|}",
            "Then the dynamic-pod field header container value is \"${5}\"",
        ],
        "description": "Select a dynamic pod field and verify empty state and header container value",
    },
}
