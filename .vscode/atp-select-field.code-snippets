{
    "atp-step-select-option-selection": {
        "scope": "feature",
        "prefix": "atp-step-select-option-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user clicks in the select field",
            "Then at least the following list of options is displayed for the select field:${4:\"Option 1 | Option 2 | Option 3\"}",
            "And the user selects \"${5}\" in the select field",
            "Then the value of the select field is \"${6}\"",
        ],
        "description": "Select a field, click it, select an option, and verify the value",
    },
    "atp-step-select-option-selection-after-write": {
        "scope": "feature",
        "prefix": "atp-step-select-option-selection-after-write",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"${4}\" in the select field",
            "Then at least the following list of options is displayed for the select field:${5:\"Option 1 | Option 2 | Option 3\"}",
            "And the user selects \"${6}\" in the select field",
            "Then the value of the select field is \"${7}\"",
        ],
        "description": "Select a field, write in it, select an option, and verify the value",
    },
    "atp-step-select-state-verificaton": {
        "scope": "feature",
        "prefix": "atp-step-select-state-verificaton",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the select field is ${4|enabled,disabled|}",
            "Then the value of the select field is \"${5}\"",
        ],
        "description": "Select a field, verify if it's enabled/disabled, and check its value",
    },
    "atp-step-select-state-read-only-verificaton": {
        "scope": "feature",
        "prefix": "atp-step-select-state-read-only-verificaton",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} select field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the select field is read-only",
            "Then the value of the select field is \"${4}\"",
        ],
        "description": "Select a field, verify if it's read-only, and check its value",
    },
    "atp-step-select-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-select-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} select field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a select field is displayed or hidden",
    },
}
