{
    "atp-step-progress-control-value": {
        "scope": "feature",
        "prefix": "atp-step-progress-control-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} progress field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the value of the progress field is \"${4} %\"",
        ],
        "description": "Select a progress field and verify its value",
    },
    "atp-step-progress-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-progress-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} progress field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the progress field is ${4|enabled,disabled|}",
            "Then the value of the progress field is \"${5} %\"",
        ],
        "description": "Verify the state and value of a progress field",
    },
    "atp-step-progress-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-progress-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} progress field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a progress field is displayed or hidden",
    },
}
