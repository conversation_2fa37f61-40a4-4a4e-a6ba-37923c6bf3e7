{
    "atp-step-vital-pod-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user writes \"${4}\" in the \"${5}\" ${6|bound,labelled|} nested ${7|button,calendar,checkbox,count,date,relative date,icon,label,link,numeric,progress,radio,rich text,scan,text,text area|} field of the vital pod field",
            "Then the value of the \"${8}\" ${9|bound,labelled|} nested ${10|button,calendar,checkbox,count,date,icon,label,link,numeric,progress,radio,rich text,scan,text,text area|} field in the vital pod field is \"${4}\"",
            "When the user clicks the \"${11}\" ${12|bound,labelled|} nested switch field of the vital pod field",
            "Then the \"${13}\" ${14|bound,labelled|} nested switch field in the vital pod field is set to \"${15}\"",
        ],
        "description": "Select a vital pod field, write in nested fields, verify values, and handle switch fields",
    },
    "atp-step-vital-pod-select-multi-dropdown-values": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-select-multi-dropdown-values",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks in the \"${4}\" ${5|bound,labelled|} nested multi dropdown field of the vital pod field",
            "Then at least the following list of options is displayed ${6:\"Option 1 | Option 2 | Option 3\"} in the \"${7}\" ${8|bound,labelled|} nested multi dropdown field of the vital pod field",
            "When the user selects ${9:\"Option 1 | Option 2\"} in the \"${10}\" ${11|bound,labelled|} nested multi dropdown field of the vital pod field",
            "Then the value of the \"${12}\" ${13|bound,labelled|} nested multi dropdown field in the vital pod field is ${14:\"Option 1,Option 2\"}",
        ],
        "description": "Select a vital pod field, interact with multi dropdown fields, and verify options and values",
    },
    "atp-step-vital-pod-select-multi-reference-values": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-select-multi-reference-values",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks in the \"${4}\" ${5|bound,labelled|} nested multi reference field of the vital pod field",
            "Then at least the following list of options is displayed ${6:\"Option 1 | Option 2 | Option 3\"} in the \"${7}\" ${8|bound,labelled|} nested multi reference field of the vital pod field",
            "When the user selects ${9:\"Option 1 | Option 2\"} in the \"${10}\" ${11|bound,labelled|} nested multi reference field of the vital pod field",
            "Then the value of the \"${12}\" ${13|bound,labelled|} nested multi reference field in the vital pod field is ${14:\"Option 1|Option 2\"}",
        ],
        "description": "Select a vital pod field, interact with multi reference fields, and verify options and values",
    },
    "atp-step-vital-pod-write-and-select-a-value": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-write-and-select-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user writes \"${4}\" in the \"${5}\" ${6|bound,labelled|} nested ${7|filter select,reference,select|} field of the vital pod field",
            "Then at least the following list of options is displayed \"${8}\" in the \"${9}\" ${10|bound,labelled|} nested ${11|reference,select|} field of the vital pod field",
            "When the user selects \"${12}\" in the \"${13}\" ${14|bound,labelled|} nested ${15|filter select,reference,select|} field of the vital pod field",
            "Then the value of the \"${16}\" ${17|bound,labelled|} nested ${18|filter select,reference,select|} field in the vital pod field is \"${19}\"",
        ],
        "description": "Select a vital pod field, write in filter select/reference/select fields, verify options, and verify selected values",
    },
    "atp-step-vital-pod-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the vital pod field is ${4|enabled,disabled|}",
            "Then the value of the \"${5}\" ${6|bound,labelled|} nested ${7|button,calendar,checkbox,count,date,filter select,icon,label,link,multi dropdown,multi reference,numeric,progress,radio,reference,rich text,scan,select,dynamic-select,text,text area|} field in the vital pod field is \"${8}\"",
        ],
        "description": "Select a vital pod field, verify its state (enabled/disabled), and verify the value of a nested field",
    },
    "atp-step-vital-pod-read-only-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-read-only-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the vital pod field is read-only",
        ],
        "description": "Select a vital pod field and verify if it's read-only",
    },
    "atp-step-vital-pod-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-state-visibility-verification",
        "body": [
            "Then the \"${1}\" ${2|bound,labelled|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Verify if a vital pod field is displayed or hidden on a specific location",
    },
    "atp-step-vital-pod-add-remove-pod": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-add-remove-pod",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" button of the vital pod field",
            "And the user clicks the \"${5}\" icon of the vital pod field",
        ],
        "description": "Select a vital pod field and perform button/icon click actions for adding or removing vital pod items",
    },
    "atp-step-vital-pod-action": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "When the user clicks the \"${4}\" action of the vital pod field",
        ],
        "description": "Select a vital pod field and click an action",
    },
    "atp-step-vital-pod-action-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-action-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the action \"${4}\" of the vital pod field is ${5|enabled,disabled|}",
        ],
        "description": "Select a vital pod field and verify if an action is enabled or disabled",
    },
    "atp-step-vital-pod-action-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-action-state-visibility-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the action \"${4}\" of the vital pod field is ${5|displayed,hidden|}",
        ],
        "description": "Select a vital pod field and verify if an action is displayed or hidden",
    },
    "atp-step-vital-pod-action-state-empty-verification": {
        "scope": "feature",
        "prefix": "atp-step-vital-pod-action-state-empty-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} vital pod field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|}",
            "Then the vital pod field is ${4|empty,not empty|}",
            "Then the vital pod field header container value is \"${5}\"",
        ],
        "description": "Select a vital pod field and verify empty state and header container value",
    },
}
