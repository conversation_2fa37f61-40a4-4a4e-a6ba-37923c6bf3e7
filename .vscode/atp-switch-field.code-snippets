{
    "atp-step-switch-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-switch-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} switch field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user turns the switch field \"OFF\"",
            "Then the switch field is set to \"OFF\"",
            "When the user turns the switch field \"ON\"",
            "Then the switch field is set to \"ON\"",
        ],
        "description": "Select a switch field, turn it off, verify it's off, turn it on, verify it's on",
    },
    "atp-step-switch-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-switch-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} switch field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the switch field is ${4|enabled,disabled|}",
            "Then the switch field is set to \"${5}\"",
        ],
        "description": "Select a switch field, verify if it's enabled/disabled, and check its value",
    },
    "atp-step-switch-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-switch-state-read-only-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} switch field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the switch field is read-only",
            "Then the switch field is set to \"${4}\"",
        ],
        "description": "Select a switch field, verify if it's read-only, and check its value",
    },
    "atp-step-switch-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-switch-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} switch field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a switch field is displayed or hidden",
    },
}
