{
    "atp-step-date-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-date-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"$4\" in the date field",
            "Then the value of the date field is \"$5\"",
        ],
        "description": "Steps to select a date field, write a date, verify value, and clear",
    },
    "atp-step-date-set-a-generated-value": {
        "scope": "feature",
        "prefix": "atp-step-date-set-a-generated-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes a generated date in the date field with value \"$4\"",
            "Then the value of the date field is a generated date with value \"$5\"",
            "And the user writes a generated date with value \"$6\" from today to the selected date field",
            "Then the value of the date field is a generated date from today with value \"$7\"",
        ],
        "description": "Steps to select a date field, write a generated date, verify generated value, and clear",
    },
    "atp-step-date-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes a generated date in the date field with value \"$4\"",
            "Then the date equal to \"$5\" is ${6|selected,not selected|}",
            "And the date equal to \"$7\" is ${8|enabled,disabled|}",
            "Then the value of the date field is a generated date with value \"$9\"",
        ],
        "description": "Steps to select a date field, write a date, verify date selection and enabled/disabled states",
    },
    "atp-step-date-range-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-range-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes a generated date in the date field with value \"${4:MM/DD/Y}\"",
            "Then the dates from \"${5:MM/DD/Y}\" to \"${6:MM/DD/Y}\" are ${7:enabled}",
            "And the dates from \"${8:MM/DD/Y}\" to \"${9:MM/DD/Y}\" are ${10:disabled}",
            "And the dates from \"${11:MM/DD/Y}\" to \"${12:MM/DD/Y}\" are ${13:out of period}",
        ],
        "description": "Date field range verification: verify ranges of dates are enabled, disabled, or out of period",
    },
    "atp-step-date-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} date field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the date field is read-only",
            "And the value of the date field is \"${4:MM/DD/YYYY}\"",
        ],
        "description": "Date field read-only verification: verify field is read-only and check its value",
    },
    "atp-step-date-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-date-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} date field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a date field is displayed or hidden",
    },
}
