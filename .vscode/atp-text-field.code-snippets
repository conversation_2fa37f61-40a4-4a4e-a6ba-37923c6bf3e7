{
    "atp-step-text-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-text-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"${4}\" in the text field",
            "Then the value of the text field is \"${5}\"",
        ],
        "description": "Select a text field, write in it, and verify the value",
    },
    "atp-step-text-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-text-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the text field is ${4|enabled,disabled|}",
            "Then the value of the text field is \"${5}\"",
        ],
        "description": "Select a text field, verify if it's enabled/disabled, and check its value",
    },
    "atp-step-text-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-text-state-read-only-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} text field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the text field is read-only",
            "Then the value of the text field is \"${4}\"",
        ],
        "description": "Select a text field, verify if it's read-only, and check its value",
    },
    "atp-step-text-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-text-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} text field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a text field is displayed or hidden",
    },
}
