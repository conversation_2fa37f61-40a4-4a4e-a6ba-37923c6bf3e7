{
    "atp-step-multi-reference-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-multi-reference-set-a-value",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "When the user writes \"$4\" in the multi reference field",
            "Then at least the following list of options is displayed for the multi reference field: \"option1 | option2 | option3\"",
            "And the user selects ${5:\"Option 1 | Option 2\"} in the multi reference field",
            "Then the value of the multi reference field is ${6:\"Option 1|Option 2\"}",
        ],
        "description": "Steps to select a multi reference field, write search text, verify options, select options and verify the value",
    },
    "atp-step-multi-reference-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-multi-reference-state-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the multi reference field is ${4|enabled,disabled|}",
            "And the value of the multi reference field is \"$5\"",
        ],
        "description": "Steps to select a multi reference field, verify if it's enabled or disabled, and verify its value",
    },
    "atp-step-multi-reference-state-read-only-verification": {
        "scope": "feature",
        "prefix": "atp-step-multi-reference-state-read-only-verification",
        "body": [
            "Given the user selects the \"$1\" ${2|labelled,bound|} multi reference field on ${3|the main page,a modal,a full width modal,the sidebar,the mobile sidebar,the navigation panel,the detail panel|}",
            "Then the multi reference field is read-only",
            "And the value of the multi reference field is \"$4\"",
        ],
        "description": "Steps to select a multi reference field, verify if it's read-only, and verify its value",
    },
    "atp-step-multi-reference-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-multi-reference-state-visibility-verification",
        "body": [
            "Then the \"$1\" ${2|bound,labelled|} multi reference field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar|} is ${4|displayed,hidden|}",
        ],
        "description": "Step to verify if a multi reference field is displayed or hidden",
    },
}
