{
    "atp-step-table-card-control-a-value": {
        "scope": "feature",
        "prefix": "atp-step-table-card-control-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "Then the value of the \"${4}\" ${5|bound,labelled|} nested ${6|date,label,numeric,reference,select,text,link|} field of the card ${7} in the table field is \"${8}\"",
        ],
        "description": "Select a table field and control/verify the value of nested fields of cards in the table field",
    },
    "atp-step-table-card-add-new-row": {
        "scope": "feature",
        "prefix": "atp-step-table-card-add-new-row",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "When the user adds a new row to the mobile table",
        ],
        "description": "Select a table field and add a new row to the mobile table",
    },
    "atp-step-table-card-click-card": {
        "scope": "feature",
        "prefix": "atp-step-table-card-click-card",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "When the user clicks the card ${4} in the table field",
        ],
        "description": "Select a table field and click on a specific card in the table field",
    },
    "atp-step-table-card-select-main-checkbox": {
        "scope": "feature",
        "prefix": "atp-step-table-card-select-main-checkbox",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "When the user ${4|ticks,unticks|} the main checkbox of the card ${5} in the table field",
            "And the user ${6|ticks,unticks|} the main checkbox of the card with the text \"${7}\" in the table field",
        ],
        "description": "Select a table field and tick/untick main checkbox of cards by card number or by text content",
    },
    "atp-step-table-card-dropdown-action-selection": {
        "scope": "feature",
        "prefix": "atp-step-table-card-dropdown-action-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "When the user clicks the \"${4}\" dropdown action of the card ${5} in the table field",
        ],
        "description": "Select a table field and click on a dropdown action of a specific card",
    },
    "atp-step-table-card-dropdown-action-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-table-card-dropdown-action-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "Then the \"${4}\" dropdown action of the card ${5} in the table field is ${6|enabled,disabled|}",
        ],
        "description": "Select a table field and verify if a dropdown action of a card is enabled or disabled",
    },
    "atp-step-table-card-inline-action": {
        "scope": "feature",
        "prefix": "atp-step-table-card-inline-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "When the user clicks the \"${4}\" inline action button of the card ${5} in the table field",
        ],
        "description": "Select a table field and click on an inline action button of a specific card",
    },
    "atp-step-table-card-inline-action-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-table-card-inline-action-state-visibility-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "Then the \"${4}\" inline action button of the card ${5} in the table field is ${6|displayed,hidden|}",
        ],
        "description": "Select a table field and verify if an inline action button of a card is displayed or hidden",
    },
    "atp-step-table-card-control-cards-selection-state": {
        "scope": "feature",
        "prefix": "atp-step-table-card-control-cards-selection-state",
        "body": [
            "Given the user selects the \"${1}\" ${2|bound,labelled|} table field on ${3|the main page,a modal,a full width modal,the detail panel,the sidebar,the navigation panel|}",
            "Then all the cards in the table field are ${4|selected,unselected|}",
        ],
        "description": "Select a table field and verify if all cards are selected or unselected",
    },
}
