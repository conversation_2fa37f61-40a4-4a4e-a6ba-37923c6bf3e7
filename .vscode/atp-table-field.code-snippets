{
    "atp-step-table-row-set-a-value": {
        "scope": "feature",
        "prefix": "atp-step-table-row-set-a-value",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user writes \"${6}\" in the \"${7}\" ${8|bound,labelled|} nested ${9|date,dropdown-list,numeric,reference,select,text,filter select|} field of the selected row in the table field",
            "Then the value of the \"${10}\" ${11|bound,labelled|} nested ${12|aggregate,checkbox,date,relative date,dropdown-list,filter select,icon,image,label,link,numeric,progress,reference,select,text|} field of the selected row in the table field is \"${13}\"",
        ],
        "description": "Snippet for selecting a table, row and setting/verifying a value",
    },
    "atp-step-table-row-set-a-generic-date": {
        "scope": "feature",
        "prefix": "atp-step-table-row-set-a-generic-date",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user writes a generated date with value \"${6}\" in the \"${7}\" ${8|bound,labelled|} nested date field of the selected row in the table field",
            "Then the value of the \"${9}\" ${10|bound,labelled|} nested date field of the selected row in the table field is a generated date with value \"${11}\"",
            "And the user writes a generated date with value \"${12}\" from today in the \"${13}\" ${14|bound,labelled|} nested date field of the selected row in the table field",
            "Then the value of the \"${15}\" ${16|bound,labelled|} nested date field of the selected row in the table field is a generated date from today with value \"${17}\"",
        ],
        "description": "Snippet for selecting a table, row and setting/verifying a generic date",
    },
    "atp-step-table-row-set-a-date-time-range": {
        "scope": "feature",
        "prefix": "atp-step-table-row-set-a-date-time-range",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user selects the \"${6}\" ${7|bound,labelled|} nested date-time-range field of the selected row in the table field",
            "And the user selects the \"${8}\" month of start nested date-time-range field of the selected row in the table field",
            "And the user selects the \"${9}\" year of start nested date-time-range field of the selected row in the table field",
            "And the user selects the \"${10}\" day of start nested date-time-range field of the selected row in the table field",
            "And the user writes \"${11}\" in time field of the start nested date-time-range field of the selected row in the table field",
            "And the user clicks the \"${12|AM,PM|}\" toggle button of the start nested date-time-range field of the selected row in the table field",
            "Then the value of the start nested date-time-range field of the selected row in the table field is \"${13}\"",
            "And the user selects the \"${14}\" month of end nested date-time-range field of the selected row in the table field",
            "And the user selects the \"${15}\" year of end nested date-time-range field of the selected row in the table field",
            "And the user selects the \"${16}\" day of end nested date-time-range field of the selected row in the table field",
            "And the user writes \"${17}\" in time field of the end nested date-time-range field of the selected row in the table field",
            "And the user clicks the \"${18|AM,PM|}\" toggle button of the end nested date-time-range field of the selected row in the table field",
            "Then the value of the end nested date-time-range field of the selected row in the table field is \"${19}\"",
        ],
        "description": "Snippet for selecting a table, row and setting/verifying a date time range",
    },
    "atp-step-table-column-filter": {
        "scope": "feature",
        "prefix": "atp-step-table-column-filter",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user filters the \"${4}\" ${5|bound,labelled|} column in the table field with value \"${6}\"",
            "And the user filters the \"${7:Column name}\" ${8|bound,labelled|} column in the table field with filter \"${9:Filter}\" and value \"${10:Value}\"",
            "And the user filters the \"${11:Column name}\" ${12|bound,labelled|} column in the table field with filter type \"${13:Filter type}\"",
        ],
        "description": "Snippet for filtering columns in a table",
    },
    "atp-step-table-column-filter-multiple-values": {
        "scope": "feature",
        "prefix": "atp-step-table-column-filter-multiple-values",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user opens the filter of the \"${4}\" ${5|bound,labelled|} column in the table field",
            "Then the clear selected items link in the filter menu of the \"${4}\" ${5|bound,labelled|} column of the table field is ${6|disabled,enabled|}",
            "And the user searches \"${7}\" in the filter of the table field",
            "And the search value of the filter in the table field is \"${8}\"",
            "And the user ${9|ticks,unticks|} the item with text \"${10}\" in the filter of the table field",
            "Then the clear selected items link in the filter menu of the \"${4}\" ${5|bound,labelled|} column of the table field is ${11|disabled,enabled|}",
            "And the user closes the filter of the \"${4}\" ${5|bound,labelled|} column in the table field",
        ],
        "description": "Snippet for filtering columns with multiple values in a table",
    },
    "atp-step-table-column-clear-the-filters": {
        "scope": "feature",
        "prefix": "atp-step-table-column-clear-the-filters",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user opens the filter of the \"${4}\" ${5|bound,labelled|} column in the table field",
            "And the user clicks the clear selected items link in the filter menu of the \"${4}\" ${5|bound,labelled|} column of the table field",
            "And the user closes the filter of the \"${4}\" ${5|bound,labelled|} column in the table field",
        ],
        "description": "Snippet for clearing filters in a table column",
    },
    "atp-step-table-column-click": {
        "scope": "feature",
        "prefix": "atp-step-table-column-click",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "Then the \"${4}\" ${5|labelled,bound|} column in the table field is ${6|displayed,hidden|}",
            "And the user clicks the \"${7}\" ${8|labelled,bound|} column of the table field",
        ],
        "description": "Snippet for checking column visibility and clicking a column in a table",
    },
    "atp-step-table-column-group-ungroup": {
        "scope": "feature",
        "prefix": "atp-step-table-column-group-ungroup",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user clicks the ${4|group by,ungroup|} option in the header menu of the \"${5}\" ${6|bound,labelled|} column of the table field",
        ],
        "description": "Snippet for grouping and ungrouping columns in a table",
    },
    "atp-step-table-column-set-settings": {
        "scope": "feature",
        "prefix": "atp-step-table-column-set-settings",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "When the user clicks the \"Open column panel\" labelled button of the table field",
            "Then the \"Column settings\" titled sidebar is displayed",
            "When searches for \"${4}\" in the lookup dialog on the sidebar",
            "And the user ${5|ticks,unticks|} the table column configuration with \"${6}\" name on the sidebar",
            "Then the table column configuration with name \"${7}\" on the sidebar is ${8|ticked,unticked|}",
            "And the table column configuration with name \"${9}\" on the sidebar is ${10|locked,unlocked|}",
            "Then the table column configuration on the sidebar are displayed in the following order \"${11}\"",
        ],
        "description": "Snippet for setting column configuration in a table",
    },
    "atp-step-table-row-add-a-floating-row": {
        "scope": "feature",
        "prefix": "atp-step-table-row-add-a-floating-row",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user adds a new table row to the table field",
        ],
        "description": "Snippet for adding a floating row to a table",
    },
    "atp-step-table-row-add-a-row-with-sidebar": {
        "scope": "feature",
        "prefix": "atp-step-table-row-add-a-row-with-sidebar",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user adds a new table row to the table field using the sidebar",
        ],
        "description": "Snippet for adding a row to a table using the sidebar",
    },
    "atp-step-table-row-custom-add-line": {
        "scope": "feature",
        "prefix": "atp-step-table-row-custom-add-line",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user clicks the \"${4}\" ${5|bound,labelled|} add action of the table field",
        ],
        "description": "Snippet for adding a custom line to a table",
    },
    "atp-step-table-row-select-main-checkbox": {
        "scope": "feature",
        "prefix": "atp-step-table-row-select-main-checkbox",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user ${6|ticks,unticks|} the main checkbox of the selected row in the table field",
        ],
        "description": "Snippet for selecting main checkbox in a table row",
    },
    "atp-step-table-row-cell-click": {
        "scope": "feature",
        "prefix": "atp-step-table-row-cell-click",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user clicks the \"${5}\" ${6|labelled,bound|} nested field of the selected row in the table field",
        ],
        "description": "Snippet for selecting main checkbox in a table row",
    },
    "atp-step-table-row-select-all": {
        "scope": "feature",
        "prefix": "atp-step-table-row-select-all",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects all rows of the table field",
        ],
        "description": "Snippet for selecting all rows in a table",
    },
    "atp-step-table-row-expand-collapse": {
        "scope": "feature",
        "prefix": "atp-step-table-row-expand-collapse",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user ${6|expands,collapses|} the selected row of the table field",
        ],
        "description": "Snippet for expanding or collapsing a row in a table",
    },
    "atp-step-table-row-dropdown-action-selection": {
        "scope": "feature",
        "prefix": "atp-step-table-row-dropdown-action-selection",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user clicks the \"${6}\" dropdown action of the selected row of the table field",
        ],
        "description": "Snippet for clicking a dropdown action in a table row",
    },
    "atp-step-table-row-dropdown-action-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-table-row-dropdown-action-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "Then the \"${6}\" dropdown action of the selected row in the table field is ${7|enabled,disabled|}",
        ],
        "description": "Snippet for verifying the enabled/disabled state of a dropdown action in a table row",
    },
    "atp-step-table-row-dropdown-action-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-table-row-dropdown-action-state-visibility-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "Then the \"${6}\" dropdown action of the selected row in the table field is ${7|displayed,hidden|}",
        ],
        "description": "Snippet for verifying the visibility of a dropdown action in a table row",
    },
    "atp-step-table-row-inline-action": {
        "scope": "feature",
        "prefix": "atp-step-table-row-inline-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "And the user hovers over the \"${6}\" inline action button of the selected row in the table field",
            "Then the value of the inline action tooltip in the table field is \"${7}\"",
            "And the user clicks the \"${8}\" inline action button of the selected row in the table field",
        ],
        "description": "Snippet for interacting with inline actions in a table row",
    },
    "atp-step-table-control-error": {
        "scope": "feature",
        "prefix": "atp-step-table-control-error",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user hovers the validation error icon of the table field",
            "Then the table has \"${4}\" ${5|errors,no error|}",
            "And the table contains errors",
            "And the user closes the global validation error panel of the table field",
        ],
        "description": "Snippet for controlling errors in a table",
    },
    "atp-step-table-row-control-error": {
        "scope": "feature",
        "prefix": "atp-step-table-row-control-error",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user selects the ${4|row,floating row|} ${5} of the table field",
            "Then the \"${6}\" ${7|labelled,bound|} nested ${8|aggregate,checkbox,date,relative date,dropdown-list,filter select,icon,image,label,link,numeric,progress,reference,select,text|} field of the selected row in the table field contains ${9|errors,no error|}",
            "And the user clicks the main validation error icon of the selected row in the table field",
            "Then the validation error tooltip containing text \"${10}\" in the table field is displayed",
        ],
        "description": "Snippet for controlling errors in a table row",
    },
    "atp-step-table-column-control-error": {
        "scope": "feature",
        "prefix": "atp-step-table-column-control-error",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "Then the \"${4}\" ${5|labelled,bound|} column in the table field contains ${6|errors,no error|}",
        ],
        "description": "Snippet for controlling column errors in a table",
    },
    "atp-step-table-business-action": {
        "scope": "feature",
        "prefix": "atp-step-table-business-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user clicks the \"${4}\" ${5|bound,labelled|} business action button of the table field",
        ],
        "description": "Snippet for clicking business action in a table",
    },
    "atp-step-table-multi-action": {
        "scope": "feature",
        "prefix": "atp-step-table-multi-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user clicks the \"${4}\" ${5|bound,labelled|} multi action of the table field",
        ],
        "description": "Snippet for clicking multi action in a table",
    },
    "atp-step-table-option-menu": {
        "scope": "feature",
        "prefix": "atp-step-table-option-menu",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "Then the option menu of the table field is ${4|displayed,hidden|}",
            "And the user clicks the option menu of the table field",
            "And the user clicks the \"${5}\" value in the option menu of the table field",
            "Then the value of the option menu of the table field is \"${6}\"",
        ],
        "description": "Snippet for interacting with option menu in a table",
    },
    "atp-step-table-header-action": {
        "scope": "feature",
        "prefix": "atp-step-table-header-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "And the user clicks the \"${4}\" ${5|bound,labelled|} header action button of the table field",
            "And the user clicks the \"${6}\" ${7|bound,labelled|} button of the table field",
        ],
        "description": "Snippet for clicking header actions in a table",
    },
    "atp-step-table-header-action-state-verification": {
        "scope": "feature",
        "prefix": "atp-step-table-header-action-state-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "Then the \"${4}\" ${5|bound,labelled|} header action button of the table field is ${6|enabled,disabled|}",
            "And the \"${7}\" ${8|bound,labelled|} button of the table field is ${9|enabled,disabled|}",
        ],
        "description": "Snippet for verifying header action state in a table",
    },
    "atp-step-table-header-action-state-visibility-verification": {
        "scope": "feature",
        "prefix": "atp-step-table-header-action-state-visibility-verification",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "Then the \"${4}\" ${5|bound,labelled|} header action button of the table field is ${6|displayed,hidden|}",
            "And the \"${7}\" ${8|bound,labelled|} button of the table field is ${9|displayed,hidden|}",
        ],
        "description": "Snippet for verifying header action visibility in a table",
    },
    "atp-step-table-bulk-action": {
        "scope": "feature",
        "prefix": "atp-step-table-bulk-action",
        "body": [
            "Given the user selects the \"${1}\" ${2|labelled,bound|} table field on ${3|the main page,a modal,a full width modal,the sidebar,the navigation panel|}",
            "Then the bulk action bar of the table field is ${4|displayed,hidden|}",
            "And the user clicks the \"${5}\" ${6|bound,labelled|} bulk action button of the table field",
            "And the bulk action bar of the table field has ${7} items",
        ],
        "description": "Snippet for interacting with bulk actions in a table",
    },
}
