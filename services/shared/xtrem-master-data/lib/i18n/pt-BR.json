{"@sage/xtrem-master-data/activity__allergen__name": "", "@sage/xtrem-master-data/activity__bom_revision_sequence__name": "", "@sage/xtrem-master-data/activity__business_entity__name": "", "@sage/xtrem-master-data/activity__capability_level__name": "", "@sage/xtrem-master-data/activity__container__name": "", "@sage/xtrem-master-data/activity__cost_category__name": "", "@sage/xtrem-master-data/activity__currency__name": "", "@sage/xtrem-master-data/activity__customer__name": "", "@sage/xtrem-master-data/activity__customer_price_reason__name": "", "@sage/xtrem-master-data/activity__customer_supplier_category__name": "", "@sage/xtrem-master-data/activity__daily_shift__name": "", "@sage/xtrem-master-data/activity__delivery_mode__name": "", "@sage/xtrem-master-data/activity__employee__name": "", "@sage/xtrem-master-data/activity__ghs_classification__name": "", "@sage/xtrem-master-data/activity__group_resource__name": "", "@sage/xtrem-master-data/activity__incoterm__name": "", "@sage/xtrem-master-data/activity__indirect_cost_origin__name": "", "@sage/xtrem-master-data/activity__indirect_cost_section__name": "", "@sage/xtrem-master-data/activity__item__name": "", "@sage/xtrem-master-data/activity__item_category__name": "", "@sage/xtrem-master-data/activity__item_site__name": "", "@sage/xtrem-master-data/activity__labour_resource__name": "", "@sage/xtrem-master-data/activity__license_plate_number__name": "", "@sage/xtrem-master-data/activity__location__name": "", "@sage/xtrem-master-data/activity__location_sequence__name": "", "@sage/xtrem-master-data/activity__location_type__name": "", "@sage/xtrem-master-data/activity__location_zone__name": "", "@sage/xtrem-master-data/activity__machine_resource__name": "", "@sage/xtrem-master-data/activity__payment_term__name": "", "@sage/xtrem-master-data/activity__reason_code__name": "", "@sage/xtrem-master-data/activity__sequence_number__name": "", "@sage/xtrem-master-data/activity__sequence_number_assignment__name": "", "@sage/xtrem-master-data/activity__shift_detail__name": "", "@sage/xtrem-master-data/activity__standard__name": "", "@sage/xtrem-master-data/activity__supplier__name": "", "@sage/xtrem-master-data/activity__supplier_certificate__name": "", "@sage/xtrem-master-data/activity__tool_resource__name": "", "@sage/xtrem-master-data/activity__unit_of_measure__name": "", "@sage/xtrem-master-data/activity__weekly_shift__name": "", "@sage/xtrem-master-data/business_entity_address_node_only_one_primary_contact": "", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological-control-must-be-active": "", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-cannot-be-later-than-today": "", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-earlier-than-previous-document-date": "", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-later-than-next-document-date": "", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-component-type-value": "", "@sage/xtrem-master-data/classes__sequence-number-generator__monthly-sequence-numbers-not-allowed": "", "@sage/xtrem-master-data/classes__sequence-number-generator__no-sequence-number-assigned": "", "@sage/xtrem-master-data/classes__sequence-number-generator__node-instance-is-required": "", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-at-application-level-definition-and-no-input-value-for-site": "", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-defined-at-application-level": "", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-instance-not-found": "", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-not-defined": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_continue": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_message": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_title": "", "@sage/xtrem-master-data/client_functions__master_data_resync_submitted": "", "@sage/xtrem-master-data/company_node_only_one_primary_address": "", "@sage/xtrem-master-data/company_node_only_one_primary_contact": "", "@sage/xtrem-master-data/control__item__landedCost_service_option_inactive": "", "@sage/xtrem-master-data/control-begin__sequence-number__definition_level_is_not_present_in_components": "", "@sage/xtrem-master-data/control-begin__sequence-number__no_sequence_number_component": "", "@sage/xtrem-master-data/control-begin__sequence-number__rtz_level_is_not_present_in_components": "", "@sage/xtrem-master-data/control-begin__sequence-number__sequence-numeric-wrong-component": "", "@sage/xtrem-master-data/create": "", "@sage/xtrem-master-data/create-confirmation": "", "@sage/xtrem-master-data/data_types__address_entity_type_enum__name": "", "@sage/xtrem-master-data/data_types__address_line_data_type__name": "", "@sage/xtrem-master-data/data_types__amount_data_type__name": "", "@sage/xtrem-master-data/data_types__amount_in_company_currency__name": "", "@sage/xtrem-master-data/data_types__amount_in_financial_site_currency__name": "", "@sage/xtrem-master-data/data_types__amount_in_transaction_currency__name": "", "@sage/xtrem-master-data/data_types__approval_status_enum__name": "", "@sage/xtrem-master-data/data_types__base_certificate_property_data_type__name": "", "@sage/xtrem-master-data/data_types__base_decimal__name": "", "@sage/xtrem-master-data/data_types__base_display_status_enum__name": "", "@sage/xtrem-master-data/data_types__base_origin_enum__name": "", "@sage/xtrem-master-data/data_types__base_price__name": "", "@sage/xtrem-master-data/data_types__base_sequence_number_component_type_enum__name": "", "@sage/xtrem-master-data/data_types__base_status_enum__name": "", "@sage/xtrem-master-data/data_types__bom_revision_sequence__name": "", "@sage/xtrem-master-data/data_types__business_entity__name": "", "@sage/xtrem-master-data/data_types__business_entity_id__name": "", "@sage/xtrem-master-data/data_types__business_entity_type_enum__name": "", "@sage/xtrem-master-data/data_types__business_relation_type_enum__name": "", "@sage/xtrem-master-data/data_types__capacity_percentage__name": "", "@sage/xtrem-master-data/data_types__city_data_type__name": "", "@sage/xtrem-master-data/data_types__coefficient_data_type__name": "", "@sage/xtrem-master-data/data_types__company_price_data_type__name": "", "@sage/xtrem-master-data/data_types__constant_sequence_data_type__name": "", "@sage/xtrem-master-data/data_types__consumption_mode_enum__name": "", "@sage/xtrem-master-data/data_types__contact_position_data_type__name": "", "@sage/xtrem-master-data/data_types__contact_property_data_type__name": "", "@sage/xtrem-master-data/data_types__contact_role_enum__name": "", "@sage/xtrem-master-data/data_types__container_type_enum__name": "", "@sage/xtrem-master-data/data_types__cost_calculation_method_enum__name": "", "@sage/xtrem-master-data/data_types__cost_category_type_enum__name": "", "@sage/xtrem-master-data/data_types__cost_data_type__name": "", "@sage/xtrem-master-data/data_types__cost_valuation_method_enum__name": "", "@sage/xtrem-master-data/data_types__cost_value_data_type__name": "", "@sage/xtrem-master-data/data_types__currency__name": "", "@sage/xtrem-master-data/data_types__customer__name": "", "@sage/xtrem-master-data/data_types__customer_display_status_enum__name": "", "@sage/xtrem-master-data/data_types__customer_on_hold_type_enum__name": "", "@sage/xtrem-master-data/data_types__customer_supplier_category__name": "", "@sage/xtrem-master-data/data_types__delivery_mode__name": "", "@sage/xtrem-master-data/data_types__discount_charge_calculation_basis_enum__name": "", "@sage/xtrem-master-data/data_types__discount_charge_calculation_rule_enum__name": "", "@sage/xtrem-master-data/data_types__discount_charge_sign_enum__name": "", "@sage/xtrem-master-data/data_types__discount_charge_value_type_enum__name": "", "@sage/xtrem-master-data/data_types__discount_or_penalty_type_enum__name": "", "@sage/xtrem-master-data/data_types__document_number__name": "", "@sage/xtrem-master-data/data_types__due_date_type_enum__name": "", "@sage/xtrem-master-data/data_types__duration_data_type__name": "", "@sage/xtrem-master-data/data_types__ean_number_data_type__name": "", "@sage/xtrem-master-data/data_types__efficiency_percentage__name": "", "@sage/xtrem-master-data/data_types__email_action_type_enum__name": "", "@sage/xtrem-master-data/data_types__exchange_rate__name": "", "@sage/xtrem-master-data/data_types__extra_large_string__name": "", "@sage/xtrem-master-data/data_types__fake_site_reference_datatype__name": "", "@sage/xtrem-master-data/data_types__incoterm__name": "", "@sage/xtrem-master-data/data_types__incoterm_data_type__name": "", "@sage/xtrem-master-data/data_types__indirect_cost_percentage__name": "", "@sage/xtrem-master-data/data_types__input_sequence_data_type__name": "", "@sage/xtrem-master-data/data_types__item__name": "", "@sage/xtrem-master-data/data_types__item_category__name": "", "@sage/xtrem-master-data/data_types__item_category_type_enum__name": "", "@sage/xtrem-master-data/data_types__item_flow_type_enum__name": "", "@sage/xtrem-master-data/data_types__item_price_type_enum__name": "", "@sage/xtrem-master-data/data_types__item_status_enum__name": "", "@sage/xtrem-master-data/data_types__item_type_enum__name": "", "@sage/xtrem-master-data/data_types__label_format_data_type__name": "", "@sage/xtrem-master-data/data_types__large_string__name": "", "@sage/xtrem-master-data/data_types__legal_entity_enum__name": "", "@sage/xtrem-master-data/data_types__localized_sic_description_data_type__name": "", "@sage/xtrem-master-data/data_types__location__name": "", "@sage/xtrem-master-data/data_types__location_category_enum__name": "", "@sage/xtrem-master-data/data_types__location_sequence__name": "", "@sage/xtrem-master-data/data_types__lot_management_enum__name": "", "@sage/xtrem-master-data/data_types__master_data_company__name": "", "@sage/xtrem-master-data/data_types__master_data_site__name": "", "@sage/xtrem-master-data/data_types__medium_string__name": "", "@sage/xtrem-master-data/data_types__model_data_type__name": "", "@sage/xtrem-master-data/data_types__note__name": "", "@sage/xtrem-master-data/data_types__order_cost_data_type__name": "", "@sage/xtrem-master-data/data_types__order_type_enum__name": "", "@sage/xtrem-master-data/data_types__payment_method_enum__name": "", "@sage/xtrem-master-data/data_types__payment_term__name": "", "@sage/xtrem-master-data/data_types__payment_term_data_type__name": "", "@sage/xtrem-master-data/data_types__payment_term_discount_or_penalty_type_enum__name": "", "@sage/xtrem-master-data/data_types__percentage__name": "", "@sage/xtrem-master-data/data_types__percentage_work_order_data_type__name": "", "@sage/xtrem-master-data/data_types__period_type_enum__name": "", "@sage/xtrem-master-data/data_types__postcode_data_type__name": "", "@sage/xtrem-master-data/data_types__potency_percentage__name": "", "@sage/xtrem-master-data/data_types__preferred_process_enum__name": "", "@sage/xtrem-master-data/data_types__price__name": "", "@sage/xtrem-master-data/data_types__price_data_type__name": "", "@sage/xtrem-master-data/data_types__price_in_sales_price__name": "", "@sage/xtrem-master-data/data_types__price_percentage__name": "", "@sage/xtrem-master-data/data_types__quantity__name": "", "@sage/xtrem-master-data/data_types__quantity_in_purchase_unit__name": "", "@sage/xtrem-master-data/data_types__quantity_in_sales_unit__name": "", "@sage/xtrem-master-data/data_types__quantity_in_stock_unit__name": "", "@sage/xtrem-master-data/data_types__quantity_in_unit__name": "", "@sage/xtrem-master-data/data_types__quantity_in_volume_unit__name": "", "@sage/xtrem-master-data/data_types__quantity_in_weight_unit__name": "", "@sage/xtrem-master-data/data_types__reason_code__name": "", "@sage/xtrem-master-data/data_types__region_data_type__name": "", "@sage/xtrem-master-data/data_types__replenishment_method_enum__name": "", "@sage/xtrem-master-data/data_types__resource_cost__name": "", "@sage/xtrem-master-data/data_types__resource_group_type_enum__name": "", "@sage/xtrem-master-data/data_types__run_time_data_type__name": "", "@sage/xtrem-master-data/data_types__scrap_factor_percentage__name": "", "@sage/xtrem-master-data/data_types__sequence_counter_definition_level_enum__name": "", "@sage/xtrem-master-data/data_types__sequence_number__name": "", "@sage/xtrem-master-data/data_types__sequence_number_reset_frequency_enum__name": "", "@sage/xtrem-master-data/data_types__sequence_number_type_enum__name": "", "@sage/xtrem-master-data/data_types__serial_number_management_enum__name": "", "@sage/xtrem-master-data/data_types__serial_number_usage_enum__name": "", "@sage/xtrem-master-data/data_types__setup_time_data_type__name": "", "@sage/xtrem-master-data/data_types__shift_data_type__name": "", "@sage/xtrem-master-data/data_types__standard_property_data_type__name": "", "@sage/xtrem-master-data/data_types__stock_management_mode_enum__name": "", "@sage/xtrem-master-data/data_types__stock_quantity__name": "", "@sage/xtrem-master-data/data_types__stock_quantity_variance_percentage__name": "", "@sage/xtrem-master-data/data_types__stock_variation_value__name": "", "@sage/xtrem-master-data/data_types__supplier__name": "", "@sage/xtrem-master-data/data_types__supplier_item_property_data_type__name": "", "@sage/xtrem-master-data/data_types__supplier_type_enum__name": "", "@sage/xtrem-master-data/data_types__symbol_data_type__name": "", "@sage/xtrem-master-data/data_types__tax_calculation_status_enum__name": "", "@sage/xtrem-master-data/data_types__telephone_number_data_type__name": "", "@sage/xtrem-master-data/data_types__time_data_type__name": "", "@sage/xtrem-master-data/data_types__time_zone__name": "", "@sage/xtrem-master-data/data_types__title_enum__name": "", "@sage/xtrem-master-data/data_types__unit_conversion_coefficient__name": "", "@sage/xtrem-master-data/data_types__unit_conversion_type_enum__name": "", "@sage/xtrem-master-data/data_types__unit_of_measure__name": "", "@sage/xtrem-master-data/data_types__unit_type_enum__name": "", "@sage/xtrem-master-data/data_types__version_data_type__name": "", "@sage/xtrem-master-data/data_types__volume_percentage__name": "", "@sage/xtrem-master-data/data_types__week_days_enum__name": "", "@sage/xtrem-master-data/data_types__weight_percentage__name": "", "@sage/xtrem-master-data/data_types__work_in_progress_document_type_enum__name": "", "@sage/xtrem-master-data/data_types__zone_type_enum__name": "", "@sage/xtrem-master-data/data-types/percentage__value_greater_than_a_maximum": "", "@sage/xtrem-master-data/data-types/percentage__value_less_than_a_minimum": "", "@sage/xtrem-master-data/data-types/percentage__value_not_in_allowed_range": "", "@sage/xtrem-master-data/delete-confirmation": "", "@sage/xtrem-master-data/edit-create-customer-price": "", "@sage/xtrem-master-data/edit-create-line": "", "@sage/xtrem-master-data/edit-create-supplier": "", "@sage/xtrem-master-data/edit-create-supplier-price": "", "@sage/xtrem-master-data/email-validation-error": "", "@sage/xtrem-master-data/enums__address_entity_type__businessEntity": "", "@sage/xtrem-master-data/enums__address_entity_type__company": "", "@sage/xtrem-master-data/enums__address_entity_type__customer": "", "@sage/xtrem-master-data/enums__address_entity_type__site": "", "@sage/xtrem-master-data/enums__address_entity_type__supplier": "", "@sage/xtrem-master-data/enums__approval_status__approved": "", "@sage/xtrem-master-data/enums__approval_status__changeRequested": "", "@sage/xtrem-master-data/enums__approval_status__confirmed": "", "@sage/xtrem-master-data/enums__approval_status__draft": "", "@sage/xtrem-master-data/enums__approval_status__pendingApproval": "", "@sage/xtrem-master-data/enums__approval_status__rejected": "", "@sage/xtrem-master-data/enums__base_display_status__approved": "", "@sage/xtrem-master-data/enums__base_display_status__changeRequested": "", "@sage/xtrem-master-data/enums__base_display_status__closed": "", "@sage/xtrem-master-data/enums__base_display_status__confirmed": "", "@sage/xtrem-master-data/enums__base_display_status__credited": "", "@sage/xtrem-master-data/enums__base_display_status__draft": "", "@sage/xtrem-master-data/enums__base_display_status__error": "", "@sage/xtrem-master-data/enums__base_display_status__invoiced": "", "@sage/xtrem-master-data/enums__base_display_status__noVariance": "", "@sage/xtrem-master-data/enums__base_display_status__ordered": "", "@sage/xtrem-master-data/enums__base_display_status__paid": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyCredited": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyInvoiced": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyOrdered": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyPaid": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyReceived": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyReturned": "", "@sage/xtrem-master-data/enums__base_display_status__partiallyShipped": "", "@sage/xtrem-master-data/enums__base_display_status__pending": "", "@sage/xtrem-master-data/enums__base_display_status__pendingApproval": "", "@sage/xtrem-master-data/enums__base_display_status__posted": "", "@sage/xtrem-master-data/enums__base_display_status__postingError": "", "@sage/xtrem-master-data/enums__base_display_status__postingInProgress": "", "@sage/xtrem-master-data/enums__base_display_status__quote": "", "@sage/xtrem-master-data/enums__base_display_status__readyToProcess": "", "@sage/xtrem-master-data/enums__base_display_status__readyToShip": "", "@sage/xtrem-master-data/enums__base_display_status__received": "", "@sage/xtrem-master-data/enums__base_display_status__rejected": "", "@sage/xtrem-master-data/enums__base_display_status__returned": "", "@sage/xtrem-master-data/enums__base_display_status__shipped": "", "@sage/xtrem-master-data/enums__base_display_status__stockError": "", "@sage/xtrem-master-data/enums__base_display_status__taxCalculationFailed": "", "@sage/xtrem-master-data/enums__base_display_status__variance": "", "@sage/xtrem-master-data/enums__base_display_status__varianceApproved": "", "@sage/xtrem-master-data/enums__base_origin__direct": "", "@sage/xtrem-master-data/enums__base_origin__invoice": "", "@sage/xtrem-master-data/enums__base_origin__order": "", "@sage/xtrem-master-data/enums__base_origin__purchaseCreditMemo": "", "@sage/xtrem-master-data/enums__base_origin__purchaseInvoice": "", "@sage/xtrem-master-data/enums__base_origin__purchaseOrder": "", "@sage/xtrem-master-data/enums__base_origin__purchaseReceipt": "", "@sage/xtrem-master-data/enums__base_origin__purchaseRequisition": "", "@sage/xtrem-master-data/enums__base_origin__purchaseReturn": "", "@sage/xtrem-master-data/enums__base_origin__purchaseSuggestion": "", "@sage/xtrem-master-data/enums__base_origin__return": "", "@sage/xtrem-master-data/enums__base_origin__shipment": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__company": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__constant": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__day": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__month": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceAlpha": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceNumber": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__site": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__week": "", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__year": "", "@sage/xtrem-master-data/enums__base_status__approved": "", "@sage/xtrem-master-data/enums__base_status__changeRequested": "", "@sage/xtrem-master-data/enums__base_status__closed": "", "@sage/xtrem-master-data/enums__base_status__confirmed": "", "@sage/xtrem-master-data/enums__base_status__credited": "", "@sage/xtrem-master-data/enums__base_status__draft": "", "@sage/xtrem-master-data/enums__base_status__error": "", "@sage/xtrem-master-data/enums__base_status__inProgress": "", "@sage/xtrem-master-data/enums__base_status__invoiced": "", "@sage/xtrem-master-data/enums__base_status__noVariance": "", "@sage/xtrem-master-data/enums__base_status__partiallyCredited": "", "@sage/xtrem-master-data/enums__base_status__partiallyInvoiced": "", "@sage/xtrem-master-data/enums__base_status__partiallyReceived": "", "@sage/xtrem-master-data/enums__base_status__partiallyReturned": "", "@sage/xtrem-master-data/enums__base_status__partiallyShipped": "", "@sage/xtrem-master-data/enums__base_status__pending": "", "@sage/xtrem-master-data/enums__base_status__pendingApproval": "", "@sage/xtrem-master-data/enums__base_status__posted": "", "@sage/xtrem-master-data/enums__base_status__postingError": "", "@sage/xtrem-master-data/enums__base_status__postingInProgress": "", "@sage/xtrem-master-data/enums__base_status__quote": "", "@sage/xtrem-master-data/enums__base_status__readyToProcess": "", "@sage/xtrem-master-data/enums__base_status__readyToShip": "", "@sage/xtrem-master-data/enums__base_status__received": "", "@sage/xtrem-master-data/enums__base_status__rejected": "", "@sage/xtrem-master-data/enums__base_status__returned": "", "@sage/xtrem-master-data/enums__base_status__shipped": "", "@sage/xtrem-master-data/enums__base_status__stockError": "", "@sage/xtrem-master-data/enums__base_status__taxCalculationFailed": "", "@sage/xtrem-master-data/enums__base_status__variance": "", "@sage/xtrem-master-data/enums__base_status__varianceApproved": "", "@sage/xtrem-master-data/enums__business_entity_type__all": "", "@sage/xtrem-master-data/enums__business_entity_type__customer": "", "@sage/xtrem-master-data/enums__business_entity_type__supplier": "", "@sage/xtrem-master-data/enums__business_relation_type__customer": "", "@sage/xtrem-master-data/enums__business_relation_type__supplier": "", "@sage/xtrem-master-data/enums__consumption_mode__none": "", "@sage/xtrem-master-data/enums__consumption_mode__quantity": "", "@sage/xtrem-master-data/enums__consumption_mode__time": "", "@sage/xtrem-master-data/enums__contact_role__commercialContact": "", "@sage/xtrem-master-data/enums__contact_role__financialContact": "", "@sage/xtrem-master-data/enums__contact_role__mainContact": "", "@sage/xtrem-master-data/enums__container_type__barrel": "", "@sage/xtrem-master-data/enums__container_type__bigBag": "", "@sage/xtrem-master-data/enums__container_type__box": "", "@sage/xtrem-master-data/enums__container_type__container": "", "@sage/xtrem-master-data/enums__container_type__other": "", "@sage/xtrem-master-data/enums__container_type__pack": "", "@sage/xtrem-master-data/enums__container_type__pallet": "", "@sage/xtrem-master-data/enums__cost_calculation_method__compound": "", "@sage/xtrem-master-data/enums__cost_calculation_method__cumulate": "", "@sage/xtrem-master-data/enums__cost_category_type__budgeted": "", "@sage/xtrem-master-data/enums__cost_category_type__other": "", "@sage/xtrem-master-data/enums__cost_category_type__simulated": "", "@sage/xtrem-master-data/enums__cost_category_type__standard": "", "@sage/xtrem-master-data/enums__cost_valuation_method__averageCost": "", "@sage/xtrem-master-data/enums__cost_valuation_method__fifoCost": "", "@sage/xtrem-master-data/enums__cost_valuation_method__standardCost": "", "@sage/xtrem-master-data/enums__customer_display_status__active": "", "@sage/xtrem-master-data/enums__customer_display_status__inactive": "", "@sage/xtrem-master-data/enums__customer_display_status__onHold": "", "@sage/xtrem-master-data/enums__customer_on_hold_type__blocking": "", "@sage/xtrem-master-data/enums__customer_on_hold_type__none": "", "@sage/xtrem-master-data/enums__customer_on_hold_type__warning": "", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPrice": "", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPriceAndCompound": "", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byLine": "", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byUnit": "", "@sage/xtrem-master-data/enums__discount_charge_sign__decrease": "", "@sage/xtrem-master-data/enums__discount_charge_sign__increase": "", "@sage/xtrem-master-data/enums__discount_charge_value_type__amount": "", "@sage/xtrem-master-data/enums__discount_charge_value_type__percentage": "", "@sage/xtrem-master-data/enums__discount_or_penalty_type__amount": "", "@sage/xtrem-master-data/enums__discount_or_penalty_type__percentage": "", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDate": "", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDateAndExtendedToEndOfMonth": "", "@sage/xtrem-master-data/enums__due_date_type__afterTheEndOfTheMonthOfInvoiceDate": "", "@sage/xtrem-master-data/enums__email_action_type__approved": "", "@sage/xtrem-master-data/enums__email_action_type__rejected": "", "@sage/xtrem-master-data/enums__item_category_type__allergen": "", "@sage/xtrem-master-data/enums__item_category_type__ghsClassification": "", "@sage/xtrem-master-data/enums__item_category_type__none": "", "@sage/xtrem-master-data/enums__item_flow_type__manufactured": "", "@sage/xtrem-master-data/enums__item_flow_type__purchased": "", "@sage/xtrem-master-data/enums__item_flow_type__sold": "", "@sage/xtrem-master-data/enums__item_flow_type__subcontracted": "", "@sage/xtrem-master-data/enums__item_price_type__discount": "", "@sage/xtrem-master-data/enums__item_price_type__normal": "", "@sage/xtrem-master-data/enums__item_price_type__specialOffer": "", "@sage/xtrem-master-data/enums__item_status__active": "", "@sage/xtrem-master-data/enums__item_status__inDevelopment": "", "@sage/xtrem-master-data/enums__item_status__notRenewed": "", "@sage/xtrem-master-data/enums__item_status__notUsable": "", "@sage/xtrem-master-data/enums__item_status__obsolete": "", "@sage/xtrem-master-data/enums__item_type__good": "", "@sage/xtrem-master-data/enums__item_type__landedCost": "", "@sage/xtrem-master-data/enums__item_type__service": "", "@sage/xtrem-master-data/enums__legal_entity__corporation": "", "@sage/xtrem-master-data/enums__legal_entity__physicalPerson": "", "@sage/xtrem-master-data/enums__location_category__customer": "", "@sage/xtrem-master-data/enums__location_category__dock": "", "@sage/xtrem-master-data/enums__location_category__internal": "", "@sage/xtrem-master-data/enums__location_category__subcontract": "", "@sage/xtrem-master-data/enums__location_category__virtual": "", "@sage/xtrem-master-data/enums__lot_management__lotManagement": "", "@sage/xtrem-master-data/enums__lot_management__lotSublotManagement": "", "@sage/xtrem-master-data/enums__lot_management__notManaged": "", "@sage/xtrem-master-data/enums__order_type__closed": "", "@sage/xtrem-master-data/enums__order_type__firm": "", "@sage/xtrem-master-data/enums__order_type__planned": "", "@sage/xtrem-master-data/enums__order_type__suggested": "", "@sage/xtrem-master-data/enums__payment_method__ACH": "", "@sage/xtrem-master-data/enums__payment_method__cash": "", "@sage/xtrem-master-data/enums__payment_method__creditCard": "", "@sage/xtrem-master-data/enums__payment_method__EFT": "", "@sage/xtrem-master-data/enums__payment_method__printedCheck": "", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__amount": "", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__percentage": "", "@sage/xtrem-master-data/enums__period_type__day": "", "@sage/xtrem-master-data/enums__period_type__month": "", "@sage/xtrem-master-data/enums__period_type__week": "", "@sage/xtrem-master-data/enums__period_type__year": "", "@sage/xtrem-master-data/enums__preferred_process__production": "", "@sage/xtrem-master-data/enums__preferred_process__purchasing": "", "@sage/xtrem-master-data/enums__replenishment_method__byMRP": "", "@sage/xtrem-master-data/enums__replenishment_method__byReorderPoint": "", "@sage/xtrem-master-data/enums__replenishment_method__notManaged": "", "@sage/xtrem-master-data/enums__resource_group_type__labor": "", "@sage/xtrem-master-data/enums__resource_group_type__machine": "", "@sage/xtrem-master-data/enums__resource_group_type__subcontract": "", "@sage/xtrem-master-data/enums__resource_group_type__tool": "", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__application": "", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__company": "", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__site": "", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__monthly": "", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__noReset": "", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__yearly": "", "@sage/xtrem-master-data/enums__sequence_number_type__alphanumeric": "", "@sage/xtrem-master-data/enums__sequence_number_type__numeric": "", "@sage/xtrem-master-data/enums__serial_number_management__managed": "", "@sage/xtrem-master-data/enums__serial_number_management__notManaged": "", "@sage/xtrem-master-data/enums__serial_number_usage__issueAndReceipt": "", "@sage/xtrem-master-data/enums__serial_number_usage__issueOnly": "", "@sage/xtrem-master-data/enums__stock_management_mode__byOrder": "", "@sage/xtrem-master-data/enums__stock_management_mode__byProject": "", "@sage/xtrem-master-data/enums__stock_management_mode__onStock": "", "@sage/xtrem-master-data/enums__supplier_type__chemical": "", "@sage/xtrem-master-data/enums__supplier_type__foodAndBeverage": "", "@sage/xtrem-master-data/enums__supplier_type__other": "", "@sage/xtrem-master-data/enums__tax_calculation_status__done": "", "@sage/xtrem-master-data/enums__tax_calculation_status__failed": "", "@sage/xtrem-master-data/enums__tax_calculation_status__inProgress": "", "@sage/xtrem-master-data/enums__tax_calculation_status__notDone": "", "@sage/xtrem-master-data/enums__title__dr": "", "@sage/xtrem-master-data/enums__title__family": "", "@sage/xtrem-master-data/enums__title__master": "", "@sage/xtrem-master-data/enums__title__miss": "", "@sage/xtrem-master-data/enums__title__mr": "", "@sage/xtrem-master-data/enums__title__mrs": "", "@sage/xtrem-master-data/enums__title__ms": "", "@sage/xtrem-master-data/enums__title__prof": "", "@sage/xtrem-master-data/enums__unit_conversion_type__other": "", "@sage/xtrem-master-data/enums__unit_conversion_type__purchase": "", "@sage/xtrem-master-data/enums__unit_conversion_type__sales": "", "@sage/xtrem-master-data/enums__unit_type__area": "", "@sage/xtrem-master-data/enums__unit_type__each": "", "@sage/xtrem-master-data/enums__unit_type__length": "", "@sage/xtrem-master-data/enums__unit_type__temperature": "", "@sage/xtrem-master-data/enums__unit_type__time": "", "@sage/xtrem-master-data/enums__unit_type__volume": "", "@sage/xtrem-master-data/enums__unit_type__weight": "", "@sage/xtrem-master-data/enums__week_days__friday": "", "@sage/xtrem-master-data/enums__week_days__monday": "", "@sage/xtrem-master-data/enums__week_days__saturday": "", "@sage/xtrem-master-data/enums__week_days__sunday": "", "@sage/xtrem-master-data/enums__week_days__thursday": "", "@sage/xtrem-master-data/enums__week_days__tuesday": "", "@sage/xtrem-master-data/enums__week_days__wednesday": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__materialNeed": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseOrder": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReceipt": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReturn": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__salesOrder": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferOrder": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferReceipt": "", "@sage/xtrem-master-data/enums__work_in_progress_document_type__workOrder": "", "@sage/xtrem-master-data/enums__zone_type__chemical": "", "@sage/xtrem-master-data/enums__zone_type__frozen": "", "@sage/xtrem-master-data/enums__zone_type__hazard": "", "@sage/xtrem-master-data/enums__zone_type__magnetic": "", "@sage/xtrem-master-data/enums__zone_type__restricted": "", "@sage/xtrem-master-data/enums__zone_type__secured": "", "@sage/xtrem-master-data/enums__zone_type__sensitive": "", "@sage/xtrem-master-data/enums__zone_type__virtual": "", "@sage/xtrem-master-data/events__control__document_external_note_must_be_empty": "", "@sage/xtrem-master-data/events/control__address-control__postalcode-validation-error": "", "@sage/xtrem-master-data/events/control__address-control__telephone-validation-error": "", "@sage/xtrem-master-data/events/control__address-control__zipcode-validation-error": "", "@sage/xtrem-master-data/events/control__base_sequence_number_control_length": "", "@sage/xtrem-master-data/events/control__base_sequence_number_control_type": "", "@sage/xtrem-master-data/events/control__business-entity__customer_already_exists_with_same_name": "", "@sage/xtrem-master-data/events/control__business-entity__site_already_exists_with_same_name": "", "@sage/xtrem-master-data/events/control__business-entity__supplier_already_exists_with_same_name": "", "@sage/xtrem-master-data/events/control__cost-category__the_standard_cost_category_must_be_mandatory": "", "@sage/xtrem-master-data/events/control__item__allergens-not-allowed": "", "@sage/xtrem-master-data/events/control__item__cannot-be-bom-revision-managed": "", "@sage/xtrem-master-data/events/control__item__cannot-be-phantom": "", "@sage/xtrem-master-data/events/control__item__code-must-be-a-number": "", "@sage/xtrem-master-data/events/control__item__empty-preferredProcess": "", "@sage/xtrem-master-data/events/control__item__ghs-classification-not-allowed": "", "@sage/xtrem-master-data/events/control__item__incorrect-value-for-economic-quantity": "", "@sage/xtrem-master-data/events/control__item__must-be-service": "", "@sage/xtrem-master-data/events/control__item__must-be-service-or-landed-cost": "", "@sage/xtrem-master-data/events/control__item__must-not-be-service": "", "@sage/xtrem-master-data/events/control__item__must-not-be-service-or-landed-cost": "", "@sage/xtrem-master-data/events/control__item__property-incorrect-for-not-stock-managed-items": "", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-and-landed-cost-items": "", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-items": "", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect": "", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-manufacturing-items": "", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-purchasing-items": "", "@sage/xtrem-master-data/events/control__location_sequence_control__range_item_length": "", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_capital_letters": "", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_range": "", "@sage/xtrem-master-data/events/control__location_sequence_control_number_value": "", "@sage/xtrem-master-data/events/control__location_sequence_control_numeric_range": "", "@sage/xtrem-master-data/events/control__sequence-number__force-reset-with-tenant": "", "@sage/xtrem-master-data/events/control__time-control__end-date-cannot-be-empty": "", "@sage/xtrem-master-data/events/control__time-control__end-datetime-cannot-be-empty": "", "@sage/xtrem-master-data/events/control__time-control__invalid-date-range": "", "@sage/xtrem-master-data/events/control__time-control__invalid-datetime-range": "", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-empty": "", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-greater-than-end-date": "", "@sage/xtrem-master-data/events/control__time-control__start-datetime-cannot-be-empty": "", "@sage/xtrem-master-data/events/control__time-control__time-cannot-be-empty": "", "@sage/xtrem-master-data/events/control__time-control__time-format-HH-MM": "", "@sage/xtrem-master-data/found-matching-business-entities": "", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description": "", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description_error": "", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_fail": "", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_success": "", "@sage/xtrem-master-data/function__customer_price_reason_priority_already_exists": "", "@sage/xtrem-master-data/functions__business_entity__incorrect_format_siret": "", "@sage/xtrem-master-data/functions__business_entity__not-a-valid-tax-id": "", "@sage/xtrem-master-data/functions__common__download_file": "", "@sage/xtrem-master-data/functions__common__history": "", "@sage/xtrem-master-data/functions__common__invalid_characters": "", "@sage/xtrem-master-data/functions__common__sequence_number_id_is_in_use": "", "@sage/xtrem-master-data/functions__common__sequence_number_id_must_have_two_characters": "", "@sage/xtrem-master-data/functions__exchange-rate__no-rate-found-for-this-currency-pair": "", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-enum-value": "", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-length": "", "@sage/xtrem-master-data/functions__sequence-number-lib__no_company_sequence_number_value_defined": "", "@sage/xtrem-master-data/functions__sequence-number-lib__no_site_sequence_number_value_defined": "", "@sage/xtrem-master-data/functions__sequence-number-lib__no-component-of-type": "", "@sage/xtrem-master-data/functions__sequence-number-lib__sequence-number-exceeded": "", "@sage/xtrem-master-data/functions__unit-of-measure-lib__different-unit-type": "", "@sage/xtrem-master-data/functions__unit-of-measure-lib__no-factors-for-the-units": "", "@sage/xtrem-master-data/generate": "", "@sage/xtrem-master-data/info": "", "@sage/xtrem-master-data/invalid-period": "", "@sage/xtrem-master-data/invalid-quantity-range": "", "@sage/xtrem-master-data/item__price-cannot-be-negative": "", "@sage/xtrem-master-data/item-not-sold": "", "@sage/xtrem-master-data/mailer_no_mailer_redirect_url_provided": "", "@sage/xtrem-master-data/menu_item__declarations": "", "@sage/xtrem-master-data/menu_item__dev-tools": "", "@sage/xtrem-master-data/menu_item__features": "", "@sage/xtrem-master-data/menu_item__features-inventory": "", "@sage/xtrem-master-data/menu_item__features-items": "", "@sage/xtrem-master-data/menu_item__features-manufacturing": "", "@sage/xtrem-master-data/menu_item__features-purchasing": "", "@sage/xtrem-master-data/menu_item__features-resources": "", "@sage/xtrem-master-data/menu_item__features-sales": "", "@sage/xtrem-master-data/menu_item__features-stock": "", "@sage/xtrem-master-data/menu_item__finance": "", "@sage/xtrem-master-data/menu_item__integrations": "", "@sage/xtrem-master-data/menu_item__inventory": "", "@sage/xtrem-master-data/menu_item__inventory-data": "", "@sage/xtrem-master-data/menu_item__item-data": "", "@sage/xtrem-master-data/menu_item__items": "", "@sage/xtrem-master-data/menu_item__licence-plate-data": "", "@sage/xtrem-master-data/menu_item__location-data": "", "@sage/xtrem-master-data/menu_item__manufacturing": "", "@sage/xtrem-master-data/menu_item__purchasing": "", "@sage/xtrem-master-data/menu_item__resources": "", "@sage/xtrem-master-data/menu_item__resources-data": "", "@sage/xtrem-master-data/menu_item__sales": "", "@sage/xtrem-master-data/menu_item__stock": "", "@sage/xtrem-master-data/menu_item__stock-data": "", "@sage/xtrem-master-data/multiple-existing-business-entities": "", "@sage/xtrem-master-data/node__base_document__no_validation_email_allowed": "", "@sage/xtrem-master-data/node_base_resource_location_site_mismatch": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__addresses": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__contacts": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__country": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__currency": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__customerOnHoldCheck": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__isSequenceNumberIdUsed": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__priceScale": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryAddress": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryContact": "", "@sage/xtrem-master-data/node-extensions__company_extension__property__sequenceNumberId": "", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser": "", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__nodeName": "", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__options": "", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__propertyOrOperation": "", "@sage/xtrem-master-data/node-extensions__country_extension__property__currency": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__businessEntity": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__country": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__currency": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__defaultLocation": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialCurrency": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialSite": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isFinance": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isInventory": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isLocationManaged": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isManufacturing": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isProjectManagement": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isPurchase": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSales": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSequenceNumberIdUsed": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__itemSites": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__primaryAddress": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__sequenceNumberId": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__siret": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__stockSite": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__taxIdNumber": "", "@sage/xtrem-master-data/node-extensions__site_extension__property__timeZone": "", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones": "", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__address__node_name": "", "@sage/xtrem-master-data/nodes__address__property__addressLine1": "", "@sage/xtrem-master-data/nodes__address__property__addressLine2": "", "@sage/xtrem-master-data/nodes__address__property__city": "", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddress": "", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/nodes__address__property__country": "", "@sage/xtrem-master-data/nodes__address__property__locationPhoneNumber": "", "@sage/xtrem-master-data/nodes__address__property__name": "", "@sage/xtrem-master-data/nodes__address__property__postcode": "", "@sage/xtrem-master-data/nodes__address__property__region": "", "@sage/xtrem-master-data/nodes__address_base__node_name": "", "@sage/xtrem-master-data/nodes__address_base__property__address": "", "@sage/xtrem-master-data/nodes__address_base__property__addressLine1": "", "@sage/xtrem-master-data/nodes__address_base__property__addressLine2": "", "@sage/xtrem-master-data/nodes__address_base__property__city": "", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddress": "", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/nodes__address_base__property__country": "", "@sage/xtrem-master-data/nodes__address_base__property__isActive": "", "@sage/xtrem-master-data/nodes__address_base__property__locationPhoneNumber": "", "@sage/xtrem-master-data/nodes__address_base__property__name": "", "@sage/xtrem-master-data/nodes__address_base__property__postcode": "", "@sage/xtrem-master-data/nodes__address_base__property__region": "", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__allergen__node_name": "", "@sage/xtrem-master-data/nodes__allergen__property__id": "", "@sage/xtrem-master-data/nodes__allergen__property__isActive": "", "@sage/xtrem-master-data/nodes__allergen__property__name": "", "@sage/xtrem-master-data/nodes__allergen__property__pictogram": "", "@sage/xtrem-master-data/nodes__base_business_relation__node_name": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__businessEntity": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__category": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__country": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__currency": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__id": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__image": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__internalNote": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__isActive": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__legalEntity": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__minimumOrderAmount": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__name": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__paymentTerm": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryAddress": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryContact": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__siret": "", "@sage/xtrem-master-data/nodes__base_business_relation__property__taxIdNumber": "", "@sage/xtrem-master-data/nodes__base_capability__node_name": "", "@sage/xtrem-master-data/nodes__base_capability__property__capabilityLevel": "", "@sage/xtrem-master-data/nodes__base_capability__property__dateEndValid": "", "@sage/xtrem-master-data/nodes__base_capability__property__dateRangeValidity": "", "@sage/xtrem-master-data/nodes__base_capability__property__dateStartValid": "", "@sage/xtrem-master-data/nodes__base_capability__property__id": "", "@sage/xtrem-master-data/nodes__base_capability__property__name": "", "@sage/xtrem-master-data/nodes__base_certificate__node_name": "", "@sage/xtrem-master-data/nodes__base_certificate__property__certificationBody": "", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfCertification": "", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfOriginalCertification": "", "@sage/xtrem-master-data/nodes__base_certificate__property__id": "", "@sage/xtrem-master-data/nodes__base_certificate__property__standard": "", "@sage/xtrem-master-data/nodes__base_certificate__property__validUntil": "", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo": "", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentIds": "", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentLineIds": "", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync": "", "@sage/xtrem-master-data/nodes__base_document__header_currency_not_updatable": "", "@sage/xtrem-master-data/nodes__base_document__id_already_exists": "", "@sage/xtrem-master-data/nodes__base_document__lines_mandatory": "", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail": "", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__document": "", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__user": "", "@sage/xtrem-master-data/nodes__base_document__no_financial_site": "", "@sage/xtrem-master-data/nodes__base_document__node_name": "", "@sage/xtrem-master-data/nodes__base_document__order_date_not_updatable": "", "@sage/xtrem-master-data/nodes__base_document__property__approvalPage": "", "@sage/xtrem-master-data/nodes__base_document__property__approvalStatus": "", "@sage/xtrem-master-data/nodes__base_document__property__approvalUrl": "", "@sage/xtrem-master-data/nodes__base_document__property__businessEntityAddress": "", "@sage/xtrem-master-data/nodes__base_document__property__canPrint": "", "@sage/xtrem-master-data/nodes__base_document__property__canUpdateClosedDocument": "", "@sage/xtrem-master-data/nodes__base_document__property__companyCurrency": "", "@sage/xtrem-master-data/nodes__base_document__property__currency": "", "@sage/xtrem-master-data/nodes__base_document__property__date": "", "@sage/xtrem-master-data/nodes__base_document__property__displayStatus": "", "@sage/xtrem-master-data/nodes__base_document__property__documentDate": "", "@sage/xtrem-master-data/nodes__base_document__property__documentUrl": "", "@sage/xtrem-master-data/nodes__base_document__property__externalNote": "", "@sage/xtrem-master-data/nodes__base_document__property__financialSite": "", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForResync": "", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForStock": "", "@sage/xtrem-master-data/nodes__base_document__property__internalNote": "", "@sage/xtrem-master-data/nodes__base_document__property__isExternalNote": "", "@sage/xtrem-master-data/nodes__base_document__property__isOverwriteNote": "", "@sage/xtrem-master-data/nodes__base_document__property__isPrinted": "", "@sage/xtrem-master-data/nodes__base_document__property__isSent": "", "@sage/xtrem-master-data/nodes__base_document__property__isTransferHeaderNote": "", "@sage/xtrem-master-data/nodes__base_document__property__isTransferLineNote": "", "@sage/xtrem-master-data/nodes__base_document__property__lines": "", "@sage/xtrem-master-data/nodes__base_document__property__number": "", "@sage/xtrem-master-data/nodes__base_document__property__page": "", "@sage/xtrem-master-data/nodes__base_document__property__site": "", "@sage/xtrem-master-data/nodes__base_document__property__siteAddress": "", "@sage/xtrem-master-data/nodes__base_document__property__status": "", "@sage/xtrem-master-data/nodes__base_document__property__stockSite": "", "@sage/xtrem-master-data/nodes__base_document__property__text": "", "@sage/xtrem-master-data/nodes__base_document__property__transactionCurrency": "", "@sage/xtrem-master-data/nodes__base_document__update_not_allowed_status_closed": "", "@sage/xtrem-master-data/nodes__base_document_item_line__node_name": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__document": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentId": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentNumber": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__externalNote": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__forceUpdateForStock": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__internalNote": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__isExternalNote": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__item": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemDescription": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemSite": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__origin": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantity": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantityInStockUnit": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__site": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__siteLinkedAddress": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__status": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSite": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSiteLinkedAddress": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockUnit": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unit": "", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unitToStockUnitConversionFactor": "", "@sage/xtrem-master-data/nodes__base_document_line__node_name": "", "@sage/xtrem-master-data/nodes__base_document_line__property__documentId": "", "@sage/xtrem-master-data/nodes__base_document_line__property__documentNumber": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__node_name": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__commodityCode": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__company": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__date": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__fromItem": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__itemCategory": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__lines": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__site": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__toItem": "", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__user": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__improper_calculation_rule": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__node_name": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__amount": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basis": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basisDeterminated": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationBasis": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationRule": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__sign": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__value": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueDeterminated": "", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueType": "", "@sage/xtrem-master-data/nodes__base_line_to_line__node_name": "", "@sage/xtrem-master-data/nodes__base_line_to_line__property__from": "", "@sage/xtrem-master-data/nodes__base_line_to_line__property__to": "", "@sage/xtrem-master-data/nodes__base_resource__node_name": "", "@sage/xtrem-master-data/nodes__base_resource__property__activeFrom": "", "@sage/xtrem-master-data/nodes__base_resource__property__activeRange": "", "@sage/xtrem-master-data/nodes__base_resource__property__activeTo": "", "@sage/xtrem-master-data/nodes__base_resource__property__description": "", "@sage/xtrem-master-data/nodes__base_resource__property__efficiency": "", "@sage/xtrem-master-data/nodes__base_resource__property__id": "", "@sage/xtrem-master-data/nodes__base_resource__property__isActive": "", "@sage/xtrem-master-data/nodes__base_resource__property__location": "", "@sage/xtrem-master-data/nodes__base_resource__property__name": "", "@sage/xtrem-master-data/nodes__base_resource__property__resourceCostCategories": "", "@sage/xtrem-master-data/nodes__base_resource__property__resourceImage": "", "@sage/xtrem-master-data/nodes__base_resource__property__site": "", "@sage/xtrem-master-data/nodes__base_resource__property__weeklyShift": "", "@sage/xtrem-master-data/nodes__base_sequence_number__node_name": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__componentLength": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__components": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__definitionLevel": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__id": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isChronological": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isClearedByReset": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isUsed": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__legislation": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__minimumLength": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__name": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__rtzLevel": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__sequenceNumberAssignments": "", "@sage/xtrem-master-data/nodes__base_sequence_number__property__type": "", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames": "", "@sage/xtrem-master-data/nodes__base_sequence_number_component__node_name": "", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__constant": "", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__length": "", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__type": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__node_name": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__components": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isDefault": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isSequenceGenerated": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__node_name": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__business_entity__address_mandatory": "", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__business_entity__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__business_entity__incorrect_format_siret": "", "@sage/xtrem-master-data/nodes__business_entity__node_name": "", "@sage/xtrem-master-data/nodes__business_entity__not-a-valid-tax-id": "", "@sage/xtrem-master-data/nodes__business_entity__primary_address": "", "@sage/xtrem-master-data/nodes__business_entity__primary_address_active": "", "@sage/xtrem-master-data/nodes__business_entity__primary_address_mandatory": "", "@sage/xtrem-master-data/nodes__business_entity__primary_contact": "", "@sage/xtrem-master-data/nodes__business_entity__primary_contact_active": "", "@sage/xtrem-master-data/nodes__business_entity__property__addresses": "", "@sage/xtrem-master-data/nodes__business_entity__property__contacts": "", "@sage/xtrem-master-data/nodes__business_entity__property__country": "", "@sage/xtrem-master-data/nodes__business_entity__property__currency": "", "@sage/xtrem-master-data/nodes__business_entity__property__customer": "", "@sage/xtrem-master-data/nodes__business_entity__property__id": "", "@sage/xtrem-master-data/nodes__business_entity__property__image": "", "@sage/xtrem-master-data/nodes__business_entity__property__isActive": "", "@sage/xtrem-master-data/nodes__business_entity__property__isCustomer": "", "@sage/xtrem-master-data/nodes__business_entity__property__isSite": "", "@sage/xtrem-master-data/nodes__business_entity__property__isSupplier": "", "@sage/xtrem-master-data/nodes__business_entity__property__legalEntity": "", "@sage/xtrem-master-data/nodes__business_entity__property__name": "", "@sage/xtrem-master-data/nodes__business_entity__property__parent": "", "@sage/xtrem-master-data/nodes__business_entity__property__primaryAddress": "", "@sage/xtrem-master-data/nodes__business_entity__property__primaryContact": "", "@sage/xtrem-master-data/nodes__business_entity__property__siret": "", "@sage/xtrem-master-data/nodes__business_entity__property__site": "", "@sage/xtrem-master-data/nodes__business_entity__property__supplier": "", "@sage/xtrem-master-data/nodes__business_entity__property__taxIdNumber": "", "@sage/xtrem-master-data/nodes__business_entity__property__website": "", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__business_entity_address__node_name": "", "@sage/xtrem-master-data/nodes__business_entity_address__property__businessEntity": "", "@sage/xtrem-master-data/nodes__business_entity_address__property__concatenatedAddress": "", "@sage/xtrem-master-data/nodes__business_entity_address__property__contacts": "", "@sage/xtrem-master-data/nodes__business_entity_address__property__deliveryDetail": "", "@sage/xtrem-master-data/nodes__business_entity_address__property__isPrimary": "", "@sage/xtrem-master-data/nodes__business_entity_address__property__primaryContact": "", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__business_entity_contact__node_name": "", "@sage/xtrem-master-data/nodes__business_entity_contact__property__address": "", "@sage/xtrem-master-data/nodes__business_entity_contact__property__businessEntity": "", "@sage/xtrem-master-data/nodes__business_entity_contact__property__isPrimary": "", "@sage/xtrem-master-data/nodes__business-entity-type-control-customer": "", "@sage/xtrem-master-data/nodes__business-entity-type-control-supplier": "", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__capability_level__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__capability_level__node_name": "", "@sage/xtrem-master-data/nodes__capability_level__property__description": "", "@sage/xtrem-master-data/nodes__capability_level__property__id": "", "@sage/xtrem-master-data/nodes__capability_level__property__level": "", "@sage/xtrem-master-data/nodes__capability_level__property__name": "", "@sage/xtrem-master-data/nodes__company__address_mandatory": "", "@sage/xtrem-master-data/nodes__company__primary_address": "", "@sage/xtrem-master-data/nodes__company__primary_address_mandatory": "", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__company_address__node_name": "", "@sage/xtrem-master-data/nodes__company_address__property__company": "", "@sage/xtrem-master-data/nodes__company_address__property__contacts": "", "@sage/xtrem-master-data/nodes__company_address__property__isPrimary": "", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__company_contact__node_name": "", "@sage/xtrem-master-data/nodes__company_contact__property__address": "", "@sage/xtrem-master-data/nodes__company_contact__property__company": "", "@sage/xtrem-master-data/nodes__company_contact__property__isPrimary": "", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__contact__node_name": "", "@sage/xtrem-master-data/nodes__contact__property__email": "", "@sage/xtrem-master-data/nodes__contact__property__firstName": "", "@sage/xtrem-master-data/nodes__contact__property__image": "", "@sage/xtrem-master-data/nodes__contact__property__lastName": "", "@sage/xtrem-master-data/nodes__contact__property__locationPhoneNumber": "", "@sage/xtrem-master-data/nodes__contact__property__position": "", "@sage/xtrem-master-data/nodes__contact__property__preferredName": "", "@sage/xtrem-master-data/nodes__contact__property__role": "", "@sage/xtrem-master-data/nodes__contact__property__title": "", "@sage/xtrem-master-data/nodes__contact_base__node_name": "", "@sage/xtrem-master-data/nodes__contact_base__not-a-valid-email": "", "@sage/xtrem-master-data/nodes__contact_base__property__contact": "", "@sage/xtrem-master-data/nodes__contact_base__property__displayName": "", "@sage/xtrem-master-data/nodes__contact_base__property__email": "", "@sage/xtrem-master-data/nodes__contact_base__property__firstName": "", "@sage/xtrem-master-data/nodes__contact_base__property__image": "", "@sage/xtrem-master-data/nodes__contact_base__property__isActive": "", "@sage/xtrem-master-data/nodes__contact_base__property__lastName": "", "@sage/xtrem-master-data/nodes__contact_base__property__locationPhoneNumber": "", "@sage/xtrem-master-data/nodes__contact_base__property__position": "", "@sage/xtrem-master-data/nodes__contact_base__property__preferredName": "", "@sage/xtrem-master-data/nodes__contact_base__property__role": "", "@sage/xtrem-master-data/nodes__contact_base__property__title": "", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__container__node_name": "", "@sage/xtrem-master-data/nodes__container__property__consumedLocationCapacity": "", "@sage/xtrem-master-data/nodes__container__property__id": "", "@sage/xtrem-master-data/nodes__container__property__isActive": "", "@sage/xtrem-master-data/nodes__container__property__isInternal": "", "@sage/xtrem-master-data/nodes__container__property__isSingleItem": "", "@sage/xtrem-master-data/nodes__container__property__isSingleLot": "", "@sage/xtrem-master-data/nodes__container__property__labelFormat": "", "@sage/xtrem-master-data/nodes__container__property__name": "", "@sage/xtrem-master-data/nodes__container__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__container__property__storageCapacity": "", "@sage/xtrem-master-data/nodes__container__property__type": "", "@sage/xtrem-master-data/nodes__container__sequence-number-not-required": "", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__cost_category__node_name": "", "@sage/xtrem-master-data/nodes__cost_category__property__costCategoryType": "", "@sage/xtrem-master-data/nodes__cost_category__property__id": "", "@sage/xtrem-master-data/nodes__cost_category__property__isMandatory": "", "@sage/xtrem-master-data/nodes__cost_category__property__name": "", "@sage/xtrem-master-data/nodes__cost-category__can-have-only-one-cost-type-of": "", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__currency__deleting-record": "", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate": "", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__base": "", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__dateRate": "", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__destination": "", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__rate": "", "@sage/xtrem-master-data/nodes__currency__node_name": "", "@sage/xtrem-master-data/nodes__currency__property__currentExchangeRates": "", "@sage/xtrem-master-data/nodes__currency__property__decimalDigits": "", "@sage/xtrem-master-data/nodes__currency__property__exchangeRates": "", "@sage/xtrem-master-data/nodes__currency__property__exchangeRatesDestinationInverse": "", "@sage/xtrem-master-data/nodes__currency__property__icon": "", "@sage/xtrem-master-data/nodes__currency__property__id": "", "@sage/xtrem-master-data/nodes__currency__property__isActive": "", "@sage/xtrem-master-data/nodes__currency__property__lastUpdate": "", "@sage/xtrem-master-data/nodes__currency__property__name": "", "@sage/xtrem-master-data/nodes__currency__property__rounding": "", "@sage/xtrem-master-data/nodes__currency__property__symbol": "", "@sage/xtrem-master-data/nodes__currency_id": "", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__customer__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__customer__node_name": "", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address": "", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address_mandatory": "", "@sage/xtrem-master-data/nodes__customer__primary_ship_to_address_mandatory": "", "@sage/xtrem-master-data/nodes__customer__property__billToAddress": "", "@sage/xtrem-master-data/nodes__customer__property__billToCustomer": "", "@sage/xtrem-master-data/nodes__customer__property__category": "", "@sage/xtrem-master-data/nodes__customer__property__creditLimit": "", "@sage/xtrem-master-data/nodes__customer__property__deliveryAddresses": "", "@sage/xtrem-master-data/nodes__customer__property__displayStatus": "", "@sage/xtrem-master-data/nodes__customer__property__isOnHold": "", "@sage/xtrem-master-data/nodes__customer__property__itemPrices": "", "@sage/xtrem-master-data/nodes__customer__property__items": "", "@sage/xtrem-master-data/nodes__customer__property__payByAddress": "", "@sage/xtrem-master-data/nodes__customer__property__payByCustomer": "", "@sage/xtrem-master-data/nodes__customer__property__paymentTerm": "", "@sage/xtrem-master-data/nodes__customer__property__primaryShipToAddress": "", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__customer_price_reason__node_name": "", "@sage/xtrem-master-data/nodes__customer_price_reason__property__description": "", "@sage/xtrem-master-data/nodes__customer_price_reason__property__id": "", "@sage/xtrem-master-data/nodes__customer_price_reason__property__isActive": "", "@sage/xtrem-master-data/nodes__customer_price_reason__property__name": "", "@sage/xtrem-master-data/nodes__customer_price_reason__property__priority": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__change_not_possible_category_is_used_on_customer": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__node_name": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__id": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isCustomer": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSequenceNumberManagement": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSupplier": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__name": "", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__customer-minimum__order_amount-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__customer-supplier-category__customer_or_supplier": "", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-cannot-be-set": "", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-is-mandatory": "", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__daily_shift__no_details_on_full_day_shift": "", "@sage/xtrem-master-data/nodes__daily_shift__no_overlap_on_shift_details": "", "@sage/xtrem-master-data/nodes__daily_shift__node_name": "", "@sage/xtrem-master-data/nodes__daily_shift__property__capacity": "", "@sage/xtrem-master-data/nodes__daily_shift__property__formattedCapacity": "", "@sage/xtrem-master-data/nodes__daily_shift__property__id": "", "@sage/xtrem-master-data/nodes__daily_shift__property__isFullDay": "", "@sage/xtrem-master-data/nodes__daily_shift__property__name": "", "@sage/xtrem-master-data/nodes__daily_shift__property__shiftDetails": "", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__daily_shift_detail__node_name": "", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__dailyShift": "", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__shiftDetail": "", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__delivery_detail__node_name": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__address": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__incoterm": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isActive": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isFridayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isMondayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isPrimary": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSaturdayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSundayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isThursdayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isTuesdayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__isWednesdayWorkDay": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__leadTime": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__mode": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__shipmentSite": "", "@sage/xtrem-master-data/nodes__delivery_detail__property__workDaysSelection": "", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__delivery_mode__node_name": "", "@sage/xtrem-master-data/nodes__delivery_mode__property__description": "", "@sage/xtrem-master-data/nodes__delivery_mode__property__id": "", "@sage/xtrem-master-data/nodes__delivery_mode__property__isActive": "", "@sage/xtrem-master-data/nodes__delivery_mode__property__name": "", "@sage/xtrem-master-data/nodes__detailed_resource__node_name": "", "@sage/xtrem-master-data/nodes__detailed_resource__property__efficiency": "", "@sage/xtrem-master-data/nodes__detailed_resource__property__location": "", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceCostCategories": "", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceGroup": "", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__dev_tools__node_name": "", "@sage/xtrem-master-data/nodes__dev_tools__property__id": "", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__employee__node_name": "", "@sage/xtrem-master-data/nodes__employee__property__firstName": "", "@sage/xtrem-master-data/nodes__employee__property__id": "", "@sage/xtrem-master-data/nodes__employee__property__image": "", "@sage/xtrem-master-data/nodes__employee__property__lastName": "", "@sage/xtrem-master-data/nodes__employee__property__name": "", "@sage/xtrem-master-data/nodes__employee__property__site": "", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__exchange_rate__node_name": "", "@sage/xtrem-master-data/nodes__exchange_rate__property__base": "", "@sage/xtrem-master-data/nodes__exchange_rate__property__dateRate": "", "@sage/xtrem-master-data/nodes__exchange_rate__property__destination": "", "@sage/xtrem-master-data/nodes__exchange_rate__property__divisor": "", "@sage/xtrem-master-data/nodes__exchange_rate__property__rate": "", "@sage/xtrem-master-data/nodes__exchange_rate__property__shortDescription": "", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate": "", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__amount": "", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__base": "", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__destination": "", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__rateDate": "", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_naf": "", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_rcs": "", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_siren": "", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__ghs_classification__node_name": "", "@sage/xtrem-master-data/nodes__ghs_classification__property__hazard": "", "@sage/xtrem-master-data/nodes__ghs_classification__property__id": "", "@sage/xtrem-master-data/nodes__ghs_classification__property__name": "", "@sage/xtrem-master-data/nodes__ghs_classification__property__pictogram": "", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__group_resource__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__group_resource__node_name": "", "@sage/xtrem-master-data/nodes__group_resource__property__efficiency": "", "@sage/xtrem-master-data/nodes__group_resource__property__minCapabilityLevel": "", "@sage/xtrem-master-data/nodes__group_resource__property__replacements": "", "@sage/xtrem-master-data/nodes__group_resource__property__resources": "", "@sage/xtrem-master-data/nodes__group_resource__property__type": "", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__incoterm__node_name": "", "@sage/xtrem-master-data/nodes__incoterm__property__description": "", "@sage/xtrem-master-data/nodes__incoterm__property__id": "", "@sage/xtrem-master-data/nodes__incoterm__property__isActive": "", "@sage/xtrem-master-data/nodes__incoterm__property__name": "", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__indirect_cost_origin__node_name": "", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__id": "", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__name": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__node_name": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__calculationMethod": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__id": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__lines": "", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__name": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__node_name": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostOrigin": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostSection": "", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__percentage": "", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item__bom_revision_sequence_number_not_managed": "", "@sage/xtrem-master-data/nodes__item__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__item__cannot_change_purchased_property_supplier_exists": "", "@sage/xtrem-master-data/nodes__item__cannot_change_sold_property_customers_exists": "", "@sage/xtrem-master-data/nodes__item__commodity_code_format": "", "@sage/xtrem-master-data/nodes__item__density-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item__expiration_mangement_cannot_be_enabled_without_lot_management": "", "@sage/xtrem-master-data/nodes__item__mandatory_property": "", "@sage/xtrem-master-data/nodes__item__min-price-cannot-be-set-if-no-currency-is-defined": "", "@sage/xtrem-master-data/nodes__item__node_name": "", "@sage/xtrem-master-data/nodes__item__not-a-volume-measure": "", "@sage/xtrem-master-data/nodes__item__not-a-weight-measure": "", "@sage/xtrem-master-data/nodes__item__price-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item__price-cannot-be-set-if-no-currency-is-defined": "", "@sage/xtrem-master-data/nodes__item__property__allergens": "", "@sage/xtrem-master-data/nodes__item__property__basePrice": "", "@sage/xtrem-master-data/nodes__item__property__bomRevisionSequenceNumber": "", "@sage/xtrem-master-data/nodes__item__property__capacity": "", "@sage/xtrem-master-data/nodes__item__property__category": "", "@sage/xtrem-master-data/nodes__item__property__classifications": "", "@sage/xtrem-master-data/nodes__item__property__commodityCode": "", "@sage/xtrem-master-data/nodes__item__property__currency": "", "@sage/xtrem-master-data/nodes__item__property__customerPrices": "", "@sage/xtrem-master-data/nodes__item__property__customers": "", "@sage/xtrem-master-data/nodes__item__property__density": "", "@sage/xtrem-master-data/nodes__item__property__description": "", "@sage/xtrem-master-data/nodes__item__property__eanNumber": "", "@sage/xtrem-master-data/nodes__item__property__id": "", "@sage/xtrem-master-data/nodes__item__property__image": "", "@sage/xtrem-master-data/nodes__item__property__isActive": "", "@sage/xtrem-master-data/nodes__item__property__isBomRevisionManaged": "", "@sage/xtrem-master-data/nodes__item__property__isBought": "", "@sage/xtrem-master-data/nodes__item__property__isExpiryManaged": "", "@sage/xtrem-master-data/nodes__item__property__isManufactured": "", "@sage/xtrem-master-data/nodes__item__property__isPhantom": "", "@sage/xtrem-master-data/nodes__item__property__isPotencyManagement": "", "@sage/xtrem-master-data/nodes__item__property__isSold": "", "@sage/xtrem-master-data/nodes__item__property__isStockManaged": "", "@sage/xtrem-master-data/nodes__item__property__isTraceabilityManagement": "", "@sage/xtrem-master-data/nodes__item__property__itemSites": "", "@sage/xtrem-master-data/nodes__item__property__lotManagement": "", "@sage/xtrem-master-data/nodes__item__property__lotSequenceNumber": "", "@sage/xtrem-master-data/nodes__item__property__maximumSalesQuantity": "", "@sage/xtrem-master-data/nodes__item__property__minimumPrice": "", "@sage/xtrem-master-data/nodes__item__property__minimumSalesQuantity": "", "@sage/xtrem-master-data/nodes__item__property__name": "", "@sage/xtrem-master-data/nodes__item__property__purchaseUnit": "", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversion": "", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversionDedicated": "", "@sage/xtrem-master-data/nodes__item__property__salesUnit": "", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversion": "", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversionDedicated": "", "@sage/xtrem-master-data/nodes__item__property__serialNumberManagement": "", "@sage/xtrem-master-data/nodes__item__property__serialNumberSequenceNumber": "", "@sage/xtrem-master-data/nodes__item__property__serialNumberUsage": "", "@sage/xtrem-master-data/nodes__item__property__status": "", "@sage/xtrem-master-data/nodes__item__property__stockUnit": "", "@sage/xtrem-master-data/nodes__item__property__supplierPrices": "", "@sage/xtrem-master-data/nodes__item__property__suppliers": "", "@sage/xtrem-master-data/nodes__item__property__type": "", "@sage/xtrem-master-data/nodes__item__property__useSupplierSerialNumbers": "", "@sage/xtrem-master-data/nodes__item__property__volume": "", "@sage/xtrem-master-data/nodes__item__property__volumeUnit": "", "@sage/xtrem-master-data/nodes__item__property__weight": "", "@sage/xtrem-master-data/nodes__item__property__weightUnit": "", "@sage/xtrem-master-data/nodes__item__purchase_unit_not_0_decimal_places": "", "@sage/xtrem-master-data/nodes__item__sales_unit_not_0_decimal_places": "", "@sage/xtrem-master-data/nodes__item__standard_unit_of_measure_not_changeable": "", "@sage/xtrem-master-data/nodes__item__stock_unit_not_0_decimal_places": "", "@sage/xtrem-master-data/nodes__item__the_lot_and_serial_number_cannot_use_the_same_sequence_number": "", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_lot": "", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_serial_number": "", "@sage/xtrem-master-data/nodes__item__volume-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item__weight-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_allergen__node_name": "", "@sage/xtrem-master-data/nodes__item_allergen__property__allergen": "", "@sage/xtrem-master-data/nodes__item_allergen__property__item": "", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used": "", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used_on_supplier": "", "@sage/xtrem-master-data/nodes__item_category__node_name": "", "@sage/xtrem-master-data/nodes__item_category__property__id": "", "@sage/xtrem-master-data/nodes__item_category__property__isSequenceNumberManagement": "", "@sage/xtrem-master-data/nodes__item_category__property__name": "", "@sage/xtrem-master-data/nodes__item_category__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__item_category__property__type": "", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_classifications__node_name": "", "@sage/xtrem-master-data/nodes__item_classifications__property__classification": "", "@sage/xtrem-master-data/nodes__item_classifications__property__item": "", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_customer__node_name": "", "@sage/xtrem-master-data/nodes__item_customer__property__customer": "", "@sage/xtrem-master-data/nodes__item_customer__property__id": "", "@sage/xtrem-master-data/nodes__item_customer__property__isActive": "", "@sage/xtrem-master-data/nodes__item_customer__property__item": "", "@sage/xtrem-master-data/nodes__item_customer__property__maximumSalesQuantity": "", "@sage/xtrem-master-data/nodes__item_customer__property__minimumSalesQuantity": "", "@sage/xtrem-master-data/nodes__item_customer__property__name": "", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnit": "", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnitToStockUnitConversion": "", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_customer_price__node_name": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__charge": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__currency": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__customer": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__discount": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__endDate": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__fromQuantity": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__isActive": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__item": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__price": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__priceReason": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__salesSite": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__startDate": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__stockSite": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__toQuantity": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__unit": "", "@sage/xtrem-master-data/nodes__item_customer_price__property__validUnits": "", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice": "", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__parameter__priceParameters": "", "@sage/xtrem-master-data/nodes__item_not_active": "", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_site__deletion_forbidden": "", "@sage/xtrem-master-data/nodes__item_site__node_name": "", "@sage/xtrem-master-data/nodes__item_site__property__batchQuantity": "", "@sage/xtrem-master-data/nodes__item_site__property__completedProductDefaultLocation": "", "@sage/xtrem-master-data/nodes__item_site__property__costs": "", "@sage/xtrem-master-data/nodes__item_site__property__defaultSupplier": "", "@sage/xtrem-master-data/nodes__item_site__property__economicOrderQuantity": "", "@sage/xtrem-master-data/nodes__item_site__property__expectedQuantity": "", "@sage/xtrem-master-data/nodes__item_site__property__id": "", "@sage/xtrem-master-data/nodes__item_site__property__inboundDefaultLocation": "", "@sage/xtrem-master-data/nodes__item_site__property__indirectCostSection": "", "@sage/xtrem-master-data/nodes__item_site__property__isOrderToOrder": "", "@sage/xtrem-master-data/nodes__item_site__property__item": "", "@sage/xtrem-master-data/nodes__item_site__property__itemSiteCost": "", "@sage/xtrem-master-data/nodes__item_site__property__outboundDefaultLocation": "", "@sage/xtrem-master-data/nodes__item_site__property__preferredProcess": "", "@sage/xtrem-master-data/nodes__item_site__property__prodLeadTime": "", "@sage/xtrem-master-data/nodes__item_site__property__purchaseLeadTime": "", "@sage/xtrem-master-data/nodes__item_site__property__reorderPoint": "", "@sage/xtrem-master-data/nodes__item_site__property__replenishmentMethod": "", "@sage/xtrem-master-data/nodes__item_site__property__requiredQuantity": "", "@sage/xtrem-master-data/nodes__item_site__property__safetyStock": "", "@sage/xtrem-master-data/nodes__item_site__property__site": "", "@sage/xtrem-master-data/nodes__item_site__property__stdCostValue": "", "@sage/xtrem-master-data/nodes__item_site__property__stockUnit": "", "@sage/xtrem-master-data/nodes__item_site__property__suppliers": "", "@sage/xtrem-master-data/nodes__item_site__property__valuationMethod": "", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite": "", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__parameter__searchCriteria": "", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_site_cost__node_name": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__costCategory": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__forQuantity": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__fromDate": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__indirectCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__isCalculated": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__isUpdatingPreviousCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__itemSite": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__laborCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__machineCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__materialCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__stockUnit": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__toDate": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__toolCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__totalCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__unitCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__property__version": "", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost": "", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__effectiveDate": "", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__item": "", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__site": "", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_site_supplier__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__item_site_supplier__node_name": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__isDefaultItemSupplier": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__item": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSite": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSupplier": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__minimumPurchaseOrderQuantity": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__priority": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseLeadTime": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseUnit": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__site": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__supplier": "", "@sage/xtrem-master-data/nodes__item_site_supplier__property__uStoredPriority": "", "@sage/xtrem-master-data/nodes__item_site_supplier__suppliers-dont-match": "", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_supplier__node_name": "", "@sage/xtrem-master-data/nodes__item_supplier__property__id": "", "@sage/xtrem-master-data/nodes__item_supplier__property__isActive": "", "@sage/xtrem-master-data/nodes__item_supplier__property__isDefaultItemSupplier": "", "@sage/xtrem-master-data/nodes__item_supplier__property__item": "", "@sage/xtrem-master-data/nodes__item_supplier__property__minimumPurchaseQuantity": "", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseLeadTime": "", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseUnitOfMeasure": "", "@sage/xtrem-master-data/nodes__item_supplier__property__supplier": "", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemCode": "", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemName": "", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierPriority": "", "@sage/xtrem-master-data/nodes__item_supplier__purchase_unit_forbidden": "", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__item_supplier_price__node_name": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__currency": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValid": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidFrom": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidTo": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__fromQuantity": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__item": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__itemSupplier": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__price": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__priority": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__site": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__supplier": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__toQuantity": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__type": "", "@sage/xtrem-master-data/nodes__item_supplier_price__property__unit": "", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice": "", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__parameter__priceParameters": "", "@sage/xtrem-master-data/nodes__item-category__sequence-number-cannot-be-set": "", "@sage/xtrem-master-data/nodes__item-category__sequence-number-is-mandatory": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_infinite_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_specific_to_infinite": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate-greater-than-toDate": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity-greater-than-toQuantity": "", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_infinite_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_specific_to_infinite": "", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__startDate-greater-than-endDate": "", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-range": "", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-unit-of-measure": "", "@sage/xtrem-master-data/nodes__item-price__price-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item-price__priority-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item-price__quantity-cannot-be-negative": "", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory": "", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-purchase-site": "", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-or-purchase-site": "", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-site": "", "@sage/xtrem-master-data/nodes__item-site__valuation-method-must-be-standard-cost": "", "@sage/xtrem-master-data/nodes__item-site-cost__calculated-cost-only-for-manufactured-item": "", "@sage/xtrem-master-data/nodes__item-site-cost__failed_deletion_impossible_if_before_today": "", "@sage/xtrem-master-data/nodes__item-site-cost__failed_update_impossible": "", "@sage/xtrem-master-data/nodes__item-site-cost__failed-from-date-already-set": "", "@sage/xtrem-master-data/nodes__item-site-cost__failed-updating-previous-cost": "", "@sage/xtrem-master-data/nodes__item-site-cost__incorrect-key-changes": "", "@sage/xtrem-master-data/nodes__item-site-cost__labor-cost-only-for-manufactured-item": "", "@sage/xtrem-master-data/nodes__item-site-cost__material-cost-only-for-manufactured-item": "", "@sage/xtrem-master-data/nodes__item-site-cost__tool-cost-only-for-manufactured-item": "", "@sage/xtrem-master-data/nodes__item-supplier-price__fromQuantity_improper_range": "", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__labor_capability__node_name": "", "@sage/xtrem-master-data/nodes__labor_capability__property__labor": "", "@sage/xtrem-master-data/nodes__labor_capability__property__machine": "", "@sage/xtrem-master-data/nodes__labor_capability__property__service": "", "@sage/xtrem-master-data/nodes__labor_capability__property__tool": "", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__labor_resource__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__labor_resource__node_name": "", "@sage/xtrem-master-data/nodes__labor_resource__property__capabilities": "", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers": "", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__containerId": "", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleItem": "", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleLot": "", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__locationId": "", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__numberToCreate": "", "@sage/xtrem-master-data/nodes__license_plate_number__node_name": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__consumedCapacity": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__container": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleItem": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleLot": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__location": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__number": "", "@sage/xtrem-master-data/nodes__license_plate_number__property__owner": "", "@sage/xtrem-master-data/nodes__license-plate-number__location-required-for-automatic-number-generation": "", "@sage/xtrem-master-data/nodes__license-plate-number__no-default-sequence": "", "@sage/xtrem-master-data/nodes__license-plate-number__owner-not-required": "", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__location__location_category_virtual_not_allowed": "", "@sage/xtrem-master-data/nodes__location__location_zone_virtual_not_allowed": "", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations": "", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locations": "", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locationSequence": "", "@sage/xtrem-master-data/nodes__location__mutation__getLocations": "", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__locationSequence": "", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__requiredCombinations": "", "@sage/xtrem-master-data/nodes__location__node_name": "", "@sage/xtrem-master-data/nodes__location__property__dangerousGoodAllowed": "", "@sage/xtrem-master-data/nodes__location__property__id": "", "@sage/xtrem-master-data/nodes__location__property__isActive": "", "@sage/xtrem-master-data/nodes__location__property__isVirtualAllowed": "", "@sage/xtrem-master-data/nodes__location__property__locationType": "", "@sage/xtrem-master-data/nodes__location__property__locationZone": "", "@sage/xtrem-master-data/nodes__location__property__name": "", "@sage/xtrem-master-data/nodes__location__property__site": "", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__location_sequence__node_name": "", "@sage/xtrem-master-data/nodes__location_sequence__property__components": "", "@sage/xtrem-master-data/nodes__location_sequence__property__lastSequenceUsed": "", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsRemaining": "", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsUsed": "", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__location_sequence_component__node_name": "", "@sage/xtrem-master-data/nodes__location_sequence_component__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__location_type__node_name": "", "@sage/xtrem-master-data/nodes__location_type__property__description": "", "@sage/xtrem-master-data/nodes__location_type__property__id": "", "@sage/xtrem-master-data/nodes__location_type__property__isVirtualAllowed": "", "@sage/xtrem-master-data/nodes__location_type__property__locationCategory": "", "@sage/xtrem-master-data/nodes__location_type__property__name": "", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__location_zone__node_name": "", "@sage/xtrem-master-data/nodes__location_zone__property__id": "", "@sage/xtrem-master-data/nodes__location_zone__property__isVirtualAllowed": "", "@sage/xtrem-master-data/nodes__location_zone__property__locations": "", "@sage/xtrem-master-data/nodes__location_zone__property__name": "", "@sage/xtrem-master-data/nodes__location_zone__property__site": "", "@sage/xtrem-master-data/nodes__location_zone__property__zoneType": "", "@sage/xtrem-master-data/nodes__location-type__location_category_virtual_not_allowed": "", "@sage/xtrem-master-data/nodes__location-zone__site_modify": "", "@sage/xtrem-master-data/nodes__location-zone__zone_type_virtual_not_allowed": "", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__machine_resource__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__machine_resource__node_name": "", "@sage/xtrem-master-data/nodes__machine_resource__property__contractId": "", "@sage/xtrem-master-data/nodes__machine_resource__property__contractName": "", "@sage/xtrem-master-data/nodes__machine_resource__property__minCapabilityLevel": "", "@sage/xtrem-master-data/nodes__machine_resource__property__model": "", "@sage/xtrem-master-data/nodes__machine_resource__property__serialNumber": "", "@sage/xtrem-master-data/nodes__machine_resource__property__supplier": "", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__payment_term__node_name": "", "@sage/xtrem-master-data/nodes__payment_term__property__businessEntityType": "", "@sage/xtrem-master-data/nodes__payment_term__property__days": "", "@sage/xtrem-master-data/nodes__payment_term__property__description": "", "@sage/xtrem-master-data/nodes__payment_term__property__discountAmount": "", "@sage/xtrem-master-data/nodes__payment_term__property__discountDate": "", "@sage/xtrem-master-data/nodes__payment_term__property__discountFrom": "", "@sage/xtrem-master-data/nodes__payment_term__property__discountType": "", "@sage/xtrem-master-data/nodes__payment_term__property__dueDateType": "", "@sage/xtrem-master-data/nodes__payment_term__property__id": "", "@sage/xtrem-master-data/nodes__payment_term__property__isActive": "", "@sage/xtrem-master-data/nodes__payment_term__property__name": "", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyAmount": "", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyType": "", "@sage/xtrem-master-data/nodes__payment_term_discount_amount_percentage_error": "", "@sage/xtrem-master-data/nodes__payment_term_discount_date_should_be_before_due_date": "", "@sage/xtrem-master-data/nodes__payment_term_discount_from_needs_to_match_due_date_type": "", "@sage/xtrem-master-data/nodes__payment_term_discount_mandatory": "", "@sage/xtrem-master-data/nodes__payment_term_penalty_amount_percentage_error": "", "@sage/xtrem-master-data/nodes__range_sequence_component__node_name": "", "@sage/xtrem-master-data/nodes__range_sequence_component__property__endValue": "", "@sage/xtrem-master-data/nodes__range_sequence_component__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__range_sequence_component__property__startValue": "", "@sage/xtrem-master-data/nodes__range_sequence_number__last_sequence_used_not_found": "", "@sage/xtrem-master-data/nodes__range_sequence_number__no_combinations_found": "", "@sage/xtrem-master-data/nodes__range_sequence_number__node_name": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__components": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__definitionLevel": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__isChronological": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__legislation": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__numberOfCombinations": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__rtzLevel": "", "@sage/xtrem-master-data/nodes__range_sequence_number__property__type": "", "@sage/xtrem-master-data/nodes__range_sequence_number__required_combinations_must_be_greater_than_zero": "", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__reason_code__node_name": "", "@sage/xtrem-master-data/nodes__reason_code__property__id": "", "@sage/xtrem-master-data/nodes__reason_code__property__isActive": "", "@sage/xtrem-master-data/nodes__reason_code__property__name": "", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__resource_cost_category__node_name": "", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costCategory": "", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costUnit": "", "@sage/xtrem-master-data/nodes__resource_cost_category__property__indirectCostSection": "", "@sage/xtrem-master-data/nodes__resource_cost_category__property__resource": "", "@sage/xtrem-master-data/nodes__resource_cost_category__property__runCost": "", "@sage/xtrem-master-data/nodes__resource_cost_category__property__setupCost": "", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__resource_group_replacement__node_name": "", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__replacement": "", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__resourceGroup": "", "@sage/xtrem-master-data/nodes__resource-cost-category__the-cost-category-is-mandatory": "", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number__property__components": "", "@sage/xtrem-master-data/nodes__sequence_number__property__id": "", "@sage/xtrem-master-data/nodes__sequence_number__property__values": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_company_mandatory": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_site_mandatory": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__only_allowed_for_sales_invoice_credit_memo_fr_legislation": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__company": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__currentLegislationId": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isActive": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isAssignOnPosting": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isDefaultAssignment": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isUsed": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__legislation": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumberAssignmentDocumentType": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__site": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__displayOrder": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeFactory": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeValues": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignmentModule": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignments": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__id": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__name": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__nodes": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__property__modules": "", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number_component__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number_component__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__sequence_number_value__node_name": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__additionalInfo": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__company": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__period": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__periodDate": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceNumber": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceValue": "", "@sage/xtrem-master-data/nodes__sequence_number_value__property__site": "", "@sage/xtrem-master-data/nodes__sequence-number__length_exceed": "", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__shift_detail__node_name": "", "@sage/xtrem-master-data/nodes__shift_detail__property__duration": "", "@sage/xtrem-master-data/nodes__shift_detail__property__formattedDuration": "", "@sage/xtrem-master-data/nodes__shift_detail__property__id": "", "@sage/xtrem-master-data/nodes__shift_detail__property__name": "", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftEnd": "", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftStart": "", "@sage/xtrem-master-data/nodes__site__create_new_site": "", "@sage/xtrem-master-data/nodes__site__create_new_site_fail": "", "@sage/xtrem-master-data/nodes__site_extension__corporation_and_site": "", "@sage/xtrem-master-data/nodes__site_extension__current_site_is_financial_site": "", "@sage/xtrem-master-data/nodes__site_extension__financial_site_mandatory": "", "@sage/xtrem-master-data/nodes__site_extension__invalid_timezone": "", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__standard__node_name": "", "@sage/xtrem-master-data/nodes__standard__property__code": "", "@sage/xtrem-master-data/nodes__standard__property__id": "", "@sage/xtrem-master-data/nodes__standard__property__industrySector": "", "@sage/xtrem-master-data/nodes__standard__property__name": "", "@sage/xtrem-master-data/nodes__standard__property__sdo": "", "@sage/xtrem-master-data/nodes__standard__property__version": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__node_name": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__legislation": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicCode": "", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicDescription": "", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__supplier__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__supplier__create_new_supplier_fail": "", "@sage/xtrem-master-data/nodes__supplier__node_name": "", "@sage/xtrem-master-data/nodes__supplier__property__billByAddress": "", "@sage/xtrem-master-data/nodes__supplier__property__billBySupplier": "", "@sage/xtrem-master-data/nodes__supplier__property__category": "", "@sage/xtrem-master-data/nodes__supplier__property__certificates": "", "@sage/xtrem-master-data/nodes__supplier__property__deliveryMode": "", "@sage/xtrem-master-data/nodes__supplier__property__incoterm": "", "@sage/xtrem-master-data/nodes__supplier__property__itemPrices": "", "@sage/xtrem-master-data/nodes__supplier__property__items": "", "@sage/xtrem-master-data/nodes__supplier__property__parent": "", "@sage/xtrem-master-data/nodes__supplier__property__paymentMethod": "", "@sage/xtrem-master-data/nodes__supplier__property__paymentTerm": "", "@sage/xtrem-master-data/nodes__supplier__property__payToAddress": "", "@sage/xtrem-master-data/nodes__supplier__property__payToSupplier": "", "@sage/xtrem-master-data/nodes__supplier__property__returnToAddress": "", "@sage/xtrem-master-data/nodes__supplier__property__returnToSupplier": "", "@sage/xtrem-master-data/nodes__supplier__property__standardIndustrialClassification": "", "@sage/xtrem-master-data/nodes__supplier__property__supplierType": "", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier": "", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__item": "", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__site": "", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate": "", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__certificate": "", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__newCertificateDate": "", "@sage/xtrem-master-data/nodes__supplier_certificate__node_name": "", "@sage/xtrem-master-data/nodes__supplier_certificate__property__supplier": "", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__team__node_name": "", "@sage/xtrem-master-data/nodes__team__property__description": "", "@sage/xtrem-master-data/nodes__team__property__id": "", "@sage/xtrem-master-data/nodes__team__property__name": "", "@sage/xtrem-master-data/nodes__team__property__site": "", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__tool_resource__bulkMutation__bulkDelete": "", "@sage/xtrem-master-data/nodes__tool_resource__node_name": "", "@sage/xtrem-master-data/nodes__tool_resource__property__consumptionMode": "", "@sage/xtrem-master-data/nodes__tool_resource__property__hoursTracked": "", "@sage/xtrem-master-data/nodes__tool_resource__property__item": "", "@sage/xtrem-master-data/nodes__tool_resource__property__quantity": "", "@sage/xtrem-master-data/nodes__tool_resource__property__unitProduced": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__item": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__type": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__flow_without_item": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_delete": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_property_update": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_update": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__item__type": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__purchase__customer": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__sales__supplier": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__customer": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__item": "", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__type": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__node_name": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__coefficient": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__customer": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__fromUnit": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__isStandard": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__item": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__supplier": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__toUnit": "", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__type": "", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__unit_of_measure__decrease_not_possible_unit_of_measure_is_used": "", "@sage/xtrem-master-data/nodes__unit_of_measure__node_name": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__conversionFactor": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__decimalDigits": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__description": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__id": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__isActive": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__name": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__symbol": "", "@sage/xtrem-master-data/nodes__unit_of_measure__property__type": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__conversionFactor": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__customer": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__formatToUnitDecimalDigits": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__fromUnit": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__item": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__quantity": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__supplier": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__toUnit": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__type": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__item": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__supplier": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__customer": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__fromUnit": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__item": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__supplier": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__toUnit": "", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__type": "", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__version_information__node_name": "", "@sage/xtrem-master-data/nodes__version_information__property__text": "", "@sage/xtrem-master-data/nodes__version_information__property__version": "", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport": "", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__filter": "", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__id": "", "@sage/xtrem-master-data/nodes__weekly_shift__no_daily_shift_on_full_week_shift": "", "@sage/xtrem-master-data/nodes__weekly_shift__node_name": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__capacity": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__formattedCapacity": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__fridayShift": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__id": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__isFullWeek": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__mondayShift": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__name": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__saturdayShift": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__sundayShift": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__thursdayShift": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__tuesdayShift": "", "@sage/xtrem-master-data/nodes__weekly_shift__property__wednesdayShift": "", "@sage/xtrem-master-data/nodes__work_in_progress__node_name": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__actualQuantity": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentId": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentLine": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentNumber": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentType": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__endDate": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__expectedQuantity": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__item": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentLine": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentType": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__outstandingQuantity": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__remainingQuantityToAllocate": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__site": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__startDate": "", "@sage/xtrem-master-data/nodes__work_in_progress__property__status": "", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite": "", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentDate": "", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentItem": "", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentSite": "", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentStatus": "", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__originDocumentType": "", "@sage/xtrem-master-data/nodes_sequence_number_assignment_company_leg": "", "@sage/xtrem-master-data/nodes_sequence_number_assignment_site_error": "", "@sage/xtrem-master-data/nodes-resource-group-cannot-be-replaced-by-itself": "", "@sage/xtrem-master-data/nodes-resource-group-replacement-resource-group-should-be-replaced-by-another-one-with-same-site": "", "@sage/xtrem-master-data/or": "", "@sage/xtrem-master-data/package__name": "", "@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available": "", "@sage/xtrem-master-data/page__item_customer_price_panel__price_is_zero": "", "@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available": "", "@sage/xtrem-master-data/pages__address_functions__businessentity_primary_active_address_mandatory": "", "@sage/xtrem-master-data/pages__address_functions__company_primary_active_address_mandatory": "", "@sage/xtrem-master-data/pages__address_functions__customer_primary_active_address_mandatory": "", "@sage/xtrem-master-data/pages__address_functions__site_primary_active_address_mandatory": "", "@sage/xtrem-master-data/pages__address_functions__supplier_primary_active_address_mandatory": "", "@sage/xtrem-master-data/pages__address_functions_businessentity_add_new____title": "", "@sage/xtrem-master-data/pages__address_functions_businessentity_address_contacts____title": "", "@sage/xtrem-master-data/pages__address_functions_businessentity_edit____title": "", "@sage/xtrem-master-data/pages__address_panel__edit____title": "", "@sage/xtrem-master-data/pages__address_panel__isPrimary_must_be_active": "", "@sage/xtrem-master-data/pages__address_panel__new____title": "", "@sage/xtrem-master-data/pages__address-contacts_panel_add_new____title": "", "@sage/xtrem-master-data/pages__address-contacts_panel_edit____title": "", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_active": "", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_inactive": "", "@sage/xtrem-master-data/pages__allergen____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__allergen____objectTypePlural": "", "@sage/xtrem-master-data/pages__allergen____objectTypeSingular": "", "@sage/xtrem-master-data/pages__allergen____title": "", "@sage/xtrem-master-data/pages__allergen__allergenImageBlock____title": "", "@sage/xtrem-master-data/pages__allergen__allergenInformationBlock____title": "", "@sage/xtrem-master-data/pages__allergen__generalSection____title": "", "@sage/xtrem-master-data/pages__allergen__name____title": "", "@sage/xtrem-master-data/pages__already_used_message": "", "@sage/xtrem-master-data/pages__already_used_title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__componentLength__title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__isDefault__title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypePlural": "", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypeSingular": "", "@sage/xtrem-master-data/pages__bom_revision_sequence____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__addComponent____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__capital_letters_only": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentLength____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__endValue": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__startValue": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__type": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____dropdownActions__title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentsBlock____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__default": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__digits_only": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__id____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__invalid_range": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__isDefault____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainBlock____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainSection____title": "", "@sage/xtrem-master-data/pages__bom_revision_sequence__name____title": "", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__listItem__line6__title": "", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__business_entity____objectTypePlural": "", "@sage/xtrem-master-data/pages__business_entity____objectTypeSingular": "", "@sage/xtrem-master-data/pages__business_entity____title": "", "@sage/xtrem-master-data/pages__business_entity__address_active": "", "@sage/xtrem-master-data/pages__business_entity__address_inactive": "", "@sage/xtrem-master-data/pages__business_entity__addresses____addButtonText": "", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__contacts": "", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title": "", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__4": "", "@sage/xtrem-master-data/pages__business_entity__addresses____title": "", "@sage/xtrem-master-data/pages__business_entity__addressSection____title": "", "@sage/xtrem-master-data/pages__business_entity__contact_active": "", "@sage/xtrem-master-data/pages__business_entity__contact_inactive": "", "@sage/xtrem-master-data/pages__business_entity__contacts____addButtonText": "", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title": "", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__business_entity__contacts____headerLabel__title": "", "@sage/xtrem-master-data/pages__business_entity__contactSection____title": "", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__id": "", "@sage/xtrem-master-data/pages__business_entity__country____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity__currency____columns__title__id": "", "@sage/xtrem-master-data/pages__business_entity__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity__currency____title": "", "@sage/xtrem-master-data/pages__business_entity__id____title": "", "@sage/xtrem-master-data/pages__business_entity__imageBlock____title": "", "@sage/xtrem-master-data/pages__business_entity__isCustomer____title": "", "@sage/xtrem-master-data/pages__business_entity__isSite____title": "", "@sage/xtrem-master-data/pages__business_entity__isSupplier____title": "", "@sage/xtrem-master-data/pages__business_entity__legalEntity____title": "", "@sage/xtrem-master-data/pages__business_entity__mainBlock____title": "", "@sage/xtrem-master-data/pages__business_entity__mainSection____title": "", "@sage/xtrem-master-data/pages__business_entity__name____title": "", "@sage/xtrem-master-data/pages__business_entity__parent____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity__parent____title": "", "@sage/xtrem-master-data/pages__business_entity__roleBlock____title": "", "@sage/xtrem-master-data/pages__business_entity__save____title": "", "@sage/xtrem-master-data/pages__business_entity__siret____title": "", "@sage/xtrem-master-data/pages__business_entity__taxIdNumber____title": "", "@sage/xtrem-master-data/pages__business_entity__website____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine1____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine2____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__cancel____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__city____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__confirm____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____columns__title__id": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryDetail____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____postfix": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__informationBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__isActiveShippingAddress____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimary____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimaryShippingAddress____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__isShippingAddress____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__locationPhoneNumber____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__name____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingSection____title": "", "@sage/xtrem-master-data/pages__business_entity_address_panel__workDaysSelection____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__cancel____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__confirm____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__email____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__firstName____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isPrimary____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__lastName____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__locationPhoneNumber____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__position____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__preferredName____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__role____title": "", "@sage/xtrem-master-data/pages__business_entity_contact_panel__title____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____subtitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____addButtonText": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__incoterm__name": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isActive": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isPrimary": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__leadTime": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__mode__name": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__4": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity_already_a_customer": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__primary_ship_to_address_mandatory": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____subtitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity_already_a_site": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__isFinance____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____columns__title__isActive": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____subtitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity_already_a_supplier": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalBlock____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalSection____title": "", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__capability_level____objectTypePlural": "", "@sage/xtrem-master-data/pages__capability_level____objectTypeSingular": "", "@sage/xtrem-master-data/pages__capability_level____title": "", "@sage/xtrem-master-data/pages__capability_level__description____title": "", "@sage/xtrem-master-data/pages__capability_level__id____title": "", "@sage/xtrem-master-data/pages__capability_level__level____title": "", "@sage/xtrem-master-data/pages__capability_level__mainBlock____title": "", "@sage/xtrem-master-data/pages__capability_level__mainSection____title": "", "@sage/xtrem-master-data/pages__capability_level__name____title": "", "@sage/xtrem-master-data/pages__capability_panel____title": "", "@sage/xtrem-master-data/pages__capability_panel__cancel____title": "", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____title": "", "@sage/xtrem-master-data/pages__capability_panel__create____title": "", "@sage/xtrem-master-data/pages__capability_panel__dateEndValid____title": "", "@sage/xtrem-master-data/pages__capability_panel__dateStartValid____title": "", "@sage/xtrem-master-data/pages__capability_panel__id____title": "", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__minCapabilityLevel__level": "", "@sage/xtrem-master-data/pages__capability_panel__machine____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__capability_panel__machine____title": "", "@sage/xtrem-master-data/pages__capability_panel__name____title": "", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__category__name": "", "@sage/xtrem-master-data/pages__capability_panel__service____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__capability_panel__service____title": "", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__resourceGroup__name": "", "@sage/xtrem-master-data/pages__capability_panel__tool____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__capability_panel__tool____title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_business_entity_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_company_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_customer_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_site_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_supplier_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_business_entity_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_company_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_customer_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_site_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_supplier_address_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__edit_contact_title": "", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__new_contact_title": "", "@sage/xtrem-master-data/pages__company____add_site": "", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line10__title": "", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line11__title": "", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line8__title": "", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__company____objectTypePlural": "", "@sage/xtrem-master-data/pages__company____objectTypeSingular": "", "@sage/xtrem-master-data/pages__company____title": "", "@sage/xtrem-master-data/pages__company__addresses____addButtonText": "", "@sage/xtrem-master-data/pages__company__addresses____columns__title__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/pages__company__addresses____columns__title__contacts": "", "@sage/xtrem-master-data/pages__company__addresses____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__company__addresses____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title": "", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__4": "", "@sage/xtrem-master-data/pages__company__addresses____title": "", "@sage/xtrem-master-data/pages__company__addressSection____title": "", "@sage/xtrem-master-data/pages__company__contacts____addButtonText": "", "@sage/xtrem-master-data/pages__company__contacts____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__company__contacts____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__company__contacts____columns__title__role": "", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title": "", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__company__contacts____headerLabel__title": "", "@sage/xtrem-master-data/pages__company__contactSection____title": "", "@sage/xtrem-master-data/pages__company__country____columns__title__id": "", "@sage/xtrem-master-data/pages__company__country____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__company__creditLimitBlock____title": "", "@sage/xtrem-master-data/pages__company__currency____columns__title__id": "", "@sage/xtrem-master-data/pages__company__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__company__currency____title": "", "@sage/xtrem-master-data/pages__company__customerOnHoldCheck____title": "", "@sage/xtrem-master-data/pages__company__description____title": "", "@sage/xtrem-master-data/pages__company__id____title": "", "@sage/xtrem-master-data/pages__company__isSequenceNumberIdUsed____title": "", "@sage/xtrem-master-data/pages__company__legalForm____title": "", "@sage/xtrem-master-data/pages__company__legislation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__company__legislation____placeholder": "", "@sage/xtrem-master-data/pages__company__mainBlock____title": "", "@sage/xtrem-master-data/pages__company__mainSection____title": "", "@sage/xtrem-master-data/pages__company__managementSection____title": "", "@sage/xtrem-master-data/pages__company__naf____title": "", "@sage/xtrem-master-data/pages__company__name____title": "", "@sage/xtrem-master-data/pages__company__paymentTrackingBlock____title": "", "@sage/xtrem-master-data/pages__company__rcs____title": "", "@sage/xtrem-master-data/pages__company__save____title": "", "@sage/xtrem-master-data/pages__company__sequenceNumberId____title": "", "@sage/xtrem-master-data/pages__company__siren____title": "", "@sage/xtrem-master-data/pages__company__siteBlock____title": "", "@sage/xtrem-master-data/pages__compare__number_remaining_to__required": "", "@sage/xtrem-master-data/pages__contact_panel__isPrimary_must_be_active": "", "@sage/xtrem-master-data/pages__contact_selection_panel____title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__cancel____title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__email": "", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__firstName": "", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__lastName": "", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__contactSelectionBlock____title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__ok____title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__firstName": "", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__lastName": "", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__title": "", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__consumedLocationCapacity__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isInternal__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleItem__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleLot__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__labelFormat__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__sequenceNumber__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__storageCapacity__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__container____objectTypePlural": "", "@sage/xtrem-master-data/pages__container____objectTypeSingular": "", "@sage/xtrem-master-data/pages__container____title": "", "@sage/xtrem-master-data/pages__container__consumedLocationCapacity____title": "", "@sage/xtrem-master-data/pages__container__id____title": "", "@sage/xtrem-master-data/pages__container__isInternal____title": "", "@sage/xtrem-master-data/pages__container__isSingleItem____title": "", "@sage/xtrem-master-data/pages__container__isSingleLot____title": "", "@sage/xtrem-master-data/pages__container__labelFormat____title": "", "@sage/xtrem-master-data/pages__container__mainSection____title": "", "@sage/xtrem-master-data/pages__container__name____title": "", "@sage/xtrem-master-data/pages__container__sequenceNumber____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__container__sequenceNumber____title": "", "@sage/xtrem-master-data/pages__container__storageCapacity____title": "", "@sage/xtrem-master-data/pages__container__type____title": "", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__cost_category____objectTypePlural": "", "@sage/xtrem-master-data/pages__cost_category____objectTypeSingular": "", "@sage/xtrem-master-data/pages__cost_category____title": "", "@sage/xtrem-master-data/pages__cost_category__costCategoryType____title": "", "@sage/xtrem-master-data/pages__cost_category__id____title": "", "@sage/xtrem-master-data/pages__cost_category__isMandatory____title": "", "@sage/xtrem-master-data/pages__cost_category__mainSection____title": "", "@sage/xtrem-master-data/pages__cost_category__name____title": "", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line6__title": "", "@sage/xtrem-master-data/pages__country____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__country____objectTypePlural": "", "@sage/xtrem-master-data/pages__country____objectTypeSingular": "", "@sage/xtrem-master-data/pages__country____title": "", "@sage/xtrem-master-data/pages__country__continent____title": "", "@sage/xtrem-master-data/pages__country__countryFlagBlock____title": "", "@sage/xtrem-master-data/pages__country__currency____columns__title__id": "", "@sage/xtrem-master-data/pages__country__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__country__currency____title": "", "@sage/xtrem-master-data/pages__country__generalSection____title": "", "@sage/xtrem-master-data/pages__country__id____title": "", "@sage/xtrem-master-data/pages__country__isEuMember____title": "", "@sage/xtrem-master-data/pages__country__iso31661Alpha3____title": "", "@sage/xtrem-master-data/pages__country__legislation____placeholder": "", "@sage/xtrem-master-data/pages__country__legislation____title": "", "@sage/xtrem-master-data/pages__country__mainBlock____title": "", "@sage/xtrem-master-data/pages__country__name____title": "", "@sage/xtrem-master-data/pages__country__regionLabel____title": "", "@sage/xtrem-master-data/pages__country__zipLabel____title": "", "@sage/xtrem-master-data/pages__country_invalid_id": "", "@sage/xtrem-master-data/pages__country_invalid_iso_code": "", "@sage/xtrem-master-data/pages__create_test_data____title": "", "@sage/xtrem-master-data/pages__create_test_data__instructions____content": "", "@sage/xtrem-master-data/pages__create_test_data__linkField____title": "", "@sage/xtrem-master-data/pages__create_test_data__mainBlock____title": "", "@sage/xtrem-master-data/pages__create_test_data__mainSection____title": "", "@sage/xtrem-master-data/pages__create-test-data__instuctions": "", "@sage/xtrem-master-data/pages__create-test-data__link_instuctions": "", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__currency____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__currency____objectTypePlural": "", "@sage/xtrem-master-data/pages__currency____objectTypeSingular": "", "@sage/xtrem-master-data/pages__currency____title": "", "@sage/xtrem-master-data/pages__currency___id____title": "", "@sage/xtrem-master-data/pages__currency__addExchangeRate____title": "", "@sage/xtrem-master-data/pages__currency__addRateSection____title": "", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__id": "", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____title": "", "@sage/xtrem-master-data/pages__currency__converterResult____placeholder": "", "@sage/xtrem-master-data/pages__currency__converterResult____title": "", "@sage/xtrem-master-data/pages__currency__converterSection____title": "", "@sage/xtrem-master-data/pages__currency__converterToAmount____placeholder": "", "@sage/xtrem-master-data/pages__currency__converterToAmount____title": "", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__id": "", "@sage/xtrem-master-data/pages__currency__converterToCurrency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__currency__converterToCurrency____placeholder": "", "@sage/xtrem-master-data/pages__currency__converterToCurrency____title": "", "@sage/xtrem-master-data/pages__currency__currencyRate____title": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__dateRate": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__id": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__symbol": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__rate": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__shortDescription": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__image__title": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line2Right__title": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line3Right__title": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__title__title": "", "@sage/xtrem-master-data/pages__currency__currentExchangeRatesSection____title": "", "@sage/xtrem-master-data/pages__currency__decimalDigits____title": "", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__id": "", "@sage/xtrem-master-data/pages__currency__destinationCurrency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__currency__destinationCurrency____placeholder": "", "@sage/xtrem-master-data/pages__currency__destinationCurrency____title": "", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderBlock____title": "", "@sage/xtrem-master-data/pages__currency__divisor____title": "", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__dateRate": "", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__id": "", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__name": "", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__rate": "", "@sage/xtrem-master-data/pages__currency__exchangeRates____dropdownActions__title": "", "@sage/xtrem-master-data/pages__currency__exchangeRates____title": "", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____chart__xAxis__title": "", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____title": "", "@sage/xtrem-master-data/pages__currency__exchangeRatesSection____title": "", "@sage/xtrem-master-data/pages__currency__icon____title": "", "@sage/xtrem-master-data/pages__currency__id____title": "", "@sage/xtrem-master-data/pages__currency__invalid_id": "", "@sage/xtrem-master-data/pages__currency__mainSection____title": "", "@sage/xtrem-master-data/pages__currency__name____title": "", "@sage/xtrem-master-data/pages__currency__rateDate____placeholder": "", "@sage/xtrem-master-data/pages__currency__rateDate____title": "", "@sage/xtrem-master-data/pages__currency__rounding____title": "", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__id": "", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____title": "", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_message": "", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_title": "", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_title": "", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_reverse_rate_confirmation_message": "", "@sage/xtrem-master-data/pages__currency__side_panel_add_inverse_currency_rate_confirmation_title": "", "@sage/xtrem-master-data/pages__currency__symbol____title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line10__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line12__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line13__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line18__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line19__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line20__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line21__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line22__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line6__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line7__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line8__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line9__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-master-data/pages__customer____objectTypePlural": "", "@sage/xtrem-master-data/pages__customer____objectTypeSingular": "", "@sage/xtrem-master-data/pages__customer____title": "", "@sage/xtrem-master-data/pages__customer__addItem____title": "", "@sage/xtrem-master-data/pages__customer__addItemPriceLine____title": "", "@sage/xtrem-master-data/pages__customer__addresses____addButtonText": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__incoterm__name": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isActive": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isPrimary": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__leadTime": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__mode__name": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__4": "", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__5": "", "@sage/xtrem-master-data/pages__customer__addresses____title": "", "@sage/xtrem-master-data/pages__customer__addressSection____title": "", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_id": "", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_name": "", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_taxIdNumber": "", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine1": "", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine2": "", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__postcode": "", "@sage/xtrem-master-data/pages__customer__billToAddress____title": "", "@sage/xtrem-master-data/pages__customer__billToCustomer____title": "", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____title": "", "@sage/xtrem-master-data/pages__customer__category____columns__title__sequenceNumber__name": "", "@sage/xtrem-master-data/pages__customer__category____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__customer__contact_assigned_primary": "", "@sage/xtrem-master-data/pages__customer__contacts____addButtonText": "", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__customer__contacts____headerLabel__title": "", "@sage/xtrem-master-data/pages__customer__contactSection____title": "", "@sage/xtrem-master-data/pages__customer__country____columns__title__id": "", "@sage/xtrem-master-data/pages__customer__country____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__customer__createFromBusinessEntity____title": "", "@sage/xtrem-master-data/pages__customer__creditLimit____title": "", "@sage/xtrem-master-data/pages__customer__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__customer__display_address_active": "", "@sage/xtrem-master-data/pages__customer__displayStatus____title": "", "@sage/xtrem-master-data/pages__customer__financialBlock____title": "", "@sage/xtrem-master-data/pages__customer__financialSection____title": "", "@sage/xtrem-master-data/pages__customer__imageBlock____title": "", "@sage/xtrem-master-data/pages__customer__internalNote____helperText": "", "@sage/xtrem-master-data/pages__customer__internalNote____title": "", "@sage/xtrem-master-data/pages__customer__isOnHold____title": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__charge": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__discount": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__currency__id": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__customer__businessEntity__name": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__endDate": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__fromQuantity": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__isActive": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__description": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__id": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__name": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__price": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__priceReason__name": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__salesSite__id": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__startDate": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__stockSite__id": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__toQuantity": "", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__unit__id": "", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__customer__itemPricesSection____title": "", "@sage/xtrem-master-data/pages__customer__items____columns__columns__item__name__title": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__id": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__isActive": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__description": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__id": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__name": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__maximumSalesQuantity": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__minimumSalesQuantity": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__name": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnit__name": "", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnitToStockUnitConversion": "", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__customer__items____title": "", "@sage/xtrem-master-data/pages__customer__itemSection____title": "", "@sage/xtrem-master-data/pages__customer__mainBlock____title": "", "@sage/xtrem-master-data/pages__customer__mainSection____title": "", "@sage/xtrem-master-data/pages__customer__minimumOrderAmount____title": "", "@sage/xtrem-master-data/pages__customer__noteBlock____title": "", "@sage/xtrem-master-data/pages__customer__notesSection____title": "", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine1": "", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine2": "", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__postcode": "", "@sage/xtrem-master-data/pages__customer__payByAddress____title": "", "@sage/xtrem-master-data/pages__customer__payByCustomer____title": "", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____dropdownActions__title": "", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____title": "", "@sage/xtrem-master-data/pages__customer__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory": "", "@sage/xtrem-master-data/pages__customer__primary_ship_to_address_mandatory": "", "@sage/xtrem-master-data/pages__customer__put_on_hold": "", "@sage/xtrem-master-data/pages__customer__putOnHold____title": "", "@sage/xtrem-master-data/pages__customer__remove_on_hold": "", "@sage/xtrem-master-data/pages__customer__removeOnHold____title": "", "@sage/xtrem-master-data/pages__customer__save____title": "", "@sage/xtrem-master-data/pages__customer__website____title": "", "@sage/xtrem-master-data/pages__customer_address_panel_new__isPrimaryShippingAddress_must_be_active": "", "@sage/xtrem-master-data/pages__customer_contact_list__primary_address": "", "@sage/xtrem-master-data/pages__customer_contact_list__primary_contact": "", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypePlural": "", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypeSingular": "", "@sage/xtrem-master-data/pages__customer_price_reason____title": "", "@sage/xtrem-master-data/pages__customer_price_reason__description____title": "", "@sage/xtrem-master-data/pages__customer_price_reason__mainSection____title": "", "@sage/xtrem-master-data/pages__customer_price_reason__name____title": "", "@sage/xtrem-master-data/pages__customer_price_reason__priority____title": "", "@sage/xtrem-master-data/pages__customer_price_reason__priority_already_exists": "", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__isSequenceNumberManagement__title": "", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__sequenceNumber__title": "", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypePlural": "", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypeSingular": "", "@sage/xtrem-master-data/pages__customer_supplier_category____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__id____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__isCustomer____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__isSequenceNumberManagement____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__isSupplier____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__mainSection____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__name____title": "", "@sage/xtrem-master-data/pages__customer_supplier_category__sequenceNumber____title": "", "@sage/xtrem-master-data/pages__customer-supplier__category_dialog_content": "", "@sage/xtrem-master-data/pages__customer-supplier__generate_ID": "", "@sage/xtrem-master-data/pages__customer-supplier__keep_current_id-": "", "@sage/xtrem-master-data/pages__customer-supplier__select_id_number_title": "", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-customer": "", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier": "", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier-customer": "", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__formattedCapacity__title": "", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__isFullDay__title": "", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__daily_shift____objectTypePlural": "", "@sage/xtrem-master-data/pages__daily_shift____objectTypeSingular": "", "@sage/xtrem-master-data/pages__daily_shift____title": "", "@sage/xtrem-master-data/pages__daily_shift___id____title": "", "@sage/xtrem-master-data/pages__daily_shift__addShiftDetail____title": "", "@sage/xtrem-master-data/pages__daily_shift__detailsBlock____title": "", "@sage/xtrem-master-data/pages__daily_shift__formattedCapacity____title": "", "@sage/xtrem-master-data/pages__daily_shift__isFullDay____title": "", "@sage/xtrem-master-data/pages__daily_shift__mainBlock____title": "", "@sage/xtrem-master-data/pages__daily_shift__mainSection____title": "", "@sage/xtrem-master-data/pages__daily_shift__name____title": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__formattedDuration": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__id": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__name": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftEnd": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftStart": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____dropdownActions__title": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____title": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__formattedDuration": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftEnd": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftStart": "", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__delete_page_dialog_content": "", "@sage/xtrem-master-data/pages__delete_page_dialog_title": "", "@sage/xtrem-master-data/pages__delete_page_Item_delete_supplier_price_dialog_content": "", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title__duplicate": "", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__delivery_mode____objectTypePlural": "", "@sage/xtrem-master-data/pages__delivery_mode____objectTypeSingular": "", "@sage/xtrem-master-data/pages__delivery_mode____title": "", "@sage/xtrem-master-data/pages__delivery_mode__description____title": "", "@sage/xtrem-master-data/pages__delivery_mode__id____title": "", "@sage/xtrem-master-data/pages__delivery_mode__name____title": "", "@sage/xtrem-master-data/pages__delivery_mode__section____title": "", "@sage/xtrem-master-data/pages__email_exception": "", "@sage/xtrem-master-data/pages__enter_email_address_and_last_name": "", "@sage/xtrem-master-data/pages__ghs_classification____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__ghs_classification____objectTypePlural": "", "@sage/xtrem-master-data/pages__ghs_classification____objectTypeSingular": "", "@sage/xtrem-master-data/pages__ghs_classification____title": "", "@sage/xtrem-master-data/pages__ghs_classification__generalSection____title": "", "@sage/xtrem-master-data/pages__ghs_classification__ghsClassificationInformationBlock____title": "", "@sage/xtrem-master-data/pages__ghs_classification__hazard____title": "", "@sage/xtrem-master-data/pages__ghs_classification__id____title": "", "@sage/xtrem-master-data/pages__ghs_classification__idBlock____title": "", "@sage/xtrem-master-data/pages__ghs_classification__name____title": "", "@sage/xtrem-master-data/pages__ghs_classification__pictogram____title": "", "@sage/xtrem-master-data/pages__ghs_classification__pictogramBlock____title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__postfix": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__location__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__minCapabilityLevel__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__type__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__weeklyShift__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__group_resource____objectTypePlural": "", "@sage/xtrem-master-data/pages__group_resource____objectTypeSingular": "", "@sage/xtrem-master-data/pages__group_resource____title": "", "@sage/xtrem-master-data/pages__group_resource__addCostCategory____title": "", "@sage/xtrem-master-data/pages__group_resource__addReplacementLine____title": "", "@sage/xtrem-master-data/pages__group_resource__addResource____helperText": "", "@sage/xtrem-master-data/pages__group_resource__addResource____title": "", "@sage/xtrem-master-data/pages__group_resource__blockDetails____title": "", "@sage/xtrem-master-data/pages__group_resource__blockReplacements____title": "", "@sage/xtrem-master-data/pages__group_resource__blockResources____title": "", "@sage/xtrem-master-data/pages__group_resource__blockWeekly____title": "", "@sage/xtrem-master-data/pages__group_resource__costBlock____title": "", "@sage/xtrem-master-data/pages__group_resource__costSection____title": "", "@sage/xtrem-master-data/pages__group_resource__description____title": "", "@sage/xtrem-master-data/pages__group_resource__efficiency____postfix": "", "@sage/xtrem-master-data/pages__group_resource__efficiency____title": "", "@sage/xtrem-master-data/pages__group_resource__fullWeek____title": "", "@sage/xtrem-master-data/pages__group_resource__id____title": "", "@sage/xtrem-master-data/pages__group_resource__location____columns__title__locationType__id": "", "@sage/xtrem-master-data/pages__group_resource__location____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__group_resource__location____title": "", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____title": "", "@sage/xtrem-master-data/pages__group_resource__name____title": "", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title___sortValue": "", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__id": "", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__name": "", "@sage/xtrem-master-data/pages__group_resource__replacements____dropdownActions__title": "", "@sage/xtrem-master-data/pages__group_resource__replacements____title": "", "@sage/xtrem-master-data/pages__group_resource__resourceCapacity____title": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costCategory__name": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costUnit__name": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__indirectCostSection__name": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__runCost": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__setupCost": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____dropdownActions__title": "", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____title": "", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title": "", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__isActive": "", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__weeklyShift__id": "", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title": "", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__group_resource__resources____title": "", "@sage/xtrem-master-data/pages__group_resource__section____title": "", "@sage/xtrem-master-data/pages__group_resource__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__group_resource__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__group_resource__transferInResource____helperText": "", "@sage/xtrem-master-data/pages__group_resource__transferInResource____title": "", "@sage/xtrem-master-data/pages__group_resource__type____title": "", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____columns__title__dailyShift": "", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____title": "", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____columns__title__formattedCapacity": "", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____title": "", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__incoterm____objectTypePlural": "", "@sage/xtrem-master-data/pages__incoterm____objectTypeSingular": "", "@sage/xtrem-master-data/pages__incoterm____title": "", "@sage/xtrem-master-data/pages__incoterm__description____title": "", "@sage/xtrem-master-data/pages__incoterm__id____title": "", "@sage/xtrem-master-data/pages__incoterm__name____title": "", "@sage/xtrem-master-data/pages__incoterm__section____title": "", "@sage/xtrem-master-data/pages__indirect_cost_origin____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypePlural": "", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypeSingular": "", "@sage/xtrem-master-data/pages__indirect_cost_origin____title": "", "@sage/xtrem-master-data/pages__indirect_cost_origin__id____title": "", "@sage/xtrem-master-data/pages__indirect_cost_origin__mainSection____title": "", "@sage/xtrem-master-data/pages__indirect_cost_origin__name____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypePlural": "", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypeSingular": "", "@sage/xtrem-master-data/pages__indirect_cost_section____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__addOrigin____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__calculationMethod____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__id____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__id": "", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__name": "", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__percentage": "", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____dropdownActions__title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__mainSection____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__name____title": "", "@sage/xtrem-master-data/pages__indirect_cost_section__totalPercentage____title": "", "@sage/xtrem-master-data/pages__invalid-email": "", "@sage/xtrem-master-data/pages__item____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__item____navigationPanel__emptyStateClickableText": "", "@sage/xtrem-master-data/pages__item____navigationPanel__inlineActions__title__duplicate": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__basePrice__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__commodityCode__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__expirationDate__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBomRevisionManaged__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBought__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isManufactured__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isPhantom__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isSold__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isStockManaged__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line2Right__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line3Right__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotManagement__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotSequenceNumber__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__maximumSalesQuantity__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumPrice__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumSalesQuantity__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__purchaseUnit__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__salesUnit__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberManagement__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberSequenceNumber__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__stockUnit__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__volumeUnit__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__weightUnit__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__6": "", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__7": "", "@sage/xtrem-master-data/pages__item____objectTypePlural": "", "@sage/xtrem-master-data/pages__item____objectTypeSingular": "", "@sage/xtrem-master-data/pages__item____title": "", "@sage/xtrem-master-data/pages__item___id____title": "", "@sage/xtrem-master-data/pages__item__addingNewAllergen____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__addingNewAllergen____title": "", "@sage/xtrem-master-data/pages__item__addingNewGhsClassification____title": "", "@sage/xtrem-master-data/pages__item__addItemSite____title": "", "@sage/xtrem-master-data/pages__item__addNewAllergen____title": "", "@sage/xtrem-master-data/pages__item__addNewClassification____title": "", "@sage/xtrem-master-data/pages__item__addSupplier____title": "", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__id": "", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__name": "", "@sage/xtrem-master-data/pages__item__allergens____dropdownActions__title__remove": "", "@sage/xtrem-master-data/pages__item__allergens____title": "", "@sage/xtrem-master-data/pages__item__allergenSection____title": "", "@sage/xtrem-master-data/pages__item__basePrice____title": "", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____helperText": "", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____title": "", "@sage/xtrem-master-data/pages__item__capacity____title": "", "@sage/xtrem-master-data/pages__item__category____columns__title__sequenceNumber__name": "", "@sage/xtrem-master-data/pages__item__category____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__category____title": "", "@sage/xtrem-master-data/pages__item__commodityCode____title": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__businessEntity__id": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__id": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__isActive": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__maximumSalesQuantity": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__minimumSalesQuantity": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__name": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnit__name": "", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnitToStockUnitConversion": "", "@sage/xtrem-master-data/pages__item__customers____dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__item__customers____inlineActions__title__openLinePanel": "", "@sage/xtrem-master-data/pages__item__customers____mobileCard__title__title": "", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title": "", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__item__customers____sidebar__headerDropdownActions__title": "", "@sage/xtrem-master-data/pages__item__customers____title": "", "@sage/xtrem-master-data/pages__item__customerSection____title": "", "@sage/xtrem-master-data/pages__item__customsUnitBlock____title": "", "@sage/xtrem-master-data/pages__item__density____title": "", "@sage/xtrem-master-data/pages__item__description____title": "", "@sage/xtrem-master-data/pages__item__eanNumber____title": "", "@sage/xtrem-master-data/pages__item__financialBlock____title": "", "@sage/xtrem-master-data/pages__item__financialSection____title": "", "@sage/xtrem-master-data/pages__item__generateId": "", "@sage/xtrem-master-data/pages__item__generateNewId": "", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__id": "", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__name": "", "@sage/xtrem-master-data/pages__item__ghsClassifications____dropdownActions__title__remove": "", "@sage/xtrem-master-data/pages__item__ghsClassifications____title": "", "@sage/xtrem-master-data/pages__item__ghsClassificationSection____title": "", "@sage/xtrem-master-data/pages__item__good_stock_block_title": "", "@sage/xtrem-master-data/pages__item__good_stock_unit_title": "", "@sage/xtrem-master-data/pages__item__headerSection____title": "", "@sage/xtrem-master-data/pages__item__image____title": "", "@sage/xtrem-master-data/pages__item__imageBlock____title": "", "@sage/xtrem-master-data/pages__item__inventoryBlock____title": "", "@sage/xtrem-master-data/pages__item__isBomRevisionManaged____title": "", "@sage/xtrem-master-data/pages__item__isBought____title": "", "@sage/xtrem-master-data/pages__item__isExpiryManaged____title": "", "@sage/xtrem-master-data/pages__item__isManufactured____title": "", "@sage/xtrem-master-data/pages__item__isPhantom____title": "", "@sage/xtrem-master-data/pages__item__isSold____title": "", "@sage/xtrem-master-data/pages__item__isStockManaged____title": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__title": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__prodLeadTime": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__purchaseLeadTime": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__batchQuantity": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__economicOrderQuantity": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__expectedQuantity": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__preferredProcess": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__prodLeadTime": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__purchaseLeadTime": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__replenishmentMethod": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__requiredQuantity": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__safetyStock": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__stdCostValue": "", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__valuationMethod": "", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__edit": "", "@sage/xtrem-master-data/pages__item__itemSites____title": "", "@sage/xtrem-master-data/pages__item__keepCurrentId": "", "@sage/xtrem-master-data/pages__item__lotManagement____title": "", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____title": "", "@sage/xtrem-master-data/pages__item__mainBlock____title": "", "@sage/xtrem-master-data/pages__item__mainSection____title": "", "@sage/xtrem-master-data/pages__item__managementSection____title": "", "@sage/xtrem-master-data/pages__item__manufacturingBlock____title": "", "@sage/xtrem-master-data/pages__item__maximumSalesQuantity____title": "", "@sage/xtrem-master-data/pages__item__minimumPrice____title": "", "@sage/xtrem-master-data/pages__item__minimumSalesQuantity____title": "", "@sage/xtrem-master-data/pages__item__name____title": "", "@sage/xtrem-master-data/pages__item__positionField1____title": "", "@sage/xtrem-master-data/pages__item__positionField2____title": "", "@sage/xtrem-master-data/pages__item__positionField3____title": "", "@sage/xtrem-master-data/pages__item__priceSection____title": "", "@sage/xtrem-master-data/pages__item__purchase_unit_not_0_decimal_places": "", "@sage/xtrem-master-data/pages__item__purchaseUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__purchaseUnitBlock____title": "", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversion____title": "", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversionDedicated____title": "", "@sage/xtrem-master-data/pages__item__sales_unit_not_0_decimal_places": "", "@sage/xtrem-master-data/pages__item__salesBlock____title": "", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__id": "", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__symbol": "", "@sage/xtrem-master-data/pages__item__salesCurrency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__salesCurrency____title": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__currency__name__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title__2": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__2": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__3": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__currency__name": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__customer": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__priceReason__name": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__salesSite": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__stockSite": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__unit__name": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__charge": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__discount": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__charge": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__name": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__businessEntity__id": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__discount": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__endDate": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__fromQuantity": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__isActive": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__price": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__priceReason__name": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__startDate": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__toQuantity": "", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__name": "", "@sage/xtrem-master-data/pages__item__salesPrices____dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__item__salesPrices____inlineActions__title__openLinePanel": "", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__title__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__titleRight__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__item__salesPrices____sidebar__headerDropdownActions__title": "", "@sage/xtrem-master-data/pages__item__salesPrices____title": "", "@sage/xtrem-master-data/pages__item__salesPricesSection____title": "", "@sage/xtrem-master-data/pages__item__salesUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__salesUnitBlock____title": "", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversion____title": "", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversionDedicated____title": "", "@sage/xtrem-master-data/pages__item__saveItem____title": "", "@sage/xtrem-master-data/pages__item__selectId": "", "@sage/xtrem-master-data/pages__item__serialNumberManagement____title": "", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____title": "", "@sage/xtrem-master-data/pages__item__serialNumberUsage____title": "", "@sage/xtrem-master-data/pages__item__service_stock_block_title": "", "@sage/xtrem-master-data/pages__item__service_stock_unit_title": "", "@sage/xtrem-master-data/pages__item__siteSection____title": "", "@sage/xtrem-master-data/pages__item__status____title": "", "@sage/xtrem-master-data/pages__item__stock_unit_not_0_decimal_places": "", "@sage/xtrem-master-data/pages__item__stockUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__storageBlock____title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__currency__name__title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__2": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__3": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__2": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__3": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__4": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__currency__name": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__site": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__supplier": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__unit__name": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__name": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidFrom": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidTo": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__fromQuantity": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__price": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__priority": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__site__businessEntity__id": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__businessEntity__id": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__toQuantity": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__type": "", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__name": "", "@sage/xtrem-master-data/pages__item__supplierPrices____dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__item__supplierPrices____inlineActions__title__openLinePanel": "", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__title__title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__titleRight__title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____sidebar__headerDropdownActions__title": "", "@sage/xtrem-master-data/pages__item__supplierPrices____title": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__2": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__3": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__4": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__purchaseUnitOfMeasure": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__supplier": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isActive": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isDefaultItemSupplier": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__minimumPurchaseQuantity": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseLeadTime": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseUnitOfMeasure": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__businessEntity__id": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemCode": "", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemName": "", "@sage/xtrem-master-data/pages__item__suppliers____dropdownActions__title__delete": "", "@sage/xtrem-master-data/pages__item__suppliers____inlineActions__title__openLinePanel": "", "@sage/xtrem-master-data/pages__item__suppliers____mobileCard__title__title": "", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title": "", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__item__suppliers____sidebar__headerDropdownActions__title": "", "@sage/xtrem-master-data/pages__item__suppliers____title": "", "@sage/xtrem-master-data/pages__item__supplierSection____title": "", "@sage/xtrem-master-data/pages__item__type____title": "", "@sage/xtrem-master-data/pages__item__typeBlock____title": "", "@sage/xtrem-master-data/pages__item__unitBlock____title": "", "@sage/xtrem-master-data/pages__item__unitSection____title": "", "@sage/xtrem-master-data/pages__item__volume____title": "", "@sage/xtrem-master-data/pages__item__volumeUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item__weight____title": "", "@sage/xtrem-master-data/pages__item__weightUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__isSequenceNumberManagement__title": "", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__sequenceNumber__title": "", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__type__title": "", "@sage/xtrem-master-data/pages__item_category____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__item_category____objectTypePlural": "", "@sage/xtrem-master-data/pages__item_category____objectTypeSingular": "", "@sage/xtrem-master-data/pages__item_category____title": "", "@sage/xtrem-master-data/pages__item_category__declarationsBlock____title": "", "@sage/xtrem-master-data/pages__item_category__generalBlock____title": "", "@sage/xtrem-master-data/pages__item_category__generalSection____title": "", "@sage/xtrem-master-data/pages__item_category__id____title": "", "@sage/xtrem-master-data/pages__item_category__isSequenceNumberManagement____title": "", "@sage/xtrem-master-data/pages__item_category__name____title": "", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____title": "", "@sage/xtrem-master-data/pages__item_category__type____title": "", "@sage/xtrem-master-data/pages__item_customer__edit____title": "", "@sage/xtrem-master-data/pages__item_customer_panel____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__cancel____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__id____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__item____columns__title__category__name": "", "@sage/xtrem-master-data/pages__item_customer_panel__item____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_panel__item____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__item_is_inactive": "", "@sage/xtrem-master-data/pages__item_customer_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__maximumSalesQuantity____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__minimumSalesQuantity____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__name____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnitToStockUnitConversion____title": "", "@sage/xtrem-master-data/pages__item_customer_panel__save____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__cancel____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____postfix": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__confirm____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____columns__title__id": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__id": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____postfix": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__edit____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__endDate____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__fromQuantity____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____columns__title__category__name": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__new____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_greater_than_100": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_is_negative": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__price____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__startDate____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__toQuantity____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____title": "", "@sage/xtrem-master-data/pages__item_customer_price_panel__validUnits____title": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel____title": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__cancel____title": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__idBlock____title": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__charge": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__discount": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title___id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__currency__id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__customer__id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__endDate": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__fromQuantity": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__name": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__price": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__priceReason__name": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__name": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__startDate": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__name": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__toQuantity": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__unit__id": "", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__batchQuantity__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__economicOrderQuantity__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__expectedQuantity__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemCategory__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemDescription__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemId__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__preferredProcess__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__postfix": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__reorderPoint__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__replenishmentMethod__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__requiredQuantity__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__safetyStock__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__stdCostValue__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__title__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__valuationMethod__title": "", "@sage/xtrem-master-data/pages__item_site____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__item_site____objectTypePlural": "", "@sage/xtrem-master-data/pages__item_site____objectTypeSingular": "", "@sage/xtrem-master-data/pages__item_site____title": "", "@sage/xtrem-master-data/pages__item_site___id____title": "", "@sage/xtrem-master-data/pages__item_site___valuationMethod____title": "", "@sage/xtrem-master-data/pages__item_site__addItemSiteCost____title": "", "@sage/xtrem-master-data/pages__item_site__addSupplier____title": "", "@sage/xtrem-master-data/pages__item_site__batchQuantity____title": "", "@sage/xtrem-master-data/pages__item_site__cancel____title": "", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____title": "", "@sage/xtrem-master-data/pages__item_site__confirm____title": "", "@sage/xtrem-master-data/pages__item_site__costBlock____title": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title__2": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__costCategory__name": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__forQuantity": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__fromDate": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__isCalculated": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__totalCost": "", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__unitCost": "", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title": "", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__item_site__costs____title": "", "@sage/xtrem-master-data/pages__item_site__costsBlock____title": "", "@sage/xtrem-master-data/pages__item_site__costSection____title": "", "@sage/xtrem-master-data/pages__item_site__economicOrderQuantity____title": "", "@sage/xtrem-master-data/pages__item_site__edit____title": "", "@sage/xtrem-master-data/pages__item_site__expectedQuantity____title": "", "@sage/xtrem-master-data/pages__item_site__id____title": "", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____title": "", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____title": "", "@sage/xtrem-master-data/pages__item_site__isOrderToOrder____title": "", "@sage/xtrem-master-data/pages__item_site__item____columns__title__category__name": "", "@sage/xtrem-master-data/pages__item_site__item____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site__item____title": "", "@sage/xtrem-master-data/pages__item_site__locationBlock____title": "", "@sage/xtrem-master-data/pages__item_site__mainSection____title": "", "@sage/xtrem-master-data/pages__item_site__new____title": "", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____title": "", "@sage/xtrem-master-data/pages__item_site__preferredProcess____title": "", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____postfix": "", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____title": "", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____postfix": "", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____title": "", "@sage/xtrem-master-data/pages__item_site__qualityControlBlock____title": "", "@sage/xtrem-master-data/pages__item_site__reorderPoint____title": "", "@sage/xtrem-master-data/pages__item_site__replenishmentBlock____title": "", "@sage/xtrem-master-data/pages__item_site__replenishmentMethod____title": "", "@sage/xtrem-master-data/pages__item_site__replenishmentSection____title": "", "@sage/xtrem-master-data/pages__item_site__requiredQuantity____title": "", "@sage/xtrem-master-data/pages__item_site__safetyStock____title": "", "@sage/xtrem-master-data/pages__item_site__saveItemSite____title": "", "@sage/xtrem-master-data/pages__item_site__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__item_site__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site__stdCostValue____title": "", "@sage/xtrem-master-data/pages__item_site__stockBlock____title": "", "@sage/xtrem-master-data/pages__item_site__stockRulesSection____title": "", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__isDefaultItemSupplier": "", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__minimumPurchaseOrderQuantity": "", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseLeadTime": "", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseUnit__name": "", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__id": "", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__name": "", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title": "", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__item_site__suppliers____title": "", "@sage/xtrem-master-data/pages__item_site__suppliersBlock____title": "", "@sage/xtrem-master-data/pages__item_site__suppliersSection____title": "", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__item_site_cost____objectTypePlural": "", "@sage/xtrem-master-data/pages__item_site_cost____objectTypeSingular": "", "@sage/xtrem-master-data/pages__item_site_cost____title": "", "@sage/xtrem-master-data/pages__item_site_cost___id____title": "", "@sage/xtrem-master-data/pages__item_site_cost__calculate____title": "", "@sage/xtrem-master-data/pages__item_site_cost__chartBlock____title": "", "@sage/xtrem-master-data/pages__item_site_cost__costBlock____title": "", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__costCategoryType": "", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__isMandatory": "", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____title": "", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriod____title": "", "@sage/xtrem-master-data/pages__item_site_cost__forQuantity____title": "", "@sage/xtrem-master-data/pages__item_site_cost__fromDate____title": "", "@sage/xtrem-master-data/pages__item_site_cost__isCalculated____title": "", "@sage/xtrem-master-data/pages__item_site_cost__item____title": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__id": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__name": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__status": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__type": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site__id": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____title": "", "@sage/xtrem-master-data/pages__item_site_cost__laborCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost__machineCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost__mainSection____title": "", "@sage/xtrem-master-data/pages__item_site_cost__materialCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost__site____title": "", "@sage/xtrem-master-data/pages__item_site_cost__toDate____title": "", "@sage/xtrem-master-data/pages__item_site_cost__toolCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost__totalCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost__unitCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost__version____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel____subtitle": "", "@sage/xtrem-master-data/pages__item_site_cost_panel____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel___id____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancel____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__chartBlock____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__costBlock____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__costCategoryType": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__isMandatory": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__costChart____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__forQuantity____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__fromDate____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__invalid-from-date": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__site__id__title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__id": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__name": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__id": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__name": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__laborCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__machineCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__materialCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__save____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__toDate____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__toolCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__totalCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__unitCost____title": "", "@sage/xtrem-master-data/pages__item_site_cost_panel__version____title": "", "@sage/xtrem-master-data/pages__item_site_supplier____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypePlural": "", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypeSingular": "", "@sage/xtrem-master-data/pages__item_site_supplier____title": "", "@sage/xtrem-master-data/pages__item_site_supplier___id____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__cancel____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__confirm____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__deleteItemSiteSupplier____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__identificationBlock____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__isDefaultItemSupplier____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemDetailsBlock____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__id": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__name": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__status": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__type": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site__id": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__country__name": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__id": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__name": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__taxIdNumber": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__mainSection____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__minimumPurchaseOrderQuantity____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____postfix": "", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseUnit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_site_supplier__saveItemSiteSupplier____title": "", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__id": "", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__taxIdNumber": "", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_supplier__item_cost_edit____title": "", "@sage/xtrem-master-data/pages__item_supplier__item_cost_new____title": "", "@sage/xtrem-master-data/pages__item_supplier__item_edit____title": "", "@sage/xtrem-master-data/pages__item_supplier__item_new____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__cancel____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__confirm____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____columns__title__id": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValid____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidFrom____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidTo____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__fromQuantity____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____columns__title__category__name": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__price____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__priority____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__country__name": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__id": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__toQuantity____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__type____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel___id____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__cancel____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__idBlock____title": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__currency__id": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidFrom": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidTo": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__fromQuantity": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__site__id": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__toQuantity": "", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__item-customer_panel__conversion_negative_value": "", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_less_than_minimum_value": "", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_negative_value": "", "@sage/xtrem-master-data/pages__item-customer_panel__minimum_quantity_negative_value": "", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_buy_to_order": "", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_make_to_order": "", "@sage/xtrem-master-data/pages__item-site__preferred_process_cannot_be": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeFrom__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeTo__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__efficiency__postfix": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__image__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__resourceGroup__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__weeklyShift__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__labor_resource____objectTypePlural": "", "@sage/xtrem-master-data/pages__labor_resource____objectTypeSingular": "", "@sage/xtrem-master-data/pages__labor_resource____title": "", "@sage/xtrem-master-data/pages__labor_resource___id____title": "", "@sage/xtrem-master-data/pages__labor_resource__activeFrom____title": "", "@sage/xtrem-master-data/pages__labor_resource__activeTo____title": "", "@sage/xtrem-master-data/pages__labor_resource__addCapability____title": "", "@sage/xtrem-master-data/pages__labor_resource__addCostCategory____title": "", "@sage/xtrem-master-data/pages__labor_resource__blockCapabilities____title": "", "@sage/xtrem-master-data/pages__labor_resource__blockDetails____title": "", "@sage/xtrem-master-data/pages__labor_resource__blockWeekly____title": "", "@sage/xtrem-master-data/pages__labor_resource__cancelSidePanel____title": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__capabilityLevel__name": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateEndValid": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateStartValid": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__machine__name": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__service__name": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__tool__name": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__labor_resource__capabilities____title": "", "@sage/xtrem-master-data/pages__labor_resource__capabilitiesSection____title": "", "@sage/xtrem-master-data/pages__labor_resource__costBlock____title": "", "@sage/xtrem-master-data/pages__labor_resource__costSection____title": "", "@sage/xtrem-master-data/pages__labor_resource__description____title": "", "@sage/xtrem-master-data/pages__labor_resource__efficiency____postfix": "", "@sage/xtrem-master-data/pages__labor_resource__efficiency____title": "", "@sage/xtrem-master-data/pages__labor_resource__id____title": "", "@sage/xtrem-master-data/pages__labor_resource__isActive____title": "", "@sage/xtrem-master-data/pages__labor_resource__location____columns__title__locationType__id": "", "@sage/xtrem-master-data/pages__labor_resource__location____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__labor_resource__location____title": "", "@sage/xtrem-master-data/pages__labor_resource__name____title": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCapacity____title": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costCategory__name": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costUnit__name": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__indirectCostSection__name": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__runCost": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__setupCost": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____dropdownActions__title": "", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____title": "", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____title": "", "@sage/xtrem-master-data/pages__labor_resource__resourceImage____title": "", "@sage/xtrem-master-data/pages__labor_resource__saveSidePanel____title": "", "@sage/xtrem-master-data/pages__labor_resource__section____title": "", "@sage/xtrem-master-data/pages__labor_resource__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__labor_resource__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__capacity": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__dailyShift": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__day": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift1": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift2": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift3": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift4": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift5": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____title": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____columns__title__formattedCapacity": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__postfix": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isInternalIcon__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isSingleLot__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__location__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__sSingleItem__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__titleRight__title": "", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__license_plate_number____objectTypePlural": "", "@sage/xtrem-master-data/pages__license_plate_number____objectTypeSingular": "", "@sage/xtrem-master-data/pages__license_plate_number____title": "", "@sage/xtrem-master-data/pages__license_plate_number___id____title": "", "@sage/xtrem-master-data/pages__license_plate_number__consumedCapacity____title": "", "@sage/xtrem-master-data/pages__license_plate_number__container____columns__title__isInternal": "", "@sage/xtrem-master-data/pages__license_plate_number__container____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__license_plate_number__container____title": "", "@sage/xtrem-master-data/pages__license_plate_number__containerType____title": "", "@sage/xtrem-master-data/pages__license_plate_number__isInternal____title": "", "@sage/xtrem-master-data/pages__license_plate_number__isSingleItem____title": "", "@sage/xtrem-master-data/pages__license_plate_number__isSingleLot____title": "", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__title__locationType__id": "", "@sage/xtrem-master-data/pages__license_plate_number__location____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__license_plate_number__location____title": "", "@sage/xtrem-master-data/pages__license_plate_number__locationSite____title": "", "@sage/xtrem-master-data/pages__license_plate_number__locationType____title": "", "@sage/xtrem-master-data/pages__license_plate_number__mainSection____title": "", "@sage/xtrem-master-data/pages__license_plate_number__mass_update__success": "", "@sage/xtrem-master-data/pages__license_plate_number__number____title": "", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__id": "", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__license_plate_number__owner____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__license_plate_number__owner____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__cancelLicensePlateNumbers____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleItem": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleLot": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__sequenceNumber__id": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleItem____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleLot____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleItem": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleLot": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__locationType__name": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__name": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__site__name": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbersBlock____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____columns__title__locationType__name": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationSite____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationType____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__mainSection____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__numberToCreate____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__saveLicensePlateNumbers____title": "", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__searchButton____title": "", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__dangerousGoodAllowed__title": "", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationType__title": "", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationZone__title": "", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__location____objectTypePlural": "", "@sage/xtrem-master-data/pages__location____objectTypeSingular": "", "@sage/xtrem-master-data/pages__location____title": "", "@sage/xtrem-master-data/pages__location__dangerousGoodAllowed____title": "", "@sage/xtrem-master-data/pages__location__id____title": "", "@sage/xtrem-master-data/pages__location__locationType____columns__title__locationCategory": "", "@sage/xtrem-master-data/pages__location__locationType____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location__locationType____title": "", "@sage/xtrem-master-data/pages__location__locationZone____columns__title__zoneType": "", "@sage/xtrem-master-data/pages__location__locationZone____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location__locationZone____title": "", "@sage/xtrem-master-data/pages__location__name____title": "", "@sage/xtrem-master-data/pages__location__section____title": "", "@sage/xtrem-master-data/pages__location__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__location__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location_mass_creation____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__cancel____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__createLocations____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationType__name__title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationZone__name__title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationType__name": "", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationZone__name": "", "@sage/xtrem-master-data/pages__location_mass_creation__locations____dropdownActions__title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationsBlock____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__lastSequenceUsed": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocationsRemaining": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberOfCombinations": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____columns__title__locationCategory": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____columns__title__zoneType": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__mainSection____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__requiredCombinations____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__searchButton____title": "", "@sage/xtrem-master-data/pages__location_mass_creation__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__location_mass_creation__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location_mass_creation__site____placeholder": "", "@sage/xtrem-master-data/pages__location_mass_creation__site____title": "", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__componentLength__title": "", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberOfCombinations__title": "", "@sage/xtrem-master-data/pages__location_sequence____objectTypePlural": "", "@sage/xtrem-master-data/pages__location_sequence____objectTypeSingular": "", "@sage/xtrem-master-data/pages__location_sequence____title": "", "@sage/xtrem-master-data/pages__location_sequence__addComponent____title": "", "@sage/xtrem-master-data/pages__location_sequence__capital_letters_only": "", "@sage/xtrem-master-data/pages__location_sequence__componentLength____title": "", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__endValue": "", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__startValue": "", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__type": "", "@sage/xtrem-master-data/pages__location_sequence__components____dropdownActions__title": "", "@sage/xtrem-master-data/pages__location_sequence__components____title": "", "@sage/xtrem-master-data/pages__location_sequence__componentsBlock____title": "", "@sage/xtrem-master-data/pages__location_sequence__constant_invalid_length": "", "@sage/xtrem-master-data/pages__location_sequence__digits_only": "", "@sage/xtrem-master-data/pages__location_sequence__id____title": "", "@sage/xtrem-master-data/pages__location_sequence__invalid_range": "", "@sage/xtrem-master-data/pages__location_sequence__mainBlock____title": "", "@sage/xtrem-master-data/pages__location_sequence__mainSection____title": "", "@sage/xtrem-master-data/pages__location_sequence__name____title": "", "@sage/xtrem-master-data/pages__location_sequence__numberOfCombinations____title": "", "@sage/xtrem-master-data/pages__location_type____navigationPanel__listItem__locationCategory__title": "", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-master-data/pages__location_type____objectTypePlural": "", "@sage/xtrem-master-data/pages__location_type____objectTypeSingular": "", "@sage/xtrem-master-data/pages__location_type____title": "", "@sage/xtrem-master-data/pages__location_type__description____title": "", "@sage/xtrem-master-data/pages__location_type__id____title": "", "@sage/xtrem-master-data/pages__location_type__locationCategory____title": "", "@sage/xtrem-master-data/pages__location_type__mainBlock____title": "", "@sage/xtrem-master-data/pages__location_type__mainSection____title": "", "@sage/xtrem-master-data/pages__location_type__name____title": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__zoneType__title": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__4": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__5": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__6": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__7": "", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__8": "", "@sage/xtrem-master-data/pages__location_zone____objectTypePlural": "", "@sage/xtrem-master-data/pages__location_zone____objectTypeSingular": "", "@sage/xtrem-master-data/pages__location_zone____title": "", "@sage/xtrem-master-data/pages__location_zone__id____title": "", "@sage/xtrem-master-data/pages__location_zone__name____title": "", "@sage/xtrem-master-data/pages__location_zone__section____title": "", "@sage/xtrem-master-data/pages__location_zone__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__location_zone__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__location_zone__zoneType____title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeFrom__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeTo__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__postfix": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__image__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__location__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__minCapabilityLevel__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__model__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__resourceGroup__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__serialNumber__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__weeklyShift__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__machine_resource____objectTypePlural": "", "@sage/xtrem-master-data/pages__machine_resource____objectTypeSingular": "", "@sage/xtrem-master-data/pages__machine_resource____title": "", "@sage/xtrem-master-data/pages__machine_resource___id____title": "", "@sage/xtrem-master-data/pages__machine_resource__activeFrom____title": "", "@sage/xtrem-master-data/pages__machine_resource__activeTo____title": "", "@sage/xtrem-master-data/pages__machine_resource__addCostCategory____title": "", "@sage/xtrem-master-data/pages__machine_resource__blockContract____title": "", "@sage/xtrem-master-data/pages__machine_resource__blockDetails____title": "", "@sage/xtrem-master-data/pages__machine_resource__blockWeekly____title": "", "@sage/xtrem-master-data/pages__machine_resource__cancelSidePanel____title": "", "@sage/xtrem-master-data/pages__machine_resource__contractId____title": "", "@sage/xtrem-master-data/pages__machine_resource__contractName____title": "", "@sage/xtrem-master-data/pages__machine_resource__contractSection____title": "", "@sage/xtrem-master-data/pages__machine_resource__costBlock____title": "", "@sage/xtrem-master-data/pages__machine_resource__costSection____title": "", "@sage/xtrem-master-data/pages__machine_resource__description____title": "", "@sage/xtrem-master-data/pages__machine_resource__efficiency____postfix": "", "@sage/xtrem-master-data/pages__machine_resource__efficiency____title": "", "@sage/xtrem-master-data/pages__machine_resource__fullWeek____title": "", "@sage/xtrem-master-data/pages__machine_resource__id____title": "", "@sage/xtrem-master-data/pages__machine_resource__isActive____title": "", "@sage/xtrem-master-data/pages__machine_resource__location____columns__title__locationType__id": "", "@sage/xtrem-master-data/pages__machine_resource__location____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__machine_resource__location____title": "", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____title": "", "@sage/xtrem-master-data/pages__machine_resource__model____title": "", "@sage/xtrem-master-data/pages__machine_resource__name____title": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCapacity____title": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costCategory__name": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costUnit__name": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__indirectCostSection__name": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__runCost": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__setupCost": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____dropdownActions__title": "", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____title": "", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____title": "", "@sage/xtrem-master-data/pages__machine_resource__resourceImage____title": "", "@sage/xtrem-master-data/pages__machine_resource__section____title": "", "@sage/xtrem-master-data/pages__machine_resource__serialNumber____title": "", "@sage/xtrem-master-data/pages__machine_resource__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__machine_resource__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__country": "", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__id": "", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__taxIdNumber": "", "@sage/xtrem-master-data/pages__machine_resource__supplier____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__machine_resource__weeklyDetails____columns__title__dailyShift": "", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____columns__title__formattedCapacity": "", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____title": "", "@sage/xtrem-master-data/pages__master_data__cancel": "", "@sage/xtrem-master-data/pages__master_data__confirm": "", "@sage/xtrem-master-data/pages__master_data__update": "", "@sage/xtrem-master-data/pages__master_data__warning-dialog-content": "", "@sage/xtrem-master-data/pages__multiple__location__creation__success_multi": "", "@sage/xtrem-master-data/pages__multiple_location_creation__success": "", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__payment_term____objectTypePlural": "", "@sage/xtrem-master-data/pages__payment_term____objectTypeSingular": "", "@sage/xtrem-master-data/pages__payment_term____title": "", "@sage/xtrem-master-data/pages__payment_term__amount": "", "@sage/xtrem-master-data/pages__payment_term__blockDiscount____title": "", "@sage/xtrem-master-data/pages__payment_term__blockDue____title": "", "@sage/xtrem-master-data/pages__payment_term__blockPenalty____title": "", "@sage/xtrem-master-data/pages__payment_term__businessEntityType____title": "", "@sage/xtrem-master-data/pages__payment_term__days____title": "", "@sage/xtrem-master-data/pages__payment_term__description____title": "", "@sage/xtrem-master-data/pages__payment_term__discountDate____title": "", "@sage/xtrem-master-data/pages__payment_term__discountFrom____title": "", "@sage/xtrem-master-data/pages__payment_term__discountType____title": "", "@sage/xtrem-master-data/pages__payment_term__dueDateType____title": "", "@sage/xtrem-master-data/pages__payment_term__id____title": "", "@sage/xtrem-master-data/pages__payment_term__name____title": "", "@sage/xtrem-master-data/pages__payment_term__penaltyType____title": "", "@sage/xtrem-master-data/pages__payment_term__percentage": "", "@sage/xtrem-master-data/pages__payment_term__section____title": "", "@sage/xtrem-master-data/pages__reason_code____objectTypePlural": "", "@sage/xtrem-master-data/pages__reason_code____objectTypeSingular": "", "@sage/xtrem-master-data/pages__reason_code____title": "", "@sage/xtrem-master-data/pages__reason_code__id____title": "", "@sage/xtrem-master-data/pages__reason_code__isActive____title": "", "@sage/xtrem-master-data/pages__reason_code__mainSection____title": "", "@sage/xtrem-master-data/pages__reason_code__name____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__approverSelectionBlock____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__default_approver": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_cannot_be_sent": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_exception_request": "", "@sage/xtrem-master-data/pages__request_approval_dialog__email_sent_to_approval": "", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____helperText": "", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email": "", "@sage/xtrem-master-data/pages__request_approval_dialog__requestApprovalSection____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__selectApprover____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__email": "", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__firstName": "", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__lastName": "", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_content": "", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__sendApprovalRequestButton____title": "", "@sage/xtrem-master-data/pages__request_approval_dialog__substitute_approver": "", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__email": "", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__firstName": "", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__lastName": "", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__type": "", "@sage/xtrem-master-data/pages__request_approval_dialog__users____title": "", "@sage/xtrem-master-data/pages__resource_functions__duration_in_hours_and_minutes": "", "@sage/xtrem-master-data/pages__resource_group_transfer____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__block____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__cancel____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__confirm____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__mainSection____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____columns__title__weeklyShift__id": "", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____columns__title__weeklyShift__id": "", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____title": "", "@sage/xtrem-master-data/pages__resource_group_transfer__type____title": "", "@sage/xtrem-master-data/pages__select_sold_to_contact_button_text": "", "@sage/xtrem-master-data/pages__send_button_text": "", "@sage/xtrem-master-data/pages__send_email_panel____title": "", "@sage/xtrem-master-data/pages__send_email_panel__cancel____title": "", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____helperText": "", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____title": "", "@sage/xtrem-master-data/pages__send_email_panel__emailFirstName____title": "", "@sage/xtrem-master-data/pages__send_email_panel__emailLastName____title": "", "@sage/xtrem-master-data/pages__send_email_panel__emailTitles____title": "", "@sage/xtrem-master-data/pages__send_email_panel__selectSoldToContact____title": "", "@sage/xtrem-master-data/pages__send_email_panel__sendEmailBlock____title": "", "@sage/xtrem-master-data/pages__send_email_panel__sendSalesOrderButton____title": "", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_5__title": "", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line6__title": "", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line7__title": "", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__sequence_number____objectTypePlural": "", "@sage/xtrem-master-data/pages__sequence_number____objectTypeSingular": "", "@sage/xtrem-master-data/pages__sequence_number____title": "", "@sage/xtrem-master-data/pages__sequence_number__addComponent____title": "", "@sage/xtrem-master-data/pages__sequence_number__componentLength____title": "", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__constant": "", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__length": "", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__type": "", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title": "", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__sequence_number__components____title": "", "@sage/xtrem-master-data/pages__sequence_number__componentsBlock____title": "", "@sage/xtrem-master-data/pages__sequence_number__createValue____title": "", "@sage/xtrem-master-data/pages__sequence_number__definitionLevel____title": "", "@sage/xtrem-master-data/pages__sequence_number__delete____title": "", "@sage/xtrem-master-data/pages__sequence_number__id____title": "", "@sage/xtrem-master-data/pages__sequence_number__isChronological____title": "", "@sage/xtrem-master-data/pages__sequence_number__isClearedByReset____title": "", "@sage/xtrem-master-data/pages__sequence_number__isUsed____title": "", "@sage/xtrem-master-data/pages__sequence_number__mainSection____title": "", "@sage/xtrem-master-data/pages__sequence_number__name____title": "", "@sage/xtrem-master-data/pages__sequence_number__propertiesBlock____title": "", "@sage/xtrem-master-data/pages__sequence_number__resetBlock____title": "", "@sage/xtrem-master-data/pages__sequence_number__rtzLevel____title": "", "@sage/xtrem-master-data/pages__sequence_number__save____title": "", "@sage/xtrem-master-data/pages__sequence_number__type____title": "", "@sage/xtrem-master-data/pages__sequence_number__updateValue____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_filter": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__addEditAssignmentLine____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__isDefaultAssignment____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__lineBlock____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__company__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isActive": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isDefaultAssignment": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__legislation__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__sequenceNumber__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__site__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____placeholder": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__cancel____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____placeholder": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__edit____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isAssignOnPosting____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isUsed____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____placeholder": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__new____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__save____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__id": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__legislation__id": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__columns__sequenceNumberAssignmentModule__id__title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__nodeFactory__name": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__sequenceNumberAssignmentModule__id": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____placeholder": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____placeholder": "", "@sage/xtrem-master-data/pages__sequence_number_value____title": "", "@sage/xtrem-master-data/pages__sequence_number_value__cancel____title": "", "@sage/xtrem-master-data/pages__sequence_number_value__confirm_action_dialog_content": "", "@sage/xtrem-master-data/pages__sequence_number_value__confirm-continue": "", "@sage/xtrem-master-data/pages__sequence_number_value__definitionLevel____title": "", "@sage/xtrem-master-data/pages__sequence_number_value__id____title": "", "@sage/xtrem-master-data/pages__sequence_number_value__minimumLength____title": "", "@sage/xtrem-master-data/pages__sequence_number_value__rtzLevel____title": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title___updateStamp": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__id": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__name": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__newNextValue": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__periodDate": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__sequenceValue": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__id": "", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__name": "", "@sage/xtrem-master-data/pages__sequence_number_value_confirm_action_dialog_title": "", "@sage/xtrem-master-data/pages__sequence-number_value_add_new____title": "", "@sage/xtrem-master-data/pages__sequence-number_value_create____title": "", "@sage/xtrem-master-data/pages__sequence-number_value_edit____title": "", "@sage/xtrem-master-data/pages__sequence-number_value_update____title": "", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftEnd__title": "", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftStart__title": "", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__shift_detail____objectTypePlural": "", "@sage/xtrem-master-data/pages__shift_detail____objectTypeSingular": "", "@sage/xtrem-master-data/pages__shift_detail____title": "", "@sage/xtrem-master-data/pages__shift_detail__formattedDuration____title": "", "@sage/xtrem-master-data/pages__shift_detail__id____title": "", "@sage/xtrem-master-data/pages__shift_detail__mainBlock____title": "", "@sage/xtrem-master-data/pages__shift_detail__mainSection____title": "", "@sage/xtrem-master-data/pages__shift_detail__name____title": "", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____placeholder": "", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____title": "", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____placeholder": "", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____title": "", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line10__title": "", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__sequenceNumber__title": "", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__taxId__title": "", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__site____objectTypePlural": "", "@sage/xtrem-master-data/pages__site____objectTypeSingular": "", "@sage/xtrem-master-data/pages__site____title": "", "@sage/xtrem-master-data/pages__site__addresses____addButtonText": "", "@sage/xtrem-master-data/pages__site__addresses____columns__title__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/pages__site__addresses____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__site__addresses____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title": "", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__4": "", "@sage/xtrem-master-data/pages__site__addresses____title": "", "@sage/xtrem-master-data/pages__site__addressSection____title": "", "@sage/xtrem-master-data/pages__site__already_exists_with_same_id": "", "@sage/xtrem-master-data/pages__site__already_exists_with_same_name": "", "@sage/xtrem-master-data/pages__site__already_exists_with_same_taxIdNumber": "", "@sage/xtrem-master-data/pages__site__contacts____addButtonText": "", "@sage/xtrem-master-data/pages__site__contacts____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__site__contacts____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title": "", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__site__contacts____headerLabel__title": "", "@sage/xtrem-master-data/pages__site__contactSection____title": "", "@sage/xtrem-master-data/pages__site__country____columns__title__id": "", "@sage/xtrem-master-data/pages__site__country____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__site__createFromBusinessEntity____title": "", "@sage/xtrem-master-data/pages__site__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__site__defaultLocation____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__site__defaultLocation____title": "", "@sage/xtrem-master-data/pages__site__description____title": "", "@sage/xtrem-master-data/pages__site__financialSite____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__site__financialSite____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createdBy": "", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createStamp": "", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updatedBy": "", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updateStamp": "", "@sage/xtrem-master-data/pages__site__groupRoleSites____title": "", "@sage/xtrem-master-data/pages__site__hierarchyChartContent____title": "", "@sage/xtrem-master-data/pages__site__imageBlock____title": "", "@sage/xtrem-master-data/pages__site__isFinance____title": "", "@sage/xtrem-master-data/pages__site__isInventory____title": "", "@sage/xtrem-master-data/pages__site__isLocationManaged____title": "", "@sage/xtrem-master-data/pages__site__isManufacturing____title": "", "@sage/xtrem-master-data/pages__site__isPurchase____title": "", "@sage/xtrem-master-data/pages__site__isSales____title": "", "@sage/xtrem-master-data/pages__site__isSequenceNumberIdUsed____title": "", "@sage/xtrem-master-data/pages__site__legalCompany____columns__title__isActive": "", "@sage/xtrem-master-data/pages__site__legalCompany____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__site__mainBlock____title": "", "@sage/xtrem-master-data/pages__site__mainSection____title": "", "@sage/xtrem-master-data/pages__site__managementSection____title": "", "@sage/xtrem-master-data/pages__site__save____title": "", "@sage/xtrem-master-data/pages__site__sequenceNumberId____title": "", "@sage/xtrem-master-data/pages__site__siteGroups____columns__title__isLegalCompany": "", "@sage/xtrem-master-data/pages__site__siteGroups____title": "", "@sage/xtrem-master-data/pages__site__siteGroupSection____title": "", "@sage/xtrem-master-data/pages__site__timeZone____title": "", "@sage/xtrem-master-data/pages__site__userGroupSection____title": "", "@sage/xtrem-master-data/pages__site__website____title": "", "@sage/xtrem-master-data/pages__site_page_taxIdNumber_required_if_no_business_entity": "", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line6__title": "", "@sage/xtrem-master-data/pages__standard____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__standard____objectTypePlural": "", "@sage/xtrem-master-data/pages__standard____objectTypeSingular": "", "@sage/xtrem-master-data/pages__standard____title": "", "@sage/xtrem-master-data/pages__standard__code____title": "", "@sage/xtrem-master-data/pages__standard__id____title": "", "@sage/xtrem-master-data/pages__standard__idBlock____title": "", "@sage/xtrem-master-data/pages__standard__industrySector____title": "", "@sage/xtrem-master-data/pages__standard__mainBlock____title": "", "@sage/xtrem-master-data/pages__standard__mainSection____title": "", "@sage/xtrem-master-data/pages__standard__name____title": "", "@sage/xtrem-master-data/pages__standard__sdo____title": "", "@sage/xtrem-master-data/pages__standard__version____title": "", "@sage/xtrem-master-data/pages__stock_journal_inquiry": "", "@sage/xtrem-master-data/pages__stock_posting_error": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__isActive__title": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line10__title": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line7__title": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line8__title": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__supplier____objectTypePlural": "", "@sage/xtrem-master-data/pages__supplier____objectTypeSingular": "", "@sage/xtrem-master-data/pages__supplier____title": "", "@sage/xtrem-master-data/pages__supplier__addCertificate____title": "", "@sage/xtrem-master-data/pages__supplier__addItem____title": "", "@sage/xtrem-master-data/pages__supplier__addPriceLine____title": "", "@sage/xtrem-master-data/pages__supplier__addressAndContactBlock____title": "", "@sage/xtrem-master-data/pages__supplier__addresses____addButtonText": "", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__concatenatedAddressWithoutName": "", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__4": "", "@sage/xtrem-master-data/pages__supplier__addresses____title": "", "@sage/xtrem-master-data/pages__supplier__addressSection____title": "", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_id": "", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_name": "", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_taxIdNumber": "", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__billByAddress____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__billByAddress____title": "", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine1": "", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine2": "", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__country__name": "", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__postcode": "", "@sage/xtrem-master-data/pages__supplier__billBySupplier____title": "", "@sage/xtrem-master-data/pages__supplier__category____columns__title__sequenceNumber__name": "", "@sage/xtrem-master-data/pages__supplier__category____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__certificationBody": "", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfCertification": "", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfOriginalCertification": "", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__id": "", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__standard__id": "", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__validUntil": "", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__supplier__certificates____title": "", "@sage/xtrem-master-data/pages__supplier__certificateSection____title": "", "@sage/xtrem-master-data/pages__supplier__commercialBlock____title": "", "@sage/xtrem-master-data/pages__supplier__commercialSection____title": "", "@sage/xtrem-master-data/pages__supplier__contacts____addButtonText": "", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__isPrimary": "", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__3": "", "@sage/xtrem-master-data/pages__supplier__contacts____headerLabel__title": "", "@sage/xtrem-master-data/pages__supplier__contactSection____title": "", "@sage/xtrem-master-data/pages__supplier__country____columns__title__id": "", "@sage/xtrem-master-data/pages__supplier__country____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__createFromBusinessEntity____title": "", "@sage/xtrem-master-data/pages__supplier__currency____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__deliveryMode____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__deliveryMode____title": "", "@sage/xtrem-master-data/pages__supplier__financialBlock____title": "", "@sage/xtrem-master-data/pages__supplier__financialSection____title": "", "@sage/xtrem-master-data/pages__supplier__imageBlock____title": "", "@sage/xtrem-master-data/pages__supplier__incoterm____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__incoterm____title": "", "@sage/xtrem-master-data/pages__supplier__internalNote____title": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isActive": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isDefaultItemSupplier": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__description": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__id": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__name": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__minimumPurchaseQuantity": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseLeadTime": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__name": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemCode": "", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemName": "", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__supplier__items____title": "", "@sage/xtrem-master-data/pages__supplier__itemSection____title": "", "@sage/xtrem-master-data/pages__supplier__mainBlock____title": "", "@sage/xtrem-master-data/pages__supplier__mainSection____title": "", "@sage/xtrem-master-data/pages__supplier__minimumOrderAmount____title": "", "@sage/xtrem-master-data/pages__supplier__noteBlock____title": "", "@sage/xtrem-master-data/pages__supplier__noteSection____title": "", "@sage/xtrem-master-data/pages__supplier__parent____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__parent____title": "", "@sage/xtrem-master-data/pages__supplier__paymentMethod____title": "", "@sage/xtrem-master-data/pages__supplier__paymentTerm____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__payToAddress____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__payToAddress____title": "", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine1": "", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine2": "", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__country__name": "", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__postcode": "", "@sage/xtrem-master-data/pages__supplier__payToSupplier____title": "", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__concatenatedAddress": "", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__returnToAddress____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__returnToAddress____title": "", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine1": "", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine2": "", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__businessEntity__name": "", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__country__name": "", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__locationPhoneNumber": "", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__postcode": "", "@sage/xtrem-master-data/pages__supplier__save____title": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__currency__id": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidFrom": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidTo": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__fromQuantity": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__description": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__id": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__name": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__id": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__name": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__toQuantity": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title__2": "", "@sage/xtrem-master-data/pages__supplier__supplierPrices____title": "", "@sage/xtrem-master-data/pages__supplier__website____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__cancel____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__certificationBody____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__confirm____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfCertification____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfOriginalCertification____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__edit____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__id____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainBlock____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__new____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__code": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__industrySector": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__sdo": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__version": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____title": "", "@sage/xtrem-master-data/pages__supplier_certificate_panel__validUntil____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel___id____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__cancel____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__confirm____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__isDefaultItemSupplier____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__title__category__name": "", "@sage/xtrem-master-data/pages__supplier_item_panel__item____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier_item_panel__item____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__mainSection____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__minimumPurchaseQuantity____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__new____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseLeadTime____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__supplier____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemCode____title": "", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemName____title": "", "@sage/xtrem-master-data/pages__team____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__team____objectTypePlural": "", "@sage/xtrem-master-data/pages__team____objectTypeSingular": "", "@sage/xtrem-master-data/pages__team____title": "", "@sage/xtrem-master-data/pages__team__description____title": "", "@sage/xtrem-master-data/pages__team__id____title": "", "@sage/xtrem-master-data/pages__team__mainBlock____title": "", "@sage/xtrem-master-data/pages__team__mainSection____title": "", "@sage/xtrem-master-data/pages__team__name____title": "", "@sage/xtrem-master-data/pages__team__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__team__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__bulkActions__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeFrom__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeTo__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__postfix": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__image__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__line2__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__location__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__resourceGroup__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__weeklyShift__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__2": "", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__3": "", "@sage/xtrem-master-data/pages__tool_resource____objectTypePlural": "", "@sage/xtrem-master-data/pages__tool_resource____objectTypeSingular": "", "@sage/xtrem-master-data/pages__tool_resource____title": "", "@sage/xtrem-master-data/pages__tool_resource___id____title": "", "@sage/xtrem-master-data/pages__tool_resource__activeFrom____title": "", "@sage/xtrem-master-data/pages__tool_resource__activeTo____title": "", "@sage/xtrem-master-data/pages__tool_resource__addCostCategory____title": "", "@sage/xtrem-master-data/pages__tool_resource__blockDetails____title": "", "@sage/xtrem-master-data/pages__tool_resource__blockWeekly____title": "", "@sage/xtrem-master-data/pages__tool_resource__cancelSidePanel____title": "", "@sage/xtrem-master-data/pages__tool_resource__consumptionMode____title": "", "@sage/xtrem-master-data/pages__tool_resource__costBlock____title": "", "@sage/xtrem-master-data/pages__tool_resource__costSection____title": "", "@sage/xtrem-master-data/pages__tool_resource__description____title": "", "@sage/xtrem-master-data/pages__tool_resource__efficiency____postfix": "", "@sage/xtrem-master-data/pages__tool_resource__efficiency____title": "", "@sage/xtrem-master-data/pages__tool_resource__fullWeek____title": "", "@sage/xtrem-master-data/pages__tool_resource__hoursTracked____title": "", "@sage/xtrem-master-data/pages__tool_resource__id____title": "", "@sage/xtrem-master-data/pages__tool_resource__isActive____title": "", "@sage/xtrem-master-data/pages__tool_resource__item____columns__title__category__name": "", "@sage/xtrem-master-data/pages__tool_resource__item____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__tool_resource__item____title": "", "@sage/xtrem-master-data/pages__tool_resource__location____columns__title__locationType__id": "", "@sage/xtrem-master-data/pages__tool_resource__location____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__tool_resource__location____title": "", "@sage/xtrem-master-data/pages__tool_resource__name____title": "", "@sage/xtrem-master-data/pages__tool_resource__quantity____title": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCapacity____title": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costCategory__name": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costUnit__name": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__indirectCostSection__name": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__runCost": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__setupCost": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____dropdownActions__title": "", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____title": "", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____title": "", "@sage/xtrem-master-data/pages__tool_resource__resourceImage____title": "", "@sage/xtrem-master-data/pages__tool_resource__saveSidePanel____title": "", "@sage/xtrem-master-data/pages__tool_resource__section____title": "", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__name": "", "@sage/xtrem-master-data/pages__tool_resource__site____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__tool_resource__toolDetails____title": "", "@sage/xtrem-master-data/pages__tool_resource__unitProduced____title": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__capacity": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__dailyShift": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__day": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift1": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift2": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift3": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift4": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift5": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____title": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____columns__title__formattedCapacity": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____title": "", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line_4__title": "", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line3__title": "", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypePlural": "", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypeSingular": "", "@sage/xtrem-master-data/pages__unit_of_measure____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__addConversion____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title__2": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__item__name__title": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__lookupDialogTitle__item__name": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__2": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__3": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__4": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__coefficient": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__customer__businessEntity__name": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__fromUnit__name": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__isStandard": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__item__name": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__type": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____dropdownActions__title": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__conversionSection____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__decimalDigits____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__description____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__generalSection____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__id____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__isActive____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__mainBlock____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__name____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__symbol____title": "", "@sage/xtrem-master-data/pages__unit_of_measure__type____title": "", "@sage/xtrem-master-data/pages__utils__notification__custom_validation_error": "", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__formattedCapacity__title": "", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__isFullWeek__title": "", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__optionsMenu__title": "", "@sage/xtrem-master-data/pages__weekly_shift____objectTypePlural": "", "@sage/xtrem-master-data/pages__weekly_shift____objectTypeSingular": "", "@sage/xtrem-master-data/pages__weekly_shift____title": "", "@sage/xtrem-master-data/pages__weekly_shift___id____title": "", "@sage/xtrem-master-data/pages__weekly_shift__detailsBlock____title": "", "@sage/xtrem-master-data/pages__weekly_shift__formattedCapacity____title": "", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____title": "", "@sage/xtrem-master-data/pages__weekly_shift__id____title": "", "@sage/xtrem-master-data/pages__weekly_shift__isFullWeek____title": "", "@sage/xtrem-master-data/pages__weekly_shift__mainBlock____title": "", "@sage/xtrem-master-data/pages__weekly_shift__mainSection____title": "", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____title": "", "@sage/xtrem-master-data/pages__weekly_shift__name____title": "", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____title": "", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____title": "", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____title": "", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____title": "", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__id": "", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__name": "", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____lookupDialogTitle": "", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____title": "", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_content": "", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_title": "", "@sage/xtrem-master-data/pages_currency_delete_inverse_rate_page_dialog_content": "", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_content": "", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_title": "", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_content": "", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_title": "", "@sage/xtrem-master-data/pages_sidebar_block_title_definition": "", "@sage/xtrem-master-data/pages_sidebar_block_title_price": "", "@sage/xtrem-master-data/pages_sidebar_block_title_ranges": "", "@sage/xtrem-master-data/pages_sidebar_tab_title_definition": "", "@sage/xtrem-master-data/pages_sidebar_tab_title_information": "", "@sage/xtrem-master-data/pages_sidebar_tab_title_prices": "", "@sage/xtrem-master-data/pages_sidebar_tab_title_ranges": "", "@sage/xtrem-master-data/pages_site__address_mandatory": "", "@sage/xtrem-master-data/pages_supplier__address_mandatory": "", "@sage/xtrem-master-data/pages-cancel-keep": "", "@sage/xtrem-master-data/pages-confirm-apply": "", "@sage/xtrem-master-data/pages-confirm-apply-new": "", "@sage/xtrem-master-data/pages-confirm-cancel": "", "@sage/xtrem-master-data/pages-confirm-continue": "", "@sage/xtrem-master-data/pages-confirm-delete": "", "@sage/xtrem-master-data/pages-confirm-no": "", "@sage/xtrem-master-data/pages-confirm-send": "", "@sage/xtrem-master-data/pages-confirm-yes": "", "@sage/xtrem-master-data/permission__create__name": "", "@sage/xtrem-master-data/permission__delete__name": "", "@sage/xtrem-master-data/permission__manage__name": "", "@sage/xtrem-master-data/permission__read__name": "", "@sage/xtrem-master-data/permission__update__name": "", "@sage/xtrem-master-data/sales-to-stock-unit-must-be-one": "", "@sage/xtrem-master-data/service_options__allocation_transfer_option__name": "", "@sage/xtrem-master-data/service_options__bill_of_material_revision_service_option__name": "", "@sage/xtrem-master-data/service_options__customer_360_view_option__name": "", "@sage/xtrem-master-data/service_options__datev_option__name": "", "@sage/xtrem-master-data/service_options__fifo_valuation_method_option__name": "", "@sage/xtrem-master-data/service_options__landed_cost_option__name": "", "@sage/xtrem-master-data/service_options__landed_cost_order_option__name": "", "@sage/xtrem-master-data/service_options__landed_cost_stock_transfer_option__name": "", "@sage/xtrem-master-data/service_options__order_to_order_option__name": "", "@sage/xtrem-master-data/service_options__phantom_item_option__name": "", "@sage/xtrem-master-data/service_options__serial_number_option__name": "", "@sage/xtrem-master-data/site-extension-financial-currency-not-defined": "", "@sage/xtrem-master-data/telephone-validation-error": "", "@sage/xtrem-master-data/update-confirmation": "", "@sage/xtrem-master-data/use-existing-business-entity": "", "@sage/xtrem-master-data/value-must-be-greater-than-current-sequence": "", "@sage/xtrem-master-data/value-must-be-positive": "", "@sage/xtrem-master-data/value-must-not-exceed-the-length-of-sequence-number": "", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__addresses__title": "", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__contacts__title": "", "@sage/xtrem-master-data/widgets__customer_contact_list____title": ""}