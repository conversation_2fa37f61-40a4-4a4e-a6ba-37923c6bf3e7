{"@sage/xtrem-master-data/activity__allergen__name": "Allergen", "@sage/xtrem-master-data/activity__bom_revision_sequence__name": "BOM revision sequence", "@sage/xtrem-master-data/activity__business_entity__name": "Business entity", "@sage/xtrem-master-data/activity__capability_level__name": "Capability level", "@sage/xtrem-master-data/activity__container__name": "Container", "@sage/xtrem-master-data/activity__cost_category__name": "Cost category", "@sage/xtrem-master-data/activity__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__customer__name": "Customer", "@sage/xtrem-master-data/activity__customer_price_reason__name": "Customer price reason", "@sage/xtrem-master-data/activity__customer_supplier_category__name": "Customer supplier category", "@sage/xtrem-master-data/activity__daily_shift__name": "Daily shift", "@sage/xtrem-master-data/activity__delivery_mode__name": "Delivery mode", "@sage/xtrem-master-data/activity__employee__name": "Employee", "@sage/xtrem-master-data/activity__ghs_classification__name": "GHS classification", "@sage/xtrem-master-data/activity__group_resource__name": "Group resource", "@sage/xtrem-master-data/activity__incoterm__name": "Incoterms rule", "@sage/xtrem-master-data/activity__indirect_cost_origin__name": "Indirect cost origin", "@sage/xtrem-master-data/activity__indirect_cost_section__name": "Indirect cost section", "@sage/xtrem-master-data/activity__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/activity__item_category__name": "Item category", "@sage/xtrem-master-data/activity__item_site__name": "Item-site", "@sage/xtrem-master-data/activity__labour_resource__name": "Labor resource", "@sage/xtrem-master-data/activity__license_plate_number__name": "License plate number", "@sage/xtrem-master-data/activity__location__name": "Location", "@sage/xtrem-master-data/activity__location_sequence__name": "Location sequence", "@sage/xtrem-master-data/activity__location_type__name": "Location type", "@sage/xtrem-master-data/activity__location_zone__name": "Location zone", "@sage/xtrem-master-data/activity__machine_resource__name": "Machine resource", "@sage/xtrem-master-data/activity__payment_term__name": "Payment term", "@sage/xtrem-master-data/activity__reason_code__name": "Reason code", "@sage/xtrem-master-data/activity__sequence_number__name": "Sequence number", "@sage/xtrem-master-data/activity__sequence_number_assignment__name": "Sequence number assignment", "@sage/xtrem-master-data/activity__shift_detail__name": "Shift detail", "@sage/xtrem-master-data/activity__standard__name": "Standard", "@sage/xtrem-master-data/activity__supplier__name": "Supplier", "@sage/xtrem-master-data/activity__supplier_certificate__name": "Supplier certificate", "@sage/xtrem-master-data/activity__tool_resource__name": "Tool resource", "@sage/xtrem-master-data/activity__unit_of_measure__name": "Unit of measure", "@sage/xtrem-master-data/activity__weekly_shift__name": "Weekly shift", "@sage/xtrem-master-data/business_entity_address_node_only_one_primary_contact": "", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological-control-must-be-active": "Sequence number {{sequenceNumber}}: The chronological control must be active for the FR legislation.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-cannot-be-later-than-today": "The document date cannot be later than today.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-earlier-than-previous-document-date": "The document date {{current}} is earlier than the previous document date {{previousDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-later-than-next-document-date": "The document date {{current}} is later than the next document date {{nextDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-component-type-value": "Incorrect component type: {{type}}", "@sage/xtrem-master-data/classes__sequence-number-generator__monthly-sequence-numbers-not-allowed": "Sequence number {{sequenceNumber}}: Monthly sequence numbers are not allowed.", "@sage/xtrem-master-data/classes__sequence-number-generator__no-sequence-number-assigned": "No sequence number has been assign for this document type.", "@sage/xtrem-master-data/classes__sequence-number-generator__node-instance-is-required": "The node instance is required.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-at-application-level-definition-and-no-input-value-for-site": "The {{id}} sequence number is defined at the {{definitionLevel}} level and the site is not entered.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-defined-at-application-level": "The {{id}} sequence number is not defined at the application level. You must enter a site or company in the sequence number components.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-instance-not-found": "Sequence number instance not found.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-not-defined": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_continue": "Continue", "@sage/xtrem-master-data/client_functions__master_data__resync_status_message": "You are about to update the status.", "@sage/xtrem-master-data/client_functions__master_data__resync_status_title": "Check and update status.", "@sage/xtrem-master-data/client_functions__master_data_resync_submitted": "Resync document request submitted:({{batchTaskId}}).", "@sage/xtrem-master-data/company_node_only_one_primary_address": "Only one primary address is allowed.", "@sage/xtrem-master-data/company_node_only_one_primary_contact": "You can only define one primary contact.", "@sage/xtrem-master-data/control__item__landedCost_service_option_inactive": "You can create an item with the Landed cost type only if the Landed cost service option is active.", "@sage/xtrem-master-data/control-begin__sequence-number__definition_level_is_not_present_in_components": "The sequence number is defined at the {{definitionLevel}} level. You need to enter the {{definitionLevel}} in the Components grid.", "@sage/xtrem-master-data/control-begin__sequence-number__no_sequence_number_component": "You need to enter a sequence number component.", "@sage/xtrem-master-data/control-begin__sequence-number__rtz_level_is_not_present_in_components": "When the Reset frequency is set to {{rtzLevel}}, you need to enter the {{type}} in the Components grid.", "@sage/xtrem-master-data/control-begin__sequence-number__sequence-numeric-wrong-component": "The sequence number type is numerical. You can only enter numerical components.", "@sage/xtrem-master-data/create": "Generate", "@sage/xtrem-master-data/create-confirmation": "Record created", "@sage/xtrem-master-data/data_types__address_entity_type_enum__name": "Address entity type enum", "@sage/xtrem-master-data/data_types__address_line_data_type__name": "Address line data type", "@sage/xtrem-master-data/data_types__amount_data_type__name": "Amount data type", "@sage/xtrem-master-data/data_types__amount_in_company_currency__name": "Amount in company currency", "@sage/xtrem-master-data/data_types__amount_in_financial_site_currency__name": "Amount in financial site currency", "@sage/xtrem-master-data/data_types__amount_in_transaction_currency__name": "Amount in transaction currency", "@sage/xtrem-master-data/data_types__approval_status_enum__name": "Approval status enum", "@sage/xtrem-master-data/data_types__base_certificate_property_data_type__name": "Base certificate property data type", "@sage/xtrem-master-data/data_types__base_decimal__name": "Base decimal", "@sage/xtrem-master-data/data_types__base_display_status_enum__name": "Base display status enum", "@sage/xtrem-master-data/data_types__base_origin_enum__name": "Base origin enum", "@sage/xtrem-master-data/data_types__base_price__name": "Base price", "@sage/xtrem-master-data/data_types__base_sequence_number_component_type_enum__name": "Base sequence number component type enum", "@sage/xtrem-master-data/data_types__base_status_enum__name": "Base status enum", "@sage/xtrem-master-data/data_types__bom_revision_sequence__name": "BOM revision sequence", "@sage/xtrem-master-data/data_types__business_entity__name": "Business entity", "@sage/xtrem-master-data/data_types__business_entity_id__name": "Business entity ID", "@sage/xtrem-master-data/data_types__business_entity_type_enum__name": "Business entity type enum", "@sage/xtrem-master-data/data_types__business_relation_type_enum__name": "Business relation type enum", "@sage/xtrem-master-data/data_types__capacity_percentage__name": "Capacity percentage", "@sage/xtrem-master-data/data_types__city_data_type__name": "City data type", "@sage/xtrem-master-data/data_types__coefficient_data_type__name": "Coefficient data type", "@sage/xtrem-master-data/data_types__company_price_data_type__name": "Company price data type", "@sage/xtrem-master-data/data_types__constant_sequence_data_type__name": "Constant sequence data type", "@sage/xtrem-master-data/data_types__consumption_mode_enum__name": "Consumption mode enum", "@sage/xtrem-master-data/data_types__contact_position_data_type__name": "Contact position data type", "@sage/xtrem-master-data/data_types__contact_property_data_type__name": "Contact property data type", "@sage/xtrem-master-data/data_types__contact_role_enum__name": "Contact role enum", "@sage/xtrem-master-data/data_types__container_type_enum__name": "Container type enum", "@sage/xtrem-master-data/data_types__cost_calculation_method_enum__name": "Cost calculation method enum", "@sage/xtrem-master-data/data_types__cost_category_type_enum__name": "Cost category type enum", "@sage/xtrem-master-data/data_types__cost_data_type__name": "Cost data type", "@sage/xtrem-master-data/data_types__cost_valuation_method_enum__name": "Cost valuation method enum", "@sage/xtrem-master-data/data_types__cost_value_data_type__name": "Cost value data type", "@sage/xtrem-master-data/data_types__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__customer__name": "Customer", "@sage/xtrem-master-data/data_types__customer_display_status_enum__name": "Customer display status enum", "@sage/xtrem-master-data/data_types__customer_on_hold_type_enum__name": "Customer on hold type enum", "@sage/xtrem-master-data/data_types__customer_supplier_category__name": "Customer supplier category", "@sage/xtrem-master-data/data_types__delivery_mode__name": "Delivery mode", "@sage/xtrem-master-data/data_types__discount_charge_calculation_basis_enum__name": "Discount charge calculation basis enum", "@sage/xtrem-master-data/data_types__discount_charge_calculation_rule_enum__name": "Discount charge calculation rule enum", "@sage/xtrem-master-data/data_types__discount_charge_sign_enum__name": "Discount charge sign enum", "@sage/xtrem-master-data/data_types__discount_charge_value_type_enum__name": "Discount charge value type enum", "@sage/xtrem-master-data/data_types__discount_or_penalty_type_enum__name": "Discount or penalty type enum", "@sage/xtrem-master-data/data_types__document_number__name": "Document number", "@sage/xtrem-master-data/data_types__due_date_type_enum__name": "Due date type enum", "@sage/xtrem-master-data/data_types__duration_data_type__name": "Duration data type", "@sage/xtrem-master-data/data_types__ean_number_data_type__name": "EAN number data type", "@sage/xtrem-master-data/data_types__efficiency_percentage__name": "Efficiency percentage", "@sage/xtrem-master-data/data_types__email_action_type_enum__name": "Email action type enum", "@sage/xtrem-master-data/data_types__exchange_rate__name": "Exchange rate", "@sage/xtrem-master-data/data_types__extra_large_string__name": "Extra large string", "@sage/xtrem-master-data/data_types__fake_site_reference_datatype__name": "", "@sage/xtrem-master-data/data_types__incoterm__name": "Incoterm", "@sage/xtrem-master-data/data_types__incoterm_data_type__name": "Incoterm data type", "@sage/xtrem-master-data/data_types__indirect_cost_percentage__name": "Indirect cost percentage", "@sage/xtrem-master-data/data_types__input_sequence_data_type__name": "Input sequence data type", "@sage/xtrem-master-data/data_types__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__item_category__name": "Item category", "@sage/xtrem-master-data/data_types__item_category_type_enum__name": "Item category type enum", "@sage/xtrem-master-data/data_types__item_flow_type_enum__name": "Item flow type enum", "@sage/xtrem-master-data/data_types__item_price_type_enum__name": "Item price type enum", "@sage/xtrem-master-data/data_types__item_status_enum__name": "Item status enum", "@sage/xtrem-master-data/data_types__item_type_enum__name": "Item type enum", "@sage/xtrem-master-data/data_types__label_format_data_type__name": "Label format data type", "@sage/xtrem-master-data/data_types__large_string__name": "Large string", "@sage/xtrem-master-data/data_types__legal_entity_enum__name": "Legal entity enum", "@sage/xtrem-master-data/data_types__localized_sic_description_data_type__name": "Localized sic description data type", "@sage/xtrem-master-data/data_types__location__name": "Location", "@sage/xtrem-master-data/data_types__location_category_enum__name": "Location category enum", "@sage/xtrem-master-data/data_types__location_sequence__name": "Location sequence", "@sage/xtrem-master-data/data_types__lot_management_enum__name": "Lot management enum", "@sage/xtrem-master-data/data_types__master_data_company__name": "Master data company", "@sage/xtrem-master-data/data_types__master_data_site__name": "Master data site", "@sage/xtrem-master-data/data_types__medium_string__name": "Medium string", "@sage/xtrem-master-data/data_types__model_data_type__name": "Model data type", "@sage/xtrem-master-data/data_types__note__name": "Note", "@sage/xtrem-master-data/data_types__order_cost_data_type__name": "Order cost data type", "@sage/xtrem-master-data/data_types__order_type_enum__name": "Order type enum", "@sage/xtrem-master-data/data_types__payment_method_enum__name": "Payment method enum", "@sage/xtrem-master-data/data_types__payment_term__name": "Payment term", "@sage/xtrem-master-data/data_types__payment_term_data_type__name": "Payment term data type", "@sage/xtrem-master-data/data_types__payment_term_discount_or_penalty_type_enum__name": "Payment term discount or penalty type enum", "@sage/xtrem-master-data/data_types__percentage__name": "Percentage", "@sage/xtrem-master-data/data_types__percentage_work_order_data_type__name": "Percentage work order data type", "@sage/xtrem-master-data/data_types__period_type_enum__name": "Period type enum", "@sage/xtrem-master-data/data_types__postcode_data_type__name": "Postcode data type", "@sage/xtrem-master-data/data_types__potency_percentage__name": "Potency percentage", "@sage/xtrem-master-data/data_types__preferred_process_enum__name": "Preferred process enum", "@sage/xtrem-master-data/data_types__price__name": "Price", "@sage/xtrem-master-data/data_types__price_data_type__name": "Price data type", "@sage/xtrem-master-data/data_types__price_in_sales_price__name": "Price in sales price", "@sage/xtrem-master-data/data_types__price_percentage__name": "Price percentage", "@sage/xtrem-master-data/data_types__quantity__name": "Quantity", "@sage/xtrem-master-data/data_types__quantity_in_purchase_unit__name": "Quantity in purchase unit", "@sage/xtrem-master-data/data_types__quantity_in_sales_unit__name": "Quantity in sales unit", "@sage/xtrem-master-data/data_types__quantity_in_stock_unit__name": "Quantity in stock unit", "@sage/xtrem-master-data/data_types__quantity_in_unit__name": "Quantity in unit", "@sage/xtrem-master-data/data_types__quantity_in_volume_unit__name": "Quantity in volume unit", "@sage/xtrem-master-data/data_types__quantity_in_weight_unit__name": "Quantity in weight unit", "@sage/xtrem-master-data/data_types__reason_code__name": "Reason code", "@sage/xtrem-master-data/data_types__region_data_type__name": "Region data type", "@sage/xtrem-master-data/data_types__replenishment_method_enum__name": "Replenishment method enum", "@sage/xtrem-master-data/data_types__resource_cost__name": "Resource cost", "@sage/xtrem-master-data/data_types__resource_group_type_enum__name": "Resource group type enum", "@sage/xtrem-master-data/data_types__run_time_data_type__name": "Run time data type", "@sage/xtrem-master-data/data_types__scrap_factor_percentage__name": "Scrap factor percentage", "@sage/xtrem-master-data/data_types__sequence_counter_definition_level_enum__name": "Sequence counter definition level enum", "@sage/xtrem-master-data/data_types__sequence_number__name": "Sequence number", "@sage/xtrem-master-data/data_types__sequence_number_reset_frequency_enum__name": "Sequence number reset frequency enum", "@sage/xtrem-master-data/data_types__sequence_number_type_enum__name": "Sequence number type enum", "@sage/xtrem-master-data/data_types__serial_number_management_enum__name": "Serial number management enum", "@sage/xtrem-master-data/data_types__serial_number_usage_enum__name": "Serial number usage enum", "@sage/xtrem-master-data/data_types__setup_time_data_type__name": "Setup time data type", "@sage/xtrem-master-data/data_types__shift_data_type__name": "Shift data type", "@sage/xtrem-master-data/data_types__standard_property_data_type__name": "Standard property data type", "@sage/xtrem-master-data/data_types__stock_management_mode_enum__name": "Stock management mode enum", "@sage/xtrem-master-data/data_types__stock_quantity__name": "Stock quantity", "@sage/xtrem-master-data/data_types__stock_quantity_variance_percentage__name": "Stock quantity variance percentage", "@sage/xtrem-master-data/data_types__stock_variation_value__name": "Stock variation value", "@sage/xtrem-master-data/data_types__supplier__name": "Supplier", "@sage/xtrem-master-data/data_types__supplier_item_property_data_type__name": "Supplier item property data type", "@sage/xtrem-master-data/data_types__supplier_type_enum__name": "Supplier type enum", "@sage/xtrem-master-data/data_types__symbol_data_type__name": "Symbol data type", "@sage/xtrem-master-data/data_types__tax_calculation_status_enum__name": "Tax calculation status enum", "@sage/xtrem-master-data/data_types__telephone_number_data_type__name": "Telephone number data type", "@sage/xtrem-master-data/data_types__time_data_type__name": "Time data type", "@sage/xtrem-master-data/data_types__time_zone__name": "Time zone", "@sage/xtrem-master-data/data_types__title_enum__name": "Title enum", "@sage/xtrem-master-data/data_types__unit_conversion_coefficient__name": "Unit conversion coefficient", "@sage/xtrem-master-data/data_types__unit_conversion_type_enum__name": "Unit conversion type enum", "@sage/xtrem-master-data/data_types__unit_of_measure__name": "Unit of measure", "@sage/xtrem-master-data/data_types__unit_type_enum__name": "Unit type enum", "@sage/xtrem-master-data/data_types__version_data_type__name": "Version data type", "@sage/xtrem-master-data/data_types__volume_percentage__name": "Volume percentage", "@sage/xtrem-master-data/data_types__week_days_enum__name": "Week days enum", "@sage/xtrem-master-data/data_types__weight_percentage__name": "Weight percentage", "@sage/xtrem-master-data/data_types__work_in_progress_document_type_enum__name": "Work in progress document type enum", "@sage/xtrem-master-data/data_types__zone_type_enum__name": "Zone type enum", "@sage/xtrem-master-data/data-types/percentage__value_greater_than_a_maximum": "The percentage value ({{value}}) must not exceed {{maxValue}}.", "@sage/xtrem-master-data/data-types/percentage__value_less_than_a_minimum": "The percentage value ({{value}}) must be greater than {{minValue}}.", "@sage/xtrem-master-data/data-types/percentage__value_not_in_allowed_range": "The percentage value ({{value}}) must be between {{minValue}} and {{maxValue}}.", "@sage/xtrem-master-data/delete-confirmation": "Record deleted", "@sage/xtrem-master-data/edit-create-customer-price": "Add new sales price", "@sage/xtrem-master-data/edit-create-line": "Add new customer", "@sage/xtrem-master-data/edit-create-supplier": "Add new supplier", "@sage/xtrem-master-data/edit-create-supplier-price": "Add new supplier price", "@sage/xtrem-master-data/email-validation-error": "Invalid email address", "@sage/xtrem-master-data/enums__address_entity_type__businessEntity": "Business entity", "@sage/xtrem-master-data/enums__address_entity_type__company": "Company", "@sage/xtrem-master-data/enums__address_entity_type__customer": "Customer", "@sage/xtrem-master-data/enums__address_entity_type__site": "Site", "@sage/xtrem-master-data/enums__address_entity_type__supplier": "Supplier", "@sage/xtrem-master-data/enums__approval_status__approved": "Approved", "@sage/xtrem-master-data/enums__approval_status__changeRequested": "Change requested", "@sage/xtrem-master-data/enums__approval_status__confirmed": "Confirmed", "@sage/xtrem-master-data/enums__approval_status__draft": "Draft", "@sage/xtrem-master-data/enums__approval_status__pendingApproval": "Pending approval", "@sage/xtrem-master-data/enums__approval_status__rejected": "Rejected", "@sage/xtrem-master-data/enums__base_display_status__approved": "Approved", "@sage/xtrem-master-data/enums__base_display_status__changeRequested": "Change requested", "@sage/xtrem-master-data/enums__base_display_status__closed": "Closed", "@sage/xtrem-master-data/enums__base_display_status__confirmed": "Confirmed", "@sage/xtrem-master-data/enums__base_display_status__credited": "Credited", "@sage/xtrem-master-data/enums__base_display_status__draft": "Draft", "@sage/xtrem-master-data/enums__base_display_status__error": "Error", "@sage/xtrem-master-data/enums__base_display_status__invoiced": "Invoiced", "@sage/xtrem-master-data/enums__base_display_status__noVariance": "No variance", "@sage/xtrem-master-data/enums__base_display_status__ordered": "Ordered", "@sage/xtrem-master-data/enums__base_display_status__paid": "Paid", "@sage/xtrem-master-data/enums__base_display_status__partiallyCredited": "Partially credited", "@sage/xtrem-master-data/enums__base_display_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-master-data/enums__base_display_status__partiallyOrdered": "Partially ordered", "@sage/xtrem-master-data/enums__base_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-master-data/enums__base_display_status__partiallyReceived": "Partially received", "@sage/xtrem-master-data/enums__base_display_status__partiallyReturned": "Partially returned", "@sage/xtrem-master-data/enums__base_display_status__partiallyShipped": "Partially shipped", "@sage/xtrem-master-data/enums__base_display_status__pending": "Pending", "@sage/xtrem-master-data/enums__base_display_status__pendingApproval": "Pending approval", "@sage/xtrem-master-data/enums__base_display_status__posted": "Posted", "@sage/xtrem-master-data/enums__base_display_status__postingError": "Posting error", "@sage/xtrem-master-data/enums__base_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-master-data/enums__base_display_status__quote": "Quote", "@sage/xtrem-master-data/enums__base_display_status__readyToProcess": "Ready to process", "@sage/xtrem-master-data/enums__base_display_status__readyToShip": "Ready to ship", "@sage/xtrem-master-data/enums__base_display_status__received": "Received", "@sage/xtrem-master-data/enums__base_display_status__rejected": "Rejected", "@sage/xtrem-master-data/enums__base_display_status__returned": "Returned", "@sage/xtrem-master-data/enums__base_display_status__shipped": "Shipped", "@sage/xtrem-master-data/enums__base_display_status__stockError": "Stock error", "@sage/xtrem-master-data/enums__base_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-master-data/enums__base_display_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__varianceApproved": "<PERSON><PERSON><PERSON> approved", "@sage/xtrem-master-data/enums__base_origin__direct": "Direct", "@sage/xtrem-master-data/enums__base_origin__invoice": "Invoice", "@sage/xtrem-master-data/enums__base_origin__order": "Order", "@sage/xtrem-master-data/enums__base_origin__purchaseCreditMemo": "Purchase credit memo", "@sage/xtrem-master-data/enums__base_origin__purchaseInvoice": "Purchase invoice", "@sage/xtrem-master-data/enums__base_origin__purchaseOrder": "Purchase order", "@sage/xtrem-master-data/enums__base_origin__purchaseReceipt": "Purchase receipt", "@sage/xtrem-master-data/enums__base_origin__purchaseRequisition": "Purchase requisition", "@sage/xtrem-master-data/enums__base_origin__purchaseReturn": "Purchase return", "@sage/xtrem-master-data/enums__base_origin__purchaseSuggestion": "Purchase suggestion", "@sage/xtrem-master-data/enums__base_origin__return": "Return", "@sage/xtrem-master-data/enums__base_origin__shipment": "Shipment", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__company": "Company", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__constant": "Constant", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__day": "Day", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__month": "Month", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceAlpha": "Sequence alpha", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__site": "Site", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__week": "Week", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__year": "Year", "@sage/xtrem-master-data/enums__base_status__approved": "Approved", "@sage/xtrem-master-data/enums__base_status__changeRequested": "Change requested", "@sage/xtrem-master-data/enums__base_status__closed": "Closed", "@sage/xtrem-master-data/enums__base_status__confirmed": "Confirmed", "@sage/xtrem-master-data/enums__base_status__credited": "Credited", "@sage/xtrem-master-data/enums__base_status__draft": "Draft", "@sage/xtrem-master-data/enums__base_status__error": "Error", "@sage/xtrem-master-data/enums__base_status__inProgress": "In progress", "@sage/xtrem-master-data/enums__base_status__invoiced": "Invoiced", "@sage/xtrem-master-data/enums__base_status__noVariance": "No variance", "@sage/xtrem-master-data/enums__base_status__partiallyCredited": "Partially credited", "@sage/xtrem-master-data/enums__base_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-master-data/enums__base_status__partiallyReceived": "Partially received", "@sage/xtrem-master-data/enums__base_status__partiallyReturned": "Partially returned", "@sage/xtrem-master-data/enums__base_status__partiallyShipped": "Partially shipped", "@sage/xtrem-master-data/enums__base_status__pending": "Pending", "@sage/xtrem-master-data/enums__base_status__pendingApproval": "Pending approval", "@sage/xtrem-master-data/enums__base_status__posted": "Posted", "@sage/xtrem-master-data/enums__base_status__postingError": "Posting error", "@sage/xtrem-master-data/enums__base_status__postingInProgress": "Posting in progress", "@sage/xtrem-master-data/enums__base_status__quote": "Quote", "@sage/xtrem-master-data/enums__base_status__readyToProcess": "Ready to process", "@sage/xtrem-master-data/enums__base_status__readyToShip": "Ready to ship", "@sage/xtrem-master-data/enums__base_status__received": "Received", "@sage/xtrem-master-data/enums__base_status__rejected": "Rejected", "@sage/xtrem-master-data/enums__base_status__returned": "Returned", "@sage/xtrem-master-data/enums__base_status__shipped": "Shipped", "@sage/xtrem-master-data/enums__base_status__stockError": "Stock error", "@sage/xtrem-master-data/enums__base_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-master-data/enums__base_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__varianceApproved": "<PERSON><PERSON><PERSON> approved", "@sage/xtrem-master-data/enums__business_entity_type__all": "All", "@sage/xtrem-master-data/enums__business_entity_type__customer": "Customer", "@sage/xtrem-master-data/enums__business_entity_type__supplier": "Supplier", "@sage/xtrem-master-data/enums__business_relation_type__customer": "Customer", "@sage/xtrem-master-data/enums__business_relation_type__supplier": "Supplier", "@sage/xtrem-master-data/enums__consumption_mode__none": "None", "@sage/xtrem-master-data/enums__consumption_mode__quantity": "Quantity", "@sage/xtrem-master-data/enums__consumption_mode__time": "Time", "@sage/xtrem-master-data/enums__contact_role__commercialContact": "Commercial contact", "@sage/xtrem-master-data/enums__contact_role__financialContact": "Financial contact", "@sage/xtrem-master-data/enums__contact_role__mainContact": "Main contact", "@sage/xtrem-master-data/enums__container_type__barrel": "Barrel", "@sage/xtrem-master-data/enums__container_type__bigBag": "Big bag", "@sage/xtrem-master-data/enums__container_type__box": "Box", "@sage/xtrem-master-data/enums__container_type__container": "Container", "@sage/xtrem-master-data/enums__container_type__other": "Other", "@sage/xtrem-master-data/enums__container_type__pack": "Pack", "@sage/xtrem-master-data/enums__container_type__pallet": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_calculation_method__compound": "Compound", "@sage/xtrem-master-data/enums__cost_calculation_method__cumulate": "Cumulated", "@sage/xtrem-master-data/enums__cost_category_type__budgeted": "Budgeted", "@sage/xtrem-master-data/enums__cost_category_type__other": "Other", "@sage/xtrem-master-data/enums__cost_category_type__simulated": "Simulated", "@sage/xtrem-master-data/enums__cost_category_type__standard": "Standard", "@sage/xtrem-master-data/enums__cost_valuation_method__averageCost": "Average unit cost", "@sage/xtrem-master-data/enums__cost_valuation_method__fifoCost": "FIFO cost", "@sage/xtrem-master-data/enums__cost_valuation_method__standardCost": "Standard cost", "@sage/xtrem-master-data/enums__customer_display_status__active": "Active", "@sage/xtrem-master-data/enums__customer_display_status__inactive": "Inactive", "@sage/xtrem-master-data/enums__customer_display_status__onHold": "On hold", "@sage/xtrem-master-data/enums__customer_on_hold_type__blocking": "Blocking", "@sage/xtrem-master-data/enums__customer_on_hold_type__none": "None", "@sage/xtrem-master-data/enums__customer_on_hold_type__warning": "Warning", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPrice": "Gross price", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPriceAndCompound": "Gross price and compound", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byLine": "By line", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byUnit": "By unit", "@sage/xtrem-master-data/enums__discount_charge_sign__decrease": "Decrease", "@sage/xtrem-master-data/enums__discount_charge_sign__increase": "Increase", "@sage/xtrem-master-data/enums__discount_charge_value_type__amount": "Amount", "@sage/xtrem-master-data/enums__discount_charge_value_type__percentage": "Percentage", "@sage/xtrem-master-data/enums__discount_or_penalty_type__amount": "Amount", "@sage/xtrem-master-data/enums__discount_or_penalty_type__percentage": "Percentage", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDate": "After invoice date", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDateAndExtendedToEndOfMonth": "After invoice date and extended to end of month", "@sage/xtrem-master-data/enums__due_date_type__afterTheEndOfTheMonthOfInvoiceDate": "After the end of the month of invoice date", "@sage/xtrem-master-data/enums__email_action_type__approved": "Approved", "@sage/xtrem-master-data/enums__email_action_type__rejected": "Rejected", "@sage/xtrem-master-data/enums__item_attribute__chemical": "Chemical", "@sage/xtrem-master-data/enums__item_attribute__food": "Food", "@sage/xtrem-master-data/enums__item_attribute__other": "Other", "@sage/xtrem-master-data/enums__item_category_type__allergen": "Allergen", "@sage/xtrem-master-data/enums__item_category_type__ghsClassification": "GHS classification", "@sage/xtrem-master-data/enums__item_category_type__none": "None", "@sage/xtrem-master-data/enums__item_flow_type__manufactured": "Manufactured", "@sage/xtrem-master-data/enums__item_flow_type__purchased": "Purchased", "@sage/xtrem-master-data/enums__item_flow_type__sold": "Sold", "@sage/xtrem-master-data/enums__item_flow_type__subcontracted": "Subcontracted", "@sage/xtrem-master-data/enums__item_price_type__discount": "Discount", "@sage/xtrem-master-data/enums__item_price_type__normal": "Normal", "@sage/xtrem-master-data/enums__item_price_type__specialOffer": "Special offer", "@sage/xtrem-master-data/enums__item_status__active": "Active", "@sage/xtrem-master-data/enums__item_status__inDevelopment": "In development", "@sage/xtrem-master-data/enums__item_status__notRenewed": "Not renewed", "@sage/xtrem-master-data/enums__item_status__notUsable": "Not usable", "@sage/xtrem-master-data/enums__item_status__obsolete": "Obsolete", "@sage/xtrem-master-data/enums__item_type__good": "Good", "@sage/xtrem-master-data/enums__item_type__landedCost": "Landed cost", "@sage/xtrem-master-data/enums__item_type__service": "Service", "@sage/xtrem-master-data/enums__legal_entity__corporation": "Corporation", "@sage/xtrem-master-data/enums__legal_entity__physicalPerson": "Physical person", "@sage/xtrem-master-data/enums__location_category__customer": "Customer", "@sage/xtrem-master-data/enums__location_category__dock": "Dock", "@sage/xtrem-master-data/enums__location_category__internal": "Internal", "@sage/xtrem-master-data/enums__location_category__subcontract": "Subcontracting", "@sage/xtrem-master-data/enums__location_category__virtual": "Virtual", "@sage/xtrem-master-data/enums__lot_management__lotManagement": "Lot", "@sage/xtrem-master-data/enums__lot_management__lotSublotManagement": "Lot and sublot", "@sage/xtrem-master-data/enums__lot_management__notManaged": "None", "@sage/xtrem-master-data/enums__order_type__closed": "Closed", "@sage/xtrem-master-data/enums__order_type__firm": "Firm", "@sage/xtrem-master-data/enums__order_type__planned": "Planned", "@sage/xtrem-master-data/enums__order_type__suggested": "Suggestion", "@sage/xtrem-master-data/enums__payment_method__ACH": "ACH", "@sage/xtrem-master-data/enums__payment_method__cash": "Cash", "@sage/xtrem-master-data/enums__payment_method__creditCard": "Credit card", "@sage/xtrem-master-data/enums__payment_method__EFT": "EFT", "@sage/xtrem-master-data/enums__payment_method__printedCheck": "Printed check", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__amount": "Amount", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__percentage": "Percentage", "@sage/xtrem-master-data/enums__payment_term_from__invoiceDate": "Invoice date", "@sage/xtrem-master-data/enums__period_type__day": "Day", "@sage/xtrem-master-data/enums__period_type__month": "Month", "@sage/xtrem-master-data/enums__period_type__week": "Week", "@sage/xtrem-master-data/enums__period_type__year": "Year", "@sage/xtrem-master-data/enums__preferred_process__production": "Production", "@sage/xtrem-master-data/enums__preferred_process__purchasing": "Purchasing", "@sage/xtrem-master-data/enums__replenishment_method__byMRP": "By MRP", "@sage/xtrem-master-data/enums__replenishment_method__byReorderPoint": "By reorder point", "@sage/xtrem-master-data/enums__replenishment_method__notManaged": "Not managed", "@sage/xtrem-master-data/enums__resource_group_type__labor": "Labor", "@sage/xtrem-master-data/enums__resource_group_type__machine": "Machine", "@sage/xtrem-master-data/enums__resource_group_type__subcontract": "Subcontracting", "@sage/xtrem-master-data/enums__resource_group_type__tool": "Tool", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__application": "Application", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__company": "Company", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__site": "Site", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__monthly": "Monthly", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__noReset": "No reset", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__yearly": "Yearly", "@sage/xtrem-master-data/enums__sequence_number_type__alphanumeric": "Alphanumeric", "@sage/xtrem-master-data/enums__sequence_number_type__numeric": "Numerical", "@sage/xtrem-master-data/enums__serial_number_management__managed": "Managed", "@sage/xtrem-master-data/enums__serial_number_management__notManaged": "None", "@sage/xtrem-master-data/enums__serial_number_usage__issueAndReceipt": "Issue and receipt", "@sage/xtrem-master-data/enums__serial_number_usage__issueOnly": "Issue only", "@sage/xtrem-master-data/enums__stock_management_mode__byOrder": "By order", "@sage/xtrem-master-data/enums__stock_management_mode__byProject": "By project", "@sage/xtrem-master-data/enums__stock_management_mode__onStock": "On stock", "@sage/xtrem-master-data/enums__supplier_type__chemical": "Chemical industry", "@sage/xtrem-master-data/enums__supplier_type__foodAndBeverage": "Food and beverage", "@sage/xtrem-master-data/enums__supplier_type__other": "Other", "@sage/xtrem-master-data/enums__tax_calculation_status__done": "Done", "@sage/xtrem-master-data/enums__tax_calculation_status__failed": "Failed", "@sage/xtrem-master-data/enums__tax_calculation_status__inProgress": "In progress", "@sage/xtrem-master-data/enums__tax_calculation_status__notDone": "Not done", "@sage/xtrem-master-data/enums__title__dr": "Dr.", "@sage/xtrem-master-data/enums__title__family": "Family", "@sage/xtrem-master-data/enums__title__master": "Master", "@sage/xtrem-master-data/enums__title__miss": "Miss", "@sage/xtrem-master-data/enums__title__mr": "Mr.", "@sage/xtrem-master-data/enums__title__mrs": "Mrs.", "@sage/xtrem-master-data/enums__title__ms": "Ms.", "@sage/xtrem-master-data/enums__title__prof": "Prof.", "@sage/xtrem-master-data/enums__unit_conversion_type__other": "Other", "@sage/xtrem-master-data/enums__unit_conversion_type__purchase": "Purchasing", "@sage/xtrem-master-data/enums__unit_conversion_type__sales": "Sales", "@sage/xtrem-master-data/enums__unit_type__area": "Area", "@sage/xtrem-master-data/enums__unit_type__each": "Each", "@sage/xtrem-master-data/enums__unit_type__length": "Length", "@sage/xtrem-master-data/enums__unit_type__temperature": "Temperature", "@sage/xtrem-master-data/enums__unit_type__time": "Time", "@sage/xtrem-master-data/enums__unit_type__volume": "Volume", "@sage/xtrem-master-data/enums__unit_type__weight": "Weight", "@sage/xtrem-master-data/enums__week_days__friday": "Friday", "@sage/xtrem-master-data/enums__week_days__monday": "Monday", "@sage/xtrem-master-data/enums__week_days__saturday": "Saturday", "@sage/xtrem-master-data/enums__week_days__sunday": "Sunday", "@sage/xtrem-master-data/enums__week_days__thursday": "Thursday", "@sage/xtrem-master-data/enums__week_days__tuesday": "Tuesday", "@sage/xtrem-master-data/enums__week_days__wednesday": "Wednesday", "@sage/xtrem-master-data/enums__work_in_progress_document_type__materialNeed": "Material requirement", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseOrder": "Purchase order", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReceipt": "Purchase receipt", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReturn": "Purchase return", "@sage/xtrem-master-data/enums__work_in_progress_document_type__salesOrder": "Sales order", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferOrder": "Stock transfer order", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferReceipt": "Stock transfer receipt", "@sage/xtrem-master-data/enums__work_in_progress_document_type__workOrder": "Work order", "@sage/xtrem-master-data/enums__zone_type__chemical": "Chemical", "@sage/xtrem-master-data/enums__zone_type__frozen": "Frozen", "@sage/xtrem-master-data/enums__zone_type__hazard": "Hazard", "@sage/xtrem-master-data/enums__zone_type__magnetic": "Magnetic", "@sage/xtrem-master-data/enums__zone_type__restricted": "Restricted", "@sage/xtrem-master-data/enums__zone_type__secured": "Secured", "@sage/xtrem-master-data/enums__zone_type__sensitive": "Sensitive", "@sage/xtrem-master-data/enums__zone_type__virtual": "Virtual", "@sage/xtrem-master-data/events__control__document_external_note_must_be_empty": "The external note must be empty if the 'isExternalNote' property is false.", "@sage/xtrem-master-data/events/control__address-control__postalcode-validation-error": "Invalid postal code", "@sage/xtrem-master-data/events/control__address-control__telephone-validation-error": "Invalid phone number", "@sage/xtrem-master-data/events/control__address-control__zipcode-validation-error": "Invalid ZIP code", "@sage/xtrem-master-data/events/control__base_sequence_number_control_length": "The component sequence length needs to be the same as one of these values: {{lengths}}.", "@sage/xtrem-master-data/events/control__base_sequence_number_control_type": "The component type is not authorized: {{type}}.", "@sage/xtrem-master-data/events/control__business-entity__customer_already_exists_with_same_name": "A customer already exists with the same name.", "@sage/xtrem-master-data/events/control__business-entity__site_already_exists_with_same_name": "A site already exists with the same name.", "@sage/xtrem-master-data/events/control__business-entity__supplier_already_exists_with_same_name": "A supplier already exists with the same name.", "@sage/xtrem-master-data/events/control__cost-category__the_standard_cost_category_must_be_mandatory": "Enter a standard cost category.", "@sage/xtrem-master-data/events/control__item__allergens-not-allowed": "Allergens are only allowed on food items.", "@sage/xtrem-master-data/events/control__item__cannot-be-bom-revision-managed": "A bill of material revision managed item needs to be manufactured.", "@sage/xtrem-master-data/events/control__item__cannot-be-phantom": "A phantom item must be manufactured and stock managed, but cannot be bought, or sold.", "@sage/xtrem-master-data/events/control__item__code-must-be-a-number": "The {{gtinCode}} code needs to have 13 numbers.", "@sage/xtrem-master-data/events/control__item__empty-preferredProcess": "A stock item must be manufactured, purchased, or both.", "@sage/xtrem-master-data/events/control__item__ghs-classification-not-allowed": "The GHS classification is only allowed on chemical items.", "@sage/xtrem-master-data/events/control__item__incorrect-value-for-economic-quantity": "Enter an economic order quantity that is a multiple of the batch quantity.", "@sage/xtrem-master-data/events/control__item__must-be-service": "The item must be a 'Service' item.", "@sage/xtrem-master-data/events/control__item__must-be-service-or-landed-cost": "The item needs to be a service item or a landed cost item.", "@sage/xtrem-master-data/events/control__item__must-not-be-service": "The item cannot be a 'Service' item.", "@sage/xtrem-master-data/events/control__item__must-not-be-service-or-landed-cost": "The item cannot be a service item or a landed cost item.", "@sage/xtrem-master-data/events/control__item__property-incorrect-for-not-stock-managed-items": "The {{property}} property is incorrect for items not managed in stock.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-and-landed-cost-items": "The {{property}} field is not available for service items or landed cost items.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-items": "The {{property}} property is not managed for service items.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect": "A value for the preferred process needs to be selected for a bought or manufactured item", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-manufacturing-items": "The preferred process 'Production' is incorrect for stock items that are not manufactured.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-purchasing-items": "The preferred process 'Purchasing' is incorrect for stock items that are not manufactured.", "@sage/xtrem-master-data/events/control__location_sequence_control__range_item_length": "The component sequence length needs to be the same as the component length.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_capital_letters": "An alphabetical sequence number component can only contain capital letters.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_range": "The start value cannot be after the end value.", "@sage/xtrem-master-data/events/control__location_sequence_control_number_value": "A numeric sequence number component can only contain a number.", "@sage/xtrem-master-data/events/control__location_sequence_control_numeric_range": "The start value cannot be after the end value.", "@sage/xtrem-master-data/events/control__sequence-number__force-reset-with-tenant": "This sequence number resets to zero when you reset the tenant.", "@sage/xtrem-master-data/events/control__time-control__end-date-cannot-be-empty": "You need to enter the end date.", "@sage/xtrem-master-data/events/control__time-control__end-datetime-cannot-be-empty": "You need to enter the end time.", "@sage/xtrem-master-data/events/control__time-control__invalid-date-range": "The date range from {{start}} to {{end}} is not valid.", "@sage/xtrem-master-data/events/control__time-control__invalid-datetime-range": "The date and time range from {{start}} to {{end}} is not valid.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-empty": "You need to enter the start date.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-greater-than-end-date": "The start date must be before the end date.", "@sage/xtrem-master-data/events/control__time-control__start-datetime-cannot-be-empty": "You need to enter the start time.", "@sage/xtrem-master-data/events/control__time-control__time-cannot-be-empty": "You need to enter the time.", "@sage/xtrem-master-data/events/control__time-control__time-format-HH-MM": "Time {{timeToValidate}} must be in format HH:MM.", "@sage/xtrem-master-data/found-matching-business-entities": "Matching business entities found", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description": "Documents printed: {{numberOfDocuments}}.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description_error": "One or more documents were not printed. Please view the batch task history for details.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_fail": "{{reportName}} printing unsuccessful", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_success": "{{reportName}} printing successful", "@sage/xtrem-master-data/function__customer_price_reason_priority_already_exists": "A customer price reason already exists for this priority.", "@sage/xtrem-master-data/functions__business_entity__incorrect_format_siret": "The format is incorrect. Use the SIRET number format: {{format}}.", "@sage/xtrem-master-data/functions__business_entity__not-a-valid-tax-id": "The format is incorrect. Use the tax ID format: {{format}}.", "@sage/xtrem-master-data/functions__common__download_file": "Download file", "@sage/xtrem-master-data/functions__common__history": "History", "@sage/xtrem-master-data/functions__common__invalid_characters": "The sequence number value contains invalid characters.", "@sage/xtrem-master-data/functions__common__sequence_number_id_is_in_use": "This sequence number value is in use. Use another value.", "@sage/xtrem-master-data/functions__common__sequence_number_id_must_have_two_characters": "The sequence number value must contain two characters.", "@sage/xtrem-master-data/functions__exchange-rate__no-rate-found-for-this-currency-pair": "No rate found for this currency pair.", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-enum-value": "Value incorrect: {{enumValue}}", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-length": "Incorrect length: {{sequenceNumberId}}", "@sage/xtrem-master-data/functions__sequence-number-lib__no_company_sequence_number_value_defined": "Enter the sequence number for the {{companyId}} company.", "@sage/xtrem-master-data/functions__sequence-number-lib__no_site_sequence_number_value_defined": "Enter the sequence number for the {{siteId}} site.", "@sage/xtrem-master-data/functions__sequence-number-lib__no-component-of-type": "{{sequenceNumberId}}: No component of the {{componentType}} type.", "@sage/xtrem-master-data/functions__sequence-number-lib__sequence-number-exceeded": "Sequence number {{sequenceNumberId}} exceeded.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__different-unit-type": "The {{fromUnit}} and {{toUnit}} units do not have the same unit type.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__no-factors-for-the-units": "No conversion factor has been defined between the {{fromUnit}} and {{toUnit}} units.", "@sage/xtrem-master-data/generate": "Generate", "@sage/xtrem-master-data/info": "Info", "@sage/xtrem-master-data/invalid-period": "Period incorrect: {{dates}}", "@sage/xtrem-master-data/invalid-quantity-range": "Quantity range incorrect: {{qtyRange}}", "@sage/xtrem-master-data/item__price-cannot-be-negative": "The price cannot be negative.", "@sage/xtrem-master-data/item-not-sold": "Set the {{itemName}} item to 'Sold'.", "@sage/xtrem-master-data/mailer_no_mailer_redirect_url_provided": "Mailer's redirect URL is missing in configuration file.", "@sage/xtrem-master-data/menu_item__declarations": "Declarations", "@sage/xtrem-master-data/menu_item__dev-tools": "Development tools", "@sage/xtrem-master-data/menu_item__employee": "Employee", "@sage/xtrem-master-data/menu_item__features": "Feature settings", "@sage/xtrem-master-data/menu_item__features-inventory": "Stock", "@sage/xtrem-master-data/menu_item__features-items": "Items", "@sage/xtrem-master-data/menu_item__features-manufacturing": "", "@sage/xtrem-master-data/menu_item__features-purchasing": "Purchasing", "@sage/xtrem-master-data/menu_item__features-resources": "Resources", "@sage/xtrem-master-data/menu_item__features-sales": "Sales", "@sage/xtrem-master-data/menu_item__features-stock": "Stock", "@sage/xtrem-master-data/menu_item__finance": "Finance", "@sage/xtrem-master-data/menu_item__integrations": "Integrations", "@sage/xtrem-master-data/menu_item__inventory": "Stock", "@sage/xtrem-master-data/menu_item__inventory-data": "Stock data", "@sage/xtrem-master-data/menu_item__item-data": "Item data", "@sage/xtrem-master-data/menu_item__items": "Items", "@sage/xtrem-master-data/menu_item__licence-plate-data": "License plate data", "@sage/xtrem-master-data/menu_item__location-data": "Location data", "@sage/xtrem-master-data/menu_item__manufacturing": "Manufacturing", "@sage/xtrem-master-data/menu_item__purchasing": "Purchasing", "@sage/xtrem-master-data/menu_item__resources": "Resources", "@sage/xtrem-master-data/menu_item__resources-data": "Resource data", "@sage/xtrem-master-data/menu_item__sales": "Sales", "@sage/xtrem-master-data/menu_item__stock": "Stock", "@sage/xtrem-master-data/menu_item__stock-data": "Stock data", "@sage/xtrem-master-data/multiple-existing-business-entities": "Multiple matching business entities found. Select one from the 'Business entity' field.", "@sage/xtrem-master-data/node__base_document__no_validation_email_allowed": "There is no email validation allowed {{document}}", "@sage/xtrem-master-data/node_base_resource_location_site_mismatch": "The location must have the same site as your resource.", "@sage/xtrem-master-data/node-extensions__company_extension__property__addresses": "Addresses", "@sage/xtrem-master-data/node-extensions__company_extension__property__contacts": "Contacts", "@sage/xtrem-master-data/node-extensions__company_extension__property__country": "Country", "@sage/xtrem-master-data/node-extensions__company_extension__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__company_extension__property__customerOnHoldCheck": "Customer on hold check", "@sage/xtrem-master-data/node-extensions__company_extension__property__isSequenceNumberIdUsed": "Sequence number ID used", "@sage/xtrem-master-data/node-extensions__company_extension__property__priceScale": "Price scale", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryAddress": "Primary address", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryContact": "Primary contact", "@sage/xtrem-master-data/node-extensions__company_extension__property__sequenceNumberId": "Sequence number ID", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser": "Is accessible for current user", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__nodeName": "Node name", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__options": "Options", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__propertyOrOperation": "Property or operation", "@sage/xtrem-master-data/node-extensions__country_extension__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__businessEntity": "Business entity", "@sage/xtrem-master-data/node-extensions__site_extension__property__country": "Country", "@sage/xtrem-master-data/node-extensions__site_extension__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__defaultLocation": "Default location", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialCurrency": "Financial currency", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialSite": "Financial site", "@sage/xtrem-master-data/node-extensions__site_extension__property__isFinance": "Finance", "@sage/xtrem-master-data/node-extensions__site_extension__property__isInventory": "Stock", "@sage/xtrem-master-data/node-extensions__site_extension__property__isLocationManaged": "Location managed", "@sage/xtrem-master-data/node-extensions__site_extension__property__isManufacturing": "Manufacturing", "@sage/xtrem-master-data/node-extensions__site_extension__property__isProjectManagement": "Project management", "@sage/xtrem-master-data/node-extensions__site_extension__property__isPurchase": "Purchase", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSales": "Sales", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSequenceNumberIdUsed": "Sequence number ID used", "@sage/xtrem-master-data/node-extensions__site_extension__property__itemSites": "Item-sites", "@sage/xtrem-master-data/node-extensions__site_extension__property__primaryAddress": "Primary address", "@sage/xtrem-master-data/node-extensions__site_extension__property__sequenceNumberId": "Sequence number ID", "@sage/xtrem-master-data/node-extensions__site_extension__property__siret": "SIRET", "@sage/xtrem-master-data/node-extensions__site_extension__property__stockSite": "Stock site", "@sage/xtrem-master-data/node-extensions__site_extension__property__taxIdNumber": "Tax ID number", "@sage/xtrem-master-data/node-extensions__site_extension__property__timeZone": "Time zone", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones": "Time zones", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__address__node_name": "Address", "@sage/xtrem-master-data/nodes__address__property__addressLine1": "Address line 1", "@sage/xtrem-master-data/nodes__address__property__addressLine2": "Address line 2", "@sage/xtrem-master-data/nodes__address__property__city": "City", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddress": "Concatenated address", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddressWithoutName": "Concatenated address without name", "@sage/xtrem-master-data/nodes__address__property__country": "Country", "@sage/xtrem-master-data/nodes__address__property__locationPhoneNumber": "Location phone number", "@sage/xtrem-master-data/nodes__address__property__name": "Name", "@sage/xtrem-master-data/nodes__address__property__postcode": "Postal code", "@sage/xtrem-master-data/nodes__address__property__region": "Region", "@sage/xtrem-master-data/nodes__address_base__node_name": "Address base", "@sage/xtrem-master-data/nodes__address_base__property__address": "Address", "@sage/xtrem-master-data/nodes__address_base__property__addressLine1": "Address line 1", "@sage/xtrem-master-data/nodes__address_base__property__addressLine2": "Address line 2", "@sage/xtrem-master-data/nodes__address_base__property__city": "City", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddress": "Concatenated address", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddressWithoutName": "Concatenated address without name", "@sage/xtrem-master-data/nodes__address_base__property__country": "Country", "@sage/xtrem-master-data/nodes__address_base__property__isActive": "Active", "@sage/xtrem-master-data/nodes__address_base__property__locationPhoneNumber": "Location phone number", "@sage/xtrem-master-data/nodes__address_base__property__name": "Name", "@sage/xtrem-master-data/nodes__address_base__property__postcode": "Postal code", "@sage/xtrem-master-data/nodes__address_base__property__region": "Region", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__allergen__node_name": "Allergen", "@sage/xtrem-master-data/nodes__allergen__property__id": "ID", "@sage/xtrem-master-data/nodes__allergen__property__isActive": "Active", "@sage/xtrem-master-data/nodes__allergen__property__name": "Name", "@sage/xtrem-master-data/nodes__allergen__property__pictogram": "Pictogram", "@sage/xtrem-master-data/nodes__base_business_relation__node_name": "Base business relation", "@sage/xtrem-master-data/nodes__base_business_relation__property__businessEntity": "Business entity", "@sage/xtrem-master-data/nodes__base_business_relation__property__category": "Category", "@sage/xtrem-master-data/nodes__base_business_relation__property__country": "Country", "@sage/xtrem-master-data/nodes__base_business_relation__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__property__id": "ID", "@sage/xtrem-master-data/nodes__base_business_relation__property__image": "Image", "@sage/xtrem-master-data/nodes__base_business_relation__property__internalNote": "Internal note", "@sage/xtrem-master-data/nodes__base_business_relation__property__isActive": "Active", "@sage/xtrem-master-data/nodes__base_business_relation__property__legalEntity": "Legal entity", "@sage/xtrem-master-data/nodes__base_business_relation__property__minimumOrderAmount": "Minimum order amount", "@sage/xtrem-master-data/nodes__base_business_relation__property__name": "Name", "@sage/xtrem-master-data/nodes__base_business_relation__property__paymentTerm": "Payment term", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryAddress": "Primary address", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryContact": "Primary contact", "@sage/xtrem-master-data/nodes__base_business_relation__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__base_business_relation__property__taxIdNumber": "Tax ID number", "@sage/xtrem-master-data/nodes__base_capability__node_name": "Base capability", "@sage/xtrem-master-data/nodes__base_capability__property__capabilityLevel": "Capability level", "@sage/xtrem-master-data/nodes__base_capability__property__dateEndValid": "Validity end date", "@sage/xtrem-master-data/nodes__base_capability__property__dateRangeValidity": "Date range validity", "@sage/xtrem-master-data/nodes__base_capability__property__dateStartValid": "Validity start date", "@sage/xtrem-master-data/nodes__base_capability__property__id": "ID", "@sage/xtrem-master-data/nodes__base_capability__property__name": "Name", "@sage/xtrem-master-data/nodes__base_certificate__node_name": "Base certificate", "@sage/xtrem-master-data/nodes__base_certificate__property__certificationBody": "Certification body", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfCertification": "Certification date", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfOriginalCertification": "Original certification date", "@sage/xtrem-master-data/nodes__base_certificate__property__id": "ID", "@sage/xtrem-master-data/nodes__base_certificate__property__standard": "Standard", "@sage/xtrem-master-data/nodes__base_certificate__property__validUntil": "Valid until", "@sage/xtrem-master-data/nodes__base_distribution_document__fx_rate_not_found": "No exchange rate found.", "@sage/xtrem-master-data/nodes__base_distribution_document__node_name": "Base distribution document", "@sage/xtrem-master-data/nodes__base_distribution_document__property__businessRelation": "Business relation", "@sage/xtrem-master-data/nodes__base_distribution_document__property__companyCurrency": "Company currency", "@sage/xtrem-master-data/nodes__base_distribution_document__property__companyFxRate": "Company exchange rate", "@sage/xtrem-master-data/nodes__base_distribution_document__property__fxRateDate": "Exchange rate date", "@sage/xtrem-master-data/nodes__base_distribution_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_distribution_document_line__node_name": "Base distribution document line", "@sage/xtrem-master-data/nodes__base_distribution_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo": "Update line to", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentIds": "Document IDs", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentLineIds": "Document line IDs", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync": "Bulk resync", "@sage/xtrem-master-data/nodes__base_document__header_currency_not_updatable": "The currency for this record cannot be changed.", "@sage/xtrem-master-data/nodes__base_document__id_already_exists": "The ID already exists. No sequence number will be allocated to the current  document.", "@sage/xtrem-master-data/nodes__base_document__lines_mandatory": "The document needs at least one line.", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail": "Send approval request mail", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__document": "Document", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__user": "User", "@sage/xtrem-master-data/nodes__base_document__no_financial_site": "No financial site found for the current site.", "@sage/xtrem-master-data/nodes__base_document__node_name": "Base document", "@sage/xtrem-master-data/nodes__base_document__order_date_not_updatable": "The Order date cannot be changed when the status of the record is In progress.", "@sage/xtrem-master-data/nodes__base_document__property__approvalPage": "Approval page", "@sage/xtrem-master-data/nodes__base_document__property__approvalStatus": "Approval status", "@sage/xtrem-master-data/nodes__base_document__property__approvalUrl": "Approval URL", "@sage/xtrem-master-data/nodes__base_document__property__businessEntityAddress": "Business entity address", "@sage/xtrem-master-data/nodes__base_document__property__canPrint": "Can print", "@sage/xtrem-master-data/nodes__base_document__property__canUpdateClosedDocument": "Can update closed document", "@sage/xtrem-master-data/nodes__base_document__property__companyCurrency": "Company currency", "@sage/xtrem-master-data/nodes__base_document__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__date": "Date", "@sage/xtrem-master-data/nodes__base_document__property__displayStatus": "Display status", "@sage/xtrem-master-data/nodes__base_document__property__documentDate": "Document date", "@sage/xtrem-master-data/nodes__base_document__property__documentUrl": "Document URL", "@sage/xtrem-master-data/nodes__base_document__property__externalNote": "External note", "@sage/xtrem-master-data/nodes__base_document__property__financialSite": "Financial site", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForResync": "Force update for resync", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-master-data/nodes__base_document__property__internalNote": "Internal note", "@sage/xtrem-master-data/nodes__base_document__property__isExternalNote": "Is external note", "@sage/xtrem-master-data/nodes__base_document__property__isOverwriteNote": "Is overwrite note", "@sage/xtrem-master-data/nodes__base_document__property__isPrinted": "Is printed", "@sage/xtrem-master-data/nodes__base_document__property__isSent": "Is sent", "@sage/xtrem-master-data/nodes__base_document__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-master-data/nodes__base_document__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-master-data/nodes__base_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_document__property__number": "Number", "@sage/xtrem-master-data/nodes__base_document__property__page": "Page", "@sage/xtrem-master-data/nodes__base_document__property__postingDate": "Posting date", "@sage/xtrem-master-data/nodes__base_document__property__site": "Site", "@sage/xtrem-master-data/nodes__base_document__property__siteAddress": "Site address", "@sage/xtrem-master-data/nodes__base_document__property__status": "Status", "@sage/xtrem-master-data/nodes__base_document__property__stockSite": "Stock site", "@sage/xtrem-master-data/nodes__base_document__property__text": "Text", "@sage/xtrem-master-data/nodes__base_document__property__transactionCurrency": "Transaction currency", "@sage/xtrem-master-data/nodes__base_document__update_not_allowed_status_closed": "The record for the document cannot be updated when it is Closed: {{number}}.", "@sage/xtrem-master-data/nodes__base_document_item_line__node_name": "Base document item line", "@sage/xtrem-master-data/nodes__base_document_item_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentId": "Document ID", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentNumber": "Document number", "@sage/xtrem-master-data/nodes__base_document_item_line__property__externalNote": "External note", "@sage/xtrem-master-data/nodes__base_document_item_line__property__forceUpdateForStock": "Force update for stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__internalNote": "Internal note", "@sage/xtrem-master-data/nodes__base_document_item_line__property__isExternalNote": "Is external note", "@sage/xtrem-master-data/nodes__base_document_item_line__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemDescription": "Item description", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemSite": "Item-site", "@sage/xtrem-master-data/nodes__base_document_item_line__property__origin": "Origin", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantity": "Quantity", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-master-data/nodes__base_document_item_line__property__site": "Site", "@sage/xtrem-master-data/nodes__base_document_item_line__property__siteLinkedAddress": "Site linked address", "@sage/xtrem-master-data/nodes__base_document_item_line__property__status": "Status", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSite": "Stock site", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSiteLinkedAddress": "Stock site linked address", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockUnit": "Stock unit", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unit": "Unit", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-master-data/nodes__base_document_line__node_name": "Base document line", "@sage/xtrem-master-data/nodes__base_document_line__property__documentId": "Document ID", "@sage/xtrem-master-data/nodes__base_document_line__property__documentNumber": "Document number", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__node_name": "Base document line inquiry", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__commodityCode": "Commodity code", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__company": "Company", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__date": "Date", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__fromItem": "From item", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__itemCategory": "Item category", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__site": "Site", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__toItem": "To item", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__user": "User", "@sage/xtrem-master-data/nodes__base_inbound_document__node_name": "Base inbound document", "@sage/xtrem-master-data/nodes__base_inbound_document__property__businessRelation": "Business relation", "@sage/xtrem-master-data/nodes__base_inbound_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_inbound_document_line__node_name": "Base inbound document line", "@sage/xtrem-master-data/nodes__base_inbound_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document__node_name": "Base inbound receipt document", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document_line__node_name": "Base inbound receipt document line", "@sage/xtrem-master-data/nodes__base_inbound_receipt_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_line_discount_charge__improper_calculation_rule": "Incorrect calculation rule. For percentages, use the 'By unit' calculation rule.", "@sage/xtrem-master-data/nodes__base_line_discount_charge__node_name": "Base line discount or charge", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__amount": "Amount", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basisDeterminated": "<PERSON><PERSON> determined", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationBasis": "Calculation basis", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationRule": "Calculation rule", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__sign": "Sign", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__value": "Value", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueDeterminated": "Value determined", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueType": "Value type", "@sage/xtrem-master-data/nodes__base_line_to_line__node_name": "Base line to line", "@sage/xtrem-master-data/nodes__base_line_to_line__property__amount": "Amount", "@sage/xtrem-master-data/nodes__base_line_to_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_to_line__property__from": "From", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantity": "Quantity", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantityInStockUnit": "Quantity in stock unit", "@sage/xtrem-master-data/nodes__base_line_to_line__property__stockUnit": "Stock unit", "@sage/xtrem-master-data/nodes__base_line_to_line__property__to": "To", "@sage/xtrem-master-data/nodes__base_line_to_line__property__unit": "Unit", "@sage/xtrem-master-data/nodes__base_outbound_document__node_name": "Base outbound document", "@sage/xtrem-master-data/nodes__base_outbound_document__property__businessRelation": "Business relation", "@sage/xtrem-master-data/nodes__base_outbound_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_outbound_document_line__node_name": "Base outbound document line", "@sage/xtrem-master-data/nodes__base_outbound_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_outbound_order_document__node_name": "Base outbound order document", "@sage/xtrem-master-data/nodes__base_outbound_order_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_outbound_order_document_line__node_name": "Base outbound order document line", "@sage/xtrem-master-data/nodes__base_outbound_order_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document__node_name": "Base outbound shipment document", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document__property__lines": "Lines", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document_line__node_name": "Base outbound shipment document line", "@sage/xtrem-master-data/nodes__base_outbound_shipment_document_line__property__document": "Document", "@sage/xtrem-master-data/nodes__base_resource__node_name": "Base resource", "@sage/xtrem-master-data/nodes__base_resource__property__activeFrom": "Active from", "@sage/xtrem-master-data/nodes__base_resource__property__activeRange": "Active range", "@sage/xtrem-master-data/nodes__base_resource__property__activeTo": "Active to", "@sage/xtrem-master-data/nodes__base_resource__property__description": "Description", "@sage/xtrem-master-data/nodes__base_resource__property__efficiency": "Efficiency", "@sage/xtrem-master-data/nodes__base_resource__property__id": "ID", "@sage/xtrem-master-data/nodes__base_resource__property__isActive": "Active", "@sage/xtrem-master-data/nodes__base_resource__property__location": "Location", "@sage/xtrem-master-data/nodes__base_resource__property__name": "Name", "@sage/xtrem-master-data/nodes__base_resource__property__resourceCostCategories": "Resource cost categories", "@sage/xtrem-master-data/nodes__base_resource__property__resourceImage": "Resource image", "@sage/xtrem-master-data/nodes__base_resource__property__site": "Site", "@sage/xtrem-master-data/nodes__base_resource__property__weeklyShift": "Weekly shift", "@sage/xtrem-master-data/nodes__base_sequence_number__node_name": "Base sequence number", "@sage/xtrem-master-data/nodes__base_sequence_number__property__componentLength": "Component length", "@sage/xtrem-master-data/nodes__base_sequence_number__property__components": "Components", "@sage/xtrem-master-data/nodes__base_sequence_number__property__definitionLevel": "Definition level", "@sage/xtrem-master-data/nodes__base_sequence_number__property__id": "ID", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isChronological": "Is chronological", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isClearedByReset": "Is cleared by reset", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isUsed": "Is used", "@sage/xtrem-master-data/nodes__base_sequence_number__property__legislation": "Legislation", "@sage/xtrem-master-data/nodes__base_sequence_number__property__minimumLength": "Minimum length", "@sage/xtrem-master-data/nodes__base_sequence_number__property__name": "Name", "@sage/xtrem-master-data/nodes__base_sequence_number__property__rtzLevel": "Rtz level", "@sage/xtrem-master-data/nodes__base_sequence_number__property__sequenceNumberAssignments": "Sequence number assignments", "@sage/xtrem-master-data/nodes__base_sequence_number__property__type": "Type", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames": "Get document node names", "@sage/xtrem-master-data/nodes__base_sequence_number_component__node_name": "Base sequence number component", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__constant": "Constant", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__length": "Length", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__type": "Type", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__bom_revision_sequence__node_name": "BOM revision sequence", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__components": "Components", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isDefault": "Is default", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isSequenceGenerated": "Is sequence generated", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__node_name": "BOM revision sequence component", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__business_entity__address_mandatory": "The business entity must have at least one address.", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__business_entity__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__business_entity__incorrect_format_siret": "The format is incorrect. Use the SIRET number format: {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__node_name": "Business entity", "@sage/xtrem-master-data/nodes__business_entity__not-a-valid-tax-id": "The format is incorrect. Use the tax ID format: {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__primary_address": "The business entity can have only one primary address.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_active": "You need to activate the business entity primary address.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_mandatory": "Assign at least one primary address to this business entity.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact": "The address should have one primary contact.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact_active": "You need to activate the address primary contact.", "@sage/xtrem-master-data/nodes__business_entity__property__addresses": "Addresses", "@sage/xtrem-master-data/nodes__business_entity__property__contacts": "Contacts", "@sage/xtrem-master-data/nodes__business_entity__property__country": "Country", "@sage/xtrem-master-data/nodes__business_entity__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__customer": "Customer", "@sage/xtrem-master-data/nodes__business_entity__property__id": "ID", "@sage/xtrem-master-data/nodes__business_entity__property__image": "Image", "@sage/xtrem-master-data/nodes__business_entity__property__isActive": "Active", "@sage/xtrem-master-data/nodes__business_entity__property__isCustomer": "Customer", "@sage/xtrem-master-data/nodes__business_entity__property__isSite": "Site", "@sage/xtrem-master-data/nodes__business_entity__property__isSupplier": "Supplier", "@sage/xtrem-master-data/nodes__business_entity__property__legalEntity": "Legal entity", "@sage/xtrem-master-data/nodes__business_entity__property__name": "Name", "@sage/xtrem-master-data/nodes__business_entity__property__parent": "Parent", "@sage/xtrem-master-data/nodes__business_entity__property__primaryAddress": "Primary address", "@sage/xtrem-master-data/nodes__business_entity__property__primaryContact": "Primary contact", "@sage/xtrem-master-data/nodes__business_entity__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__business_entity__property__site": "Site", "@sage/xtrem-master-data/nodes__business_entity__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__business_entity__property__taxIdNumber": "Tax ID", "@sage/xtrem-master-data/nodes__business_entity__property__website": "Website", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__business_entity_address__node_name": "Business entity address", "@sage/xtrem-master-data/nodes__business_entity_address__property__businessEntity": "Business entity", "@sage/xtrem-master-data/nodes__business_entity_address__property__concatenatedAddress": "Concatenated address", "@sage/xtrem-master-data/nodes__business_entity_address__property__contacts": "Contacts", "@sage/xtrem-master-data/nodes__business_entity_address__property__deliveryDetail": "Delivery detail", "@sage/xtrem-master-data/nodes__business_entity_address__property__isPrimary": "Primary", "@sage/xtrem-master-data/nodes__business_entity_address__property__primaryContact": "Primary contact", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__business_entity_contact__node_name": "Business entity contact", "@sage/xtrem-master-data/nodes__business_entity_contact__property__address": "Address", "@sage/xtrem-master-data/nodes__business_entity_contact__property__businessEntity": "Business entity", "@sage/xtrem-master-data/nodes__business_entity_contact__property__isPrimary": "Is primary", "@sage/xtrem-master-data/nodes__business-entity-type-control-customer": "This payment term is already assigned to customers.", "@sage/xtrem-master-data/nodes__business-entity-type-control-supplier": "This payment term is already assigned to suppliers.", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__capability_level__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__capability_level__node_name": "Capability level", "@sage/xtrem-master-data/nodes__capability_level__property__description": "Description", "@sage/xtrem-master-data/nodes__capability_level__property__id": "ID", "@sage/xtrem-master-data/nodes__capability_level__property__level": "Level", "@sage/xtrem-master-data/nodes__capability_level__property__name": "Name", "@sage/xtrem-master-data/nodes__company__address_mandatory": "Assign at least one address to the company.", "@sage/xtrem-master-data/nodes__company__primary_address": "The company can have only one primary address.", "@sage/xtrem-master-data/nodes__company__primary_address_mandatory": "Select at least one primary address for the company.", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__company_address__node_name": "Company address", "@sage/xtrem-master-data/nodes__company_address__property__company": "Company", "@sage/xtrem-master-data/nodes__company_address__property__contacts": "Contacts", "@sage/xtrem-master-data/nodes__company_address__property__isPrimary": "Primary", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__company_contact__node_name": "Company contact", "@sage/xtrem-master-data/nodes__company_contact__property__address": "Address", "@sage/xtrem-master-data/nodes__company_contact__property__company": "Company", "@sage/xtrem-master-data/nodes__company_contact__property__isPrimary": "Is primary", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__contact__node_name": "Contact", "@sage/xtrem-master-data/nodes__contact__property__email": "Email", "@sage/xtrem-master-data/nodes__contact__property__firstName": "First name", "@sage/xtrem-master-data/nodes__contact__property__image": "Image", "@sage/xtrem-master-data/nodes__contact__property__lastName": "Last name", "@sage/xtrem-master-data/nodes__contact__property__locationPhoneNumber": "Location phone number", "@sage/xtrem-master-data/nodes__contact__property__position": "Position", "@sage/xtrem-master-data/nodes__contact__property__preferredName": "Preferred name", "@sage/xtrem-master-data/nodes__contact__property__role": "Role", "@sage/xtrem-master-data/nodes__contact__property__title": "Title", "@sage/xtrem-master-data/nodes__contact_base__node_name": "Contact base", "@sage/xtrem-master-data/nodes__contact_base__not-a-valid-email": "Email incorrect: {{email}}", "@sage/xtrem-master-data/nodes__contact_base__property__contact": "Contact", "@sage/xtrem-master-data/nodes__contact_base__property__displayName": "Display name", "@sage/xtrem-master-data/nodes__contact_base__property__email": "Email", "@sage/xtrem-master-data/nodes__contact_base__property__firstName": "First name", "@sage/xtrem-master-data/nodes__contact_base__property__image": "Image", "@sage/xtrem-master-data/nodes__contact_base__property__isActive": "Active", "@sage/xtrem-master-data/nodes__contact_base__property__lastName": "Last name", "@sage/xtrem-master-data/nodes__contact_base__property__locationPhoneNumber": "Location phone number", "@sage/xtrem-master-data/nodes__contact_base__property__position": "Position", "@sage/xtrem-master-data/nodes__contact_base__property__preferredName": "Preferred name", "@sage/xtrem-master-data/nodes__contact_base__property__role": "Role", "@sage/xtrem-master-data/nodes__contact_base__property__title": "Title", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__container__node_name": "Container", "@sage/xtrem-master-data/nodes__container__property__consumedLocationCapacity": "Consumed location capacity", "@sage/xtrem-master-data/nodes__container__property__id": "ID", "@sage/xtrem-master-data/nodes__container__property__isActive": "Active", "@sage/xtrem-master-data/nodes__container__property__isInternal": "Internal", "@sage/xtrem-master-data/nodes__container__property__isSingleItem": "Single item", "@sage/xtrem-master-data/nodes__container__property__isSingleLot": "Single lot", "@sage/xtrem-master-data/nodes__container__property__labelFormat": "Label format", "@sage/xtrem-master-data/nodes__container__property__name": "Name", "@sage/xtrem-master-data/nodes__container__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__container__property__storageCapacity": "Storage capacity", "@sage/xtrem-master-data/nodes__container__property__type": "Type", "@sage/xtrem-master-data/nodes__container__sequence-number-not-required": "No sequence number is required for the external container.", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__cost_category__node_name": "Cost category", "@sage/xtrem-master-data/nodes__cost_category__property__costCategoryType": "Cost category type", "@sage/xtrem-master-data/nodes__cost_category__property__id": "ID", "@sage/xtrem-master-data/nodes__cost_category__property__isMandatory": "Mandatory", "@sage/xtrem-master-data/nodes__cost_category__property__name": "Name", "@sage/xtrem-master-data/nodes__cost-category__can-have-only-one-cost-type-of": "You can only have one {{costCategoryType}} cost type.", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__currency__deleting-record": "Deleting the {{exchangeRateId}} record (length of {{exchangeRateLength}}) from the exchange rates.", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate": "Save exchange rate", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__base": "Base", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__dateRate": "Date rate", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__destination": "Destination", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__rate": "Rate", "@sage/xtrem-master-data/nodes__currency__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__property__currentExchangeRates": "Current exchange rates", "@sage/xtrem-master-data/nodes__currency__property__decimalDigits": "Decimal digits", "@sage/xtrem-master-data/nodes__currency__property__exchangeRates": "Exchange rates", "@sage/xtrem-master-data/nodes__currency__property__exchangeRatesDestinationInverse": "Exchange rates destination inverse", "@sage/xtrem-master-data/nodes__currency__property__icon": "Icon", "@sage/xtrem-master-data/nodes__currency__property__id": "ID", "@sage/xtrem-master-data/nodes__currency__property__isActive": "Active", "@sage/xtrem-master-data/nodes__currency__property__lastUpdate": "Last update", "@sage/xtrem-master-data/nodes__currency__property__name": "Name", "@sage/xtrem-master-data/nodes__currency__property__rounding": "Rounding", "@sage/xtrem-master-data/nodes__currency__property__symbol": "Symbol", "@sage/xtrem-master-data/nodes__currency_id": "The ID must contain 3 characters.", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__customer__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__customer__node_name": "Customer", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address": "The customer can have only one primary ship-to address.", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address_mandatory": "Assign one active primary ship-to address to the customer.", "@sage/xtrem-master-data/nodes__customer__primary_ship_to_address_mandatory": "The customer needs to have at least one active primary ship-to address.", "@sage/xtrem-master-data/nodes__customer__property__billToAddress": "Bill-to address", "@sage/xtrem-master-data/nodes__customer__property__billToCustomer": "Bill-to customer", "@sage/xtrem-master-data/nodes__customer__property__category": "Category", "@sage/xtrem-master-data/nodes__customer__property__creditLimit": "Credit limit", "@sage/xtrem-master-data/nodes__customer__property__deliveryAddresses": "Ship-to addresses", "@sage/xtrem-master-data/nodes__customer__property__displayStatus": "Display status", "@sage/xtrem-master-data/nodes__customer__property__isOnHold": "On hold", "@sage/xtrem-master-data/nodes__customer__property__itemPrices": "Item prices", "@sage/xtrem-master-data/nodes__customer__property__items": "Items", "@sage/xtrem-master-data/nodes__customer__property__payByAddress": "Pay-by address", "@sage/xtrem-master-data/nodes__customer__property__payByCustomer": "Pay-by customer", "@sage/xtrem-master-data/nodes__customer__property__paymentTerm": "Payment term", "@sage/xtrem-master-data/nodes__customer__property__primaryShipToAddress": "Primary ship-to address", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__customer_price_reason__node_name": "Customer price reason", "@sage/xtrem-master-data/nodes__customer_price_reason__property__description": "Description", "@sage/xtrem-master-data/nodes__customer_price_reason__property__id": "ID", "@sage/xtrem-master-data/nodes__customer_price_reason__property__isActive": "Active", "@sage/xtrem-master-data/nodes__customer_price_reason__property__name": "Name", "@sage/xtrem-master-data/nodes__customer_price_reason__property__priority": "Priority", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__customer_supplier_category__change_not_possible_category_is_used_on_customer": "You can only change a customer category type if it is not assigned to a customer.", "@sage/xtrem-master-data/nodes__customer_supplier_category__node_name": "Supplier and customer category", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__id": "ID", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isCustomer": "Customer", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSequenceNumberManagement": "Is sequence number management", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSupplier": "Supplier", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__name": "Name", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__customer-minimum__order_amount-cannot-be-negative": "The minimum order amount cannot be negative.", "@sage/xtrem-master-data/nodes__customer-supplier-category__customer_or_supplier": "You need to select Customer or Supplier.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-cannot-be-set": "The sequence number cannot be set.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-is-mandatory": "The sequence number is mandatory.", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__daily_shift__no_details_on_full_day_shift": "A full-day shift cannot have shift details.", "@sage/xtrem-master-data/nodes__daily_shift__no_overlap_on_shift_details": "The shift details cannot overlap.", "@sage/xtrem-master-data/nodes__daily_shift__node_name": "Daily shift", "@sage/xtrem-master-data/nodes__daily_shift__property__capacity": "Capacity", "@sage/xtrem-master-data/nodes__daily_shift__property__formattedCapacity": "Formatted capacity", "@sage/xtrem-master-data/nodes__daily_shift__property__id": "ID", "@sage/xtrem-master-data/nodes__daily_shift__property__isFullDay": "Full day", "@sage/xtrem-master-data/nodes__daily_shift__property__name": "Name", "@sage/xtrem-master-data/nodes__daily_shift__property__shiftDetails": "Shift details", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__daily_shift_detail__node_name": "Daily shift detail", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__dailyShift": "Daily shift", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__shiftDetail": "Shift detail", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__delivery_detail__node_name": "Delivery detail", "@sage/xtrem-master-data/nodes__delivery_detail__property__address": "Address", "@sage/xtrem-master-data/nodes__delivery_detail__property__incoterm": "Incoterms rule", "@sage/xtrem-master-data/nodes__delivery_detail__property__isActive": "Is active", "@sage/xtrem-master-data/nodes__delivery_detail__property__isFridayWorkDay": "Is friday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__isMondayWorkDay": "Is monday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__isPrimary": "Is primary", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSaturdayWorkDay": "Is saturday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSundayWorkDay": "Is sunday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__isThursdayWorkDay": "Is thursday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__isTuesdayWorkDay": "Is tuesday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__isWednesdayWorkDay": "Is wednesday work day", "@sage/xtrem-master-data/nodes__delivery_detail__property__leadTime": "Lead time", "@sage/xtrem-master-data/nodes__delivery_detail__property__mode": "Mode", "@sage/xtrem-master-data/nodes__delivery_detail__property__shipmentSite": "Shipment site", "@sage/xtrem-master-data/nodes__delivery_detail__property__workDaysSelection": "Work days selection", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__delivery_mode__node_name": "Delivery mode", "@sage/xtrem-master-data/nodes__delivery_mode__property__description": "Description", "@sage/xtrem-master-data/nodes__delivery_mode__property__id": "ID", "@sage/xtrem-master-data/nodes__delivery_mode__property__isActive": "Active", "@sage/xtrem-master-data/nodes__delivery_mode__property__name": "Name", "@sage/xtrem-master-data/nodes__detailed_resource__node_name": "Detailed resource", "@sage/xtrem-master-data/nodes__detailed_resource__property__efficiency": "Efficiency", "@sage/xtrem-master-data/nodes__detailed_resource__property__location": "Location", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceCostCategories": "Resource cost categories", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceGroup": "Resource group", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__dev_tools__node_name": "Development tools", "@sage/xtrem-master-data/nodes__dev_tools__property__id": "ID", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__employee__node_name": "Employee", "@sage/xtrem-master-data/nodes__employee__property__firstName": "First name", "@sage/xtrem-master-data/nodes__employee__property__id": "ID", "@sage/xtrem-master-data/nodes__employee__property__image": "Image", "@sage/xtrem-master-data/nodes__employee__property__isActive": "Is active", "@sage/xtrem-master-data/nodes__employee__property__lastName": "Last name", "@sage/xtrem-master-data/nodes__employee__property__name": "Name", "@sage/xtrem-master-data/nodes__employee__property__resource": "Resource", "@sage/xtrem-master-data/nodes__employee__property__site": "Site", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__exchange_rate__node_name": "Exchange rate", "@sage/xtrem-master-data/nodes__exchange_rate__property__base": "Base", "@sage/xtrem-master-data/nodes__exchange_rate__property__dateRate": "Date rate", "@sage/xtrem-master-data/nodes__exchange_rate__property__destination": "Destination", "@sage/xtrem-master-data/nodes__exchange_rate__property__divisor": "Divisor", "@sage/xtrem-master-data/nodes__exchange_rate__property__rate": "Rate", "@sage/xtrem-master-data/nodes__exchange_rate__property__shortDescription": "Short description", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate": "Convert rate", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__amount": "Amount", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__base": "Base", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__destination": "Destination", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__rateDate": "Rate date", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_naf": "Use the NAF number format: {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_rcs": "Use the RCS number format: {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_siren": "Use the SIREN number format: {{format}}", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__ghs_classification__node_name": "GHS classification", "@sage/xtrem-master-data/nodes__ghs_classification__property__hazard": "Hazard", "@sage/xtrem-master-data/nodes__ghs_classification__property__id": "ID", "@sage/xtrem-master-data/nodes__ghs_classification__property__name": "Name", "@sage/xtrem-master-data/nodes__ghs_classification__property__pictogram": "Pictogram", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__group_resource__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__group_resource__node_name": "Resource group", "@sage/xtrem-master-data/nodes__group_resource__property__efficiency": "Efficiency", "@sage/xtrem-master-data/nodes__group_resource__property__minCapabilityLevel": "Minimum capability level", "@sage/xtrem-master-data/nodes__group_resource__property__replacements": "Replacements", "@sage/xtrem-master-data/nodes__group_resource__property__resources": "Resources", "@sage/xtrem-master-data/nodes__group_resource__property__type": "Type", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__incoterm__node_name": "Incoterms rule", "@sage/xtrem-master-data/nodes__incoterm__property__description": "Description", "@sage/xtrem-master-data/nodes__incoterm__property__id": "ID", "@sage/xtrem-master-data/nodes__incoterm__property__isActive": "Active", "@sage/xtrem-master-data/nodes__incoterm__property__name": "Name", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_origin__node_name": "Indirect cost origin", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__name": "Name", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_section__node_name": "Indirect cost section", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__calculationMethod": "Calculation method", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__lines": "Lines", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__name": "Name", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__node_name": "Indirect cost section line", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostOrigin": "Indirect cost origin", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostSection": "Indirect cost section", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__percentage": "Percentage", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item__bom_revision_sequence_number_not_managed": "You can only add a revision number to a BOM if the item is BOM revision managed.", "@sage/xtrem-master-data/nodes__item__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__item__cannot_change_purchased_property_supplier_exists": "You are not allowed to change the purchased property, some records exist in the Suppliers page.", "@sage/xtrem-master-data/nodes__item__cannot_change_sold_property_customers_exists": "You are not allowed to change the sold property, some records exist in the Customers page.", "@sage/xtrem-master-data/nodes__item__commodity_code_format": "Use the commodity code format: {{format}}", "@sage/xtrem-master-data/nodes__item__density-cannot-be-negative": "The density cannot be negative.", "@sage/xtrem-master-data/nodes__item__expiration_mangement_cannot_be_enabled_without_lot_management": "The expiration management cannot be enabled with items that are not managed by lot.", "@sage/xtrem-master-data/nodes__item__mandatory_property": "Enter a value for serial number managed items.", "@sage/xtrem-master-data/nodes__item__min-price-cannot-be-set-if-no-currency-is-defined": "You need to select a currency before you can enter a minimum price.", "@sage/xtrem-master-data/nodes__item__node_name": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__not-a-volume-measure": "The {{unitOfMeasure}} unit does not relate to volume.", "@sage/xtrem-master-data/nodes__item__not-a-weight-measure": "The {{unitOfMeasure}} unit does not relate to weight.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-negative": "The price cannot be negative.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-set-if-no-currency-is-defined": "You need to select a currency before you can enter a base price.", "@sage/xtrem-master-data/nodes__item__property__allergens": "Allergens", "@sage/xtrem-master-data/nodes__item__property__basePrice": "Base price", "@sage/xtrem-master-data/nodes__item__property__bomRevisionSequenceNumber": "BOM revision sequence number", "@sage/xtrem-master-data/nodes__item__property__capacity": "Capacity", "@sage/xtrem-master-data/nodes__item__property__category": "Category", "@sage/xtrem-master-data/nodes__item__property__classifications": "Classifications", "@sage/xtrem-master-data/nodes__item__property__commodityCode": "Commodity code", "@sage/xtrem-master-data/nodes__item__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__customerPrices": "Customer prices", "@sage/xtrem-master-data/nodes__item__property__customers": "Customers", "@sage/xtrem-master-data/nodes__item__property__density": "Density", "@sage/xtrem-master-data/nodes__item__property__description": "Description", "@sage/xtrem-master-data/nodes__item__property__eanNumber": "EAN", "@sage/xtrem-master-data/nodes__item__property__id": "ID", "@sage/xtrem-master-data/nodes__item__property__image": "Image", "@sage/xtrem-master-data/nodes__item__property__isActive": "Active", "@sage/xtrem-master-data/nodes__item__property__isBomRevisionManaged": "Is BOM revision managed", "@sage/xtrem-master-data/nodes__item__property__isBought": "Purchased", "@sage/xtrem-master-data/nodes__item__property__isExpiryManaged": "Expiration managed", "@sage/xtrem-master-data/nodes__item__property__isManufactured": "Manufactured", "@sage/xtrem-master-data/nodes__item__property__isPhantom": "Is phantom", "@sage/xtrem-master-data/nodes__item__property__isPotencyManagement": "Potency management", "@sage/xtrem-master-data/nodes__item__property__isSold": "Sold", "@sage/xtrem-master-data/nodes__item__property__isStockManaged": "Stock managed", "@sage/xtrem-master-data/nodes__item__property__isTraceabilityManagement": "Traceability management", "@sage/xtrem-master-data/nodes__item__property__itemSites": "Item-sites", "@sage/xtrem-master-data/nodes__item__property__lotManagement": "Lot management", "@sage/xtrem-master-data/nodes__item__property__lotSequenceNumber": "Lot sequence number", "@sage/xtrem-master-data/nodes__item__property__maximumSalesQuantity": "Maximum sales quantity", "@sage/xtrem-master-data/nodes__item__property__minimumPrice": "Minimum price", "@sage/xtrem-master-data/nodes__item__property__minimumSalesQuantity": "Minimum sales quantity", "@sage/xtrem-master-data/nodes__item__property__name": "Name", "@sage/xtrem-master-data/nodes__item__property__prices": "Prices", "@sage/xtrem-master-data/nodes__item__property__purchaseUnit": "Purchase unit", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversion": "Purchase unit to stock unit conversion", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversionDedicated": "Purchase unit to stock unit conversion dedicated", "@sage/xtrem-master-data/nodes__item__property__salesUnit": "Sales unit", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversion": "Sales unit to stock unit conversion", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversionDedicated": "Sales unit to stock unit conversion dedicated", "@sage/xtrem-master-data/nodes__item__property__serialNumberManagement": "Serial number management", "@sage/xtrem-master-data/nodes__item__property__serialNumberSequenceNumber": "Serial number sequence number", "@sage/xtrem-master-data/nodes__item__property__serialNumberUsage": "Serial number usage", "@sage/xtrem-master-data/nodes__item__property__status": "Status", "@sage/xtrem-master-data/nodes__item__property__stockUnit": "Stock unit", "@sage/xtrem-master-data/nodes__item__property__supplierPrices": "Supplier prices", "@sage/xtrem-master-data/nodes__item__property__suppliers": "Suppliers", "@sage/xtrem-master-data/nodes__item__property__type": "Type", "@sage/xtrem-master-data/nodes__item__property__useSupplierSerialNumbers": "Use supplier serial numbers", "@sage/xtrem-master-data/nodes__item__property__volume": "Volume", "@sage/xtrem-master-data/nodes__item__property__volumeUnit": "Volume unit", "@sage/xtrem-master-data/nodes__item__property__weight": "Weight", "@sage/xtrem-master-data/nodes__item__property__weightUnit": "Weight unit", "@sage/xtrem-master-data/nodes__item__purchase_unit_not_0_decimal_places": "The purchase unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.", "@sage/xtrem-master-data/nodes__item__sales_unit_not_0_decimal_places": "The sales unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.", "@sage/xtrem-master-data/nodes__item__standard_unit_of_measure_not_changeable": "The conversion from {{from}} to {{to}} is standard. You cannot change it.", "@sage/xtrem-master-data/nodes__item__stock_unit_not_0_decimal_places": "The stock unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.", "@sage/xtrem-master-data/nodes__item__the_lot_and_serial_number_cannot_use_the_same_sequence_number": "The lot and serial number cannot use the same sequence number.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_lot": "This property cannot be used with items not managed by lot.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_serial_number": "For items not managed by serial number a serial number sequence cannot be used.", "@sage/xtrem-master-data/nodes__item__volume-cannot-be-negative": "The volume cannot be negative.", "@sage/xtrem-master-data/nodes__item__weight-cannot-be-negative": "The weight cannot be negative.", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_allergen__node_name": "Item allergen", "@sage/xtrem-master-data/nodes__item_allergen__property__allergen": "Allergen", "@sage/xtrem-master-data/nodes__item_allergen__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used": "You cannot change the type for a item category that is being used.", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used_on_supplier": "You can only change a supplier category type if it is not assigned to a supplier.", "@sage/xtrem-master-data/nodes__item_category__node_name": "Item category", "@sage/xtrem-master-data/nodes__item_category__property__id": "ID", "@sage/xtrem-master-data/nodes__item_category__property__isAllergen": "Allergen", "@sage/xtrem-master-data/nodes__item_category__property__isGhsClassification": "GHS Classification", "@sage/xtrem-master-data/nodes__item_category__property__isSequenceNumberManagement": "Is sequence number management", "@sage/xtrem-master-data/nodes__item_category__property__name": "Name", "@sage/xtrem-master-data/nodes__item_category__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__item_category__property__type": "Type", "@sage/xtrem-master-data/nodes__item_classification__node_name": "Item classification", "@sage/xtrem-master-data/nodes__item_classification__property__classification": "Classification", "@sage/xtrem-master-data/nodes__item_classification__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_classifications__node_name": "Item classifications", "@sage/xtrem-master-data/nodes__item_classifications__property__classification": "Classification", "@sage/xtrem-master-data/nodes__item_classifications__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_customer__node_name": "Item-customer", "@sage/xtrem-master-data/nodes__item_customer__property__customer": "Customer", "@sage/xtrem-master-data/nodes__item_customer__property__id": "ID", "@sage/xtrem-master-data/nodes__item_customer__property__isActive": "Active", "@sage/xtrem-master-data/nodes__item_customer__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer__property__maximumSalesQuantity": "Maximum sales quantity", "@sage/xtrem-master-data/nodes__item_customer__property__minimumSalesQuantity": "Minimum sales quantity", "@sage/xtrem-master-data/nodes__item_customer__property__name": "Name", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnit": "Sales unit", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnitToStockUnitConversion": "Sales unit to stock unit conversion", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_customer_price__node_name": "Item-customer price", "@sage/xtrem-master-data/nodes__item_customer_price__property__charge": "Charge", "@sage/xtrem-master-data/nodes__item_customer_price__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__customer": "Customer", "@sage/xtrem-master-data/nodes__item_customer_price__property__discount": "Discount", "@sage/xtrem-master-data/nodes__item_customer_price__property__endDate": "End date", "@sage/xtrem-master-data/nodes__item_customer_price__property__fromQuantity": "From quantity", "@sage/xtrem-master-data/nodes__item_customer_price__property__isActive": "Active", "@sage/xtrem-master-data/nodes__item_customer_price__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__price": "Price", "@sage/xtrem-master-data/nodes__item_customer_price__property__priceReason": "Price reason", "@sage/xtrem-master-data/nodes__item_customer_price__property__salesSite": "Sales site", "@sage/xtrem-master-data/nodes__item_customer_price__property__startDate": "Start date", "@sage/xtrem-master-data/nodes__item_customer_price__property__stockSite": "Stock site", "@sage/xtrem-master-data/nodes__item_customer_price__property__toQuantity": "To quantity", "@sage/xtrem-master-data/nodes__item_customer_price__property__unit": "Unit", "@sage/xtrem-master-data/nodes__item_customer_price__property__validUnits": "Valid units", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice": "Get sales price", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__parameter__priceParameters": "Price parameters", "@sage/xtrem-master-data/nodes__item_not_active": "You need to remove the inactive items before you change the document status.", "@sage/xtrem-master-data/nodes__item_price__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_price__node_name": "Item price", "@sage/xtrem-master-data/nodes__item_price__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__dateValid": "Validity date", "@sage/xtrem-master-data/nodes__item_price__property__dateValidFrom": "Validity date from", "@sage/xtrem-master-data/nodes__item_price__property__dateValidTo": "Validity date to", "@sage/xtrem-master-data/nodes__item_price__property__fromQuantity": "From quantity", "@sage/xtrem-master-data/nodes__item_price__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__price": "Price", "@sage/xtrem-master-data/nodes__item_price__property__priority": "Priority", "@sage/xtrem-master-data/nodes__item_price__property__site": "Site", "@sage/xtrem-master-data/nodes__item_price__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__item_price__property__toQuantity": "To quantity", "@sage/xtrem-master-data/nodes__item_price__property__type": "Type", "@sage/xtrem-master-data/nodes__item_price__property__unit": "Unit", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice": "Get purchase price", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice__parameter__priceParameters": "Price parameters", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_site__deletion_forbidden": "A work in progress record with this item-site exists. You cannot delete it.", "@sage/xtrem-master-data/nodes__item_site__node_name": "Item-site", "@sage/xtrem-master-data/nodes__item_site__property__batchQuantity": "Batch quantity", "@sage/xtrem-master-data/nodes__item_site__property__completedProductDefaultLocation": "Completed product default location", "@sage/xtrem-master-data/nodes__item_site__property__costs": "Costs", "@sage/xtrem-master-data/nodes__item_site__property__defaultSupplier": "Default supplier", "@sage/xtrem-master-data/nodes__item_site__property__economicOrderQuantity": "Economic order quantity", "@sage/xtrem-master-data/nodes__item_site__property__expectedQuantity": "Expected quantity", "@sage/xtrem-master-data/nodes__item_site__property__id": "ID", "@sage/xtrem-master-data/nodes__item_site__property__inboundDefaultLocation": "Inbound default location", "@sage/xtrem-master-data/nodes__item_site__property__indirectCostSection": "Indirect cost section", "@sage/xtrem-master-data/nodes__item_site__property__isOrderToOrder": "Order-to-order", "@sage/xtrem-master-data/nodes__item_site__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__itemSiteCost": "Item-site cost", "@sage/xtrem-master-data/nodes__item_site__property__outboundDefaultLocation": "Outbound default location", "@sage/xtrem-master-data/nodes__item_site__property__preferredProcess": "Preferred process", "@sage/xtrem-master-data/nodes__item_site__property__prodLeadTime": "Production lead time", "@sage/xtrem-master-data/nodes__item_site__property__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/nodes__item_site__property__reorderPoint": "Reorder point", "@sage/xtrem-master-data/nodes__item_site__property__replenishmentMethod": "Replenishment method", "@sage/xtrem-master-data/nodes__item_site__property__requiredQuantity": "Required quantity", "@sage/xtrem-master-data/nodes__item_site__property__safetyStock": "Safety stock", "@sage/xtrem-master-data/nodes__item_site__property__site": "Site", "@sage/xtrem-master-data/nodes__item_site__property__stdCostValue": "Standard cost value", "@sage/xtrem-master-data/nodes__item_site__property__stockUnit": "Stock unit", "@sage/xtrem-master-data/nodes__item_site__property__suppliers": "Suppliers", "@sage/xtrem-master-data/nodes__item_site__property__valuationMethod": "Valuation method", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite": "Get valued item site", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__parameter__searchCriteria": "Search criteria", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_site_cost__node_name": "Item-site cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__costCategory": "Cost category", "@sage/xtrem-master-data/nodes__item_site_cost__property__forQuantity": "For quantity", "@sage/xtrem-master-data/nodes__item_site_cost__property__fromDate": "From date", "@sage/xtrem-master-data/nodes__item_site_cost__property__indirectCost": "Indirect cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__isCalculated": "Calculated", "@sage/xtrem-master-data/nodes__item_site_cost__property__isUpdatingPreviousCost": "Is updating previous cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__itemSite": "Item-site", "@sage/xtrem-master-data/nodes__item_site_cost__property__laborCost": "Labor cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__machineCost": "Machine cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__materialCost": "Material cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__stockUnit": "Stock unit", "@sage/xtrem-master-data/nodes__item_site_cost__property__toDate": "To date", "@sage/xtrem-master-data/nodes__item_site_cost__property__toolCost": "Tool cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__totalCost": "Total cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__unitCost": "Unit cost", "@sage/xtrem-master-data/nodes__item_site_cost__property__version": "Version", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost": "Get item site cost", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__effectiveDate": "Effective date", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__site": "Site", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_site_supplier__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__item_site_supplier__node_name": "Item-site-supplier", "@sage/xtrem-master-data/nodes__item_site_supplier__property__isDefaultItemSupplier": "Default item supplier", "@sage/xtrem-master-data/nodes__item_site_supplier__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSite": "Item-site", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSupplier": "Item-supplier", "@sage/xtrem-master-data/nodes__item_site_supplier__property__minimumPurchaseOrderQuantity": "Minimum purchase order quantity", "@sage/xtrem-master-data/nodes__item_site_supplier__property__priority": "Priority", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseUnit": "Purchase unit", "@sage/xtrem-master-data/nodes__item_site_supplier__property__site": "Site", "@sage/xtrem-master-data/nodes__item_site_supplier__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__item_site_supplier__property__uStoredPriority": "Stored priority", "@sage/xtrem-master-data/nodes__item_site_supplier__suppliers-dont-match": "The supplier must be the same as the one referenced on the item-supplier.", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_supplier__node_name": "Item-supplier", "@sage/xtrem-master-data/nodes__item_supplier__property__id": "ID", "@sage/xtrem-master-data/nodes__item_supplier__property__isActive": "Active", "@sage/xtrem-master-data/nodes__item_supplier__property__isDefaultItemSupplier": "Default item supplier", "@sage/xtrem-master-data/nodes__item_supplier__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__minimumPurchaseQuantity": "Minimum purchase quantity", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseUnitOfMeasure": "Purchase unit of measure", "@sage/xtrem-master-data/nodes__item_supplier__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemCode": "Supplier item code", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemName": "Supplier item name", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierPriority": "Supplier priority", "@sage/xtrem-master-data/nodes__item_supplier__purchase_unit_forbidden": "Define a conversion factor from the {{stockUnit}} stock unit to the {{purchaseUnit}} purchase unit for the {{item}} item.", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__item_supplier_price__node_name": "Item supplier price", "@sage/xtrem-master-data/nodes__item_supplier_price__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValid": "Date valid", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidFrom": "Date valid from", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidTo": "Date valid to", "@sage/xtrem-master-data/nodes__item_supplier_price__property__fromQuantity": "From quantity", "@sage/xtrem-master-data/nodes__item_supplier_price__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__itemSupplier": "Item supplier", "@sage/xtrem-master-data/nodes__item_supplier_price__property__price": "Price", "@sage/xtrem-master-data/nodes__item_supplier_price__property__priority": "Priority", "@sage/xtrem-master-data/nodes__item_supplier_price__property__site": "Site", "@sage/xtrem-master-data/nodes__item_supplier_price__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__item_supplier_price__property__toQuantity": "To quantity", "@sage/xtrem-master-data/nodes__item_supplier_price__property__type": "Type", "@sage/xtrem-master-data/nodes__item_supplier_price__property__unit": "Unit", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice": "Get purchase price", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__parameter__priceParameters": "Price parameters", "@sage/xtrem-master-data/nodes__item-category__allergen_or_ghs_classification": "The item category can be Allergen or GHS classification. You can only select one or none.", "@sage/xtrem-master-data/nodes__item-category__sequence-number-cannot-be-set": "The sequence number cannot be set.", "@sage/xtrem-master-data/nodes__item-category__sequence-number-is-mandatory": "The sequence number is mandatory.", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_infinite_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_specific_to_infinite": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate-greater-than-toDate": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity_improper_range": "The quantity range cannot overlap with another quantity range.", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity-greater-than-toQuantity": "Enter a 'From quantity' lower than the 'To quantity'.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_infinite_range": "The new date range cannot cover an infinite period if a row with specific time range exists.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_specific_to_infinite": "The new date range cannot cover a specific time range if a row with an infinite period exists.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_improper_range": "The date range cannot overlap with another date range.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate-greater-than-endDate": "Enter a start date earlier than the end date.", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-range": "Quantity range incorrect: {{qtyRange}}", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-unit-of-measure": "The {{uom}} unit does not relate to quantity.", "@sage/xtrem-master-data/nodes__item-price__price-cannot-be-negative": "The price cannot be negative.", "@sage/xtrem-master-data/nodes__item-price__price-overlap": "Price overlap: {{err<PERSON><PERSON>}}", "@sage/xtrem-master-data/nodes__item-price__priority-cannot-be-negative": "The priority cannot be negative.", "@sage/xtrem-master-data/nodes__item-price__quantity-cannot-be-negative": "The quantity cannot be negative.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory": "Select a stock site.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-purchase-site": "Select a stock or purchasing site.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-or-purchase-site": "Select a stock, sales or purchasing site.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-site": "Select a stock or sales site.", "@sage/xtrem-master-data/nodes__item-site__valuation-method-must-be-standard-cost": "Items not managed in stock need to use the standard cost valuation method.", "@sage/xtrem-master-data/nodes__item-site-cost__calculated-cost-only-for-manufactured-item": "A calculated cost is only compatible with a manufactured item.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_deletion_impossible_if_before_today": "Delete not allowed. The item-site cost start date is earlier than the current date.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_update_impossible": "The item-site cost start date is earlier than or equal to the current date.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-from-date-already-set": "A cost with the same start date already exists.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-updating-previous-cost": "Failed to update previous cost: {{errors}}", "@sage/xtrem-master-data/nodes__item-site-cost__incorrect-key-changes": "The item-site and cost category of an item-site cost cannot be updated.", "@sage/xtrem-master-data/nodes__item-site-cost__labor-cost-only-for-manufactured-item": "A labor cost is only compatible with a manufactured item.", "@sage/xtrem-master-data/nodes__item-site-cost__material-cost-only-for-manufactured-item": "A machine cost is only compatible with a manufactured item.", "@sage/xtrem-master-data/nodes__item-site-cost__tool-cost-only-for-manufactured-item": "A tool cost is only compatible with a manufactured item.", "@sage/xtrem-master-data/nodes__item-supplier-price__fromQuantity_improper_range": "The quantity range cannot overlap with another quantity range.", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__labor_capability__node_name": "Labor capability", "@sage/xtrem-master-data/nodes__labor_capability__property__labor": "Labor", "@sage/xtrem-master-data/nodes__labor_capability__property__machine": "Machine", "@sage/xtrem-master-data/nodes__labor_capability__property__service": "Service", "@sage/xtrem-master-data/nodes__labor_capability__property__tool": "Tool", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__labor_resource__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__labor_resource__node_name": "Labor resource", "@sage/xtrem-master-data/nodes__labor_resource__property__capabilities": "Capabilities", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers": "Create bulk license plate numbers", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__containerId": "Container ID", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleItem": "Single item", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleLot": "Single lot", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__locationId": "Location ID", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__numberToCreate": "Number to create", "@sage/xtrem-master-data/nodes__license_plate_number__node_name": "License plate number", "@sage/xtrem-master-data/nodes__license_plate_number__property__consumedCapacity": "Consumed capacity", "@sage/xtrem-master-data/nodes__license_plate_number__property__container": "Container", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleItem": "Single item", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleLot": "Single lot", "@sage/xtrem-master-data/nodes__license_plate_number__property__location": "Location", "@sage/xtrem-master-data/nodes__license_plate_number__property__number": "Number", "@sage/xtrem-master-data/nodes__license_plate_number__property__owner": "Owner", "@sage/xtrem-master-data/nodes__license-plate-number__location-required-for-automatic-number-generation": "The license plate number cannot be generated automatically for the container {{containerId}} without a location.", "@sage/xtrem-master-data/nodes__license-plate-number__no-default-sequence": "The license plate number cannot be generated. Enter a sequence number for the {{containerId}} container.", "@sage/xtrem-master-data/nodes__license-plate-number__owner-not-required": "No owner is required for the license plate number.", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location__location_category_virtual_not_allowed": "The virtual location category is not valid. You need to select a different location category.", "@sage/xtrem-master-data/nodes__location__location_zone_virtual_not_allowed": "The virtual location zone is not valid. You need to select a different location zone.", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations": "Create bulk locations", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locations": "Locations", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locationSequence": "Location sequence", "@sage/xtrem-master-data/nodes__location__mutation__getLocations": "Get locations", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__locationSequence": "Location sequence", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__requiredCombinations": "Required combinations", "@sage/xtrem-master-data/nodes__location__node_name": "Location", "@sage/xtrem-master-data/nodes__location__property__dangerousGoodAllowed": "Dangerous goods allowed", "@sage/xtrem-master-data/nodes__location__property__id": "ID", "@sage/xtrem-master-data/nodes__location__property__isActive": "Active", "@sage/xtrem-master-data/nodes__location__property__isVirtualAllowed": "Is virtual allowed", "@sage/xtrem-master-data/nodes__location__property__locationType": "Location type", "@sage/xtrem-master-data/nodes__location__property__locationZone": "Location zone", "@sage/xtrem-master-data/nodes__location__property__name": "Name", "@sage/xtrem-master-data/nodes__location__property__site": "Site", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_sequence__node_name": "Location sequence number", "@sage/xtrem-master-data/nodes__location_sequence__property__components": "Components", "@sage/xtrem-master-data/nodes__location_sequence__property__lastSequenceUsed": "Last sequence used", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsRemaining": "Number of locations remaining", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsUsed": "Number of locations used", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_sequence_component__node_name": "Location sequence number component", "@sage/xtrem-master-data/nodes__location_sequence_component__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_type__node_name": "Location type", "@sage/xtrem-master-data/nodes__location_type__property__description": "Description", "@sage/xtrem-master-data/nodes__location_type__property__id": "ID", "@sage/xtrem-master-data/nodes__location_type__property__isVirtualAllowed": "Is virtual allowed", "@sage/xtrem-master-data/nodes__location_type__property__locationCategory": "Location category", "@sage/xtrem-master-data/nodes__location_type__property__name": "Name", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__location_zone__node_name": "Location zone", "@sage/xtrem-master-data/nodes__location_zone__property__id": "ID", "@sage/xtrem-master-data/nodes__location_zone__property__isVirtualAllowed": "Is virtual allowed", "@sage/xtrem-master-data/nodes__location_zone__property__locations": "Locations", "@sage/xtrem-master-data/nodes__location_zone__property__name": "Name", "@sage/xtrem-master-data/nodes__location_zone__property__site": "Site", "@sage/xtrem-master-data/nodes__location_zone__property__zoneType": "Zone type", "@sage/xtrem-master-data/nodes__location-type__location_category_virtual_not_allowed": "Virtual location category is not allowed.", "@sage/xtrem-master-data/nodes__location-zone__site_modify": "The site cannot be updated. Locations are linked to this storage zone.", "@sage/xtrem-master-data/nodes__location-zone__zone_type_virtual_not_allowed": "Virtual location zone type is not allowed.", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__machine_resource__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__machine_resource__node_name": "Machine resource", "@sage/xtrem-master-data/nodes__machine_resource__property__contractId": "Contract ID", "@sage/xtrem-master-data/nodes__machine_resource__property__contractName": "Contract name", "@sage/xtrem-master-data/nodes__machine_resource__property__minCapabilityLevel": "Minimum capability level", "@sage/xtrem-master-data/nodes__machine_resource__property__model": "Model", "@sage/xtrem-master-data/nodes__machine_resource__property__serialNumber": "Serial number", "@sage/xtrem-master-data/nodes__machine_resource__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__payment_term__node_name": "Payment term", "@sage/xtrem-master-data/nodes__payment_term__property__businessEntityType": "Business entity type", "@sage/xtrem-master-data/nodes__payment_term__property__days": "Days", "@sage/xtrem-master-data/nodes__payment_term__property__description": "Description", "@sage/xtrem-master-data/nodes__payment_term__property__discountAmount": "Discount amount", "@sage/xtrem-master-data/nodes__payment_term__property__discountDate": "Discount date", "@sage/xtrem-master-data/nodes__payment_term__property__discountFrom": "Discount from", "@sage/xtrem-master-data/nodes__payment_term__property__discountType": "Discount type", "@sage/xtrem-master-data/nodes__payment_term__property__dueDateType": "Due date type", "@sage/xtrem-master-data/nodes__payment_term__property__id": "ID", "@sage/xtrem-master-data/nodes__payment_term__property__isActive": "Active", "@sage/xtrem-master-data/nodes__payment_term__property__name": "Name", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyAmount": "Penalty amount", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyType": "Penalty type", "@sage/xtrem-master-data/nodes__payment_term_discount_amount_percentage_error": "The discount amount needs to be less than 100%.", "@sage/xtrem-master-data/nodes__payment_term_discount_date_should_be_before_due_date": "", "@sage/xtrem-master-data/nodes__payment_term_discount_from_needs_to_match_due_date_type": "", "@sage/xtrem-master-data/nodes__payment_term_discount_mandatory": "When you enter a date, the discount type and amount are required.", "@sage/xtrem-master-data/nodes__payment_term_penalty_amount_percentage_error": "The penalty amount needs to be less than 100%.", "@sage/xtrem-master-data/nodes__range_sequence_component__node_name": "Range sequence component", "@sage/xtrem-master-data/nodes__range_sequence_component__property__endValue": "End value", "@sage/xtrem-master-data/nodes__range_sequence_component__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__range_sequence_component__property__startValue": "Start value", "@sage/xtrem-master-data/nodes__range_sequence_number__last_sequence_used_not_found": "The BOM revision number format is incorrect. Enter a new BOM revision number.", "@sage/xtrem-master-data/nodes__range_sequence_number__no_combinations_found": "The sequence has reached the end value. No more sequence numbers can be generated.", "@sage/xtrem-master-data/nodes__range_sequence_number__node_name": "Range sequence number", "@sage/xtrem-master-data/nodes__range_sequence_number__property__components": "Components", "@sage/xtrem-master-data/nodes__range_sequence_number__property__definitionLevel": "Definition level", "@sage/xtrem-master-data/nodes__range_sequence_number__property__isChronological": "Is chronological", "@sage/xtrem-master-data/nodes__range_sequence_number__property__legislation": "Legislation", "@sage/xtrem-master-data/nodes__range_sequence_number__property__numberOfCombinations": "Number of combinations", "@sage/xtrem-master-data/nodes__range_sequence_number__property__rtzLevel": "Rtz level", "@sage/xtrem-master-data/nodes__range_sequence_number__property__type": "Type", "@sage/xtrem-master-data/nodes__range_sequence_number__required_combinations_must_be_greater_than_zero": "You need to enter a number greater than zero.", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__reason_code__node_name": "Reason code", "@sage/xtrem-master-data/nodes__reason_code__property__id": "ID", "@sage/xtrem-master-data/nodes__reason_code__property__isActive": "Is active", "@sage/xtrem-master-data/nodes__reason_code__property__name": "Name", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__resource_cost_category__node_name": "Resource cost category", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costCategory": "Cost category", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costUnit": "Cost unit", "@sage/xtrem-master-data/nodes__resource_cost_category__property__indirectCostSection": "Indirect cost section", "@sage/xtrem-master-data/nodes__resource_cost_category__property__resource": "Resource", "@sage/xtrem-master-data/nodes__resource_cost_category__property__runCost": "Run cost", "@sage/xtrem-master-data/nodes__resource_cost_category__property__setupCost": "Setup cost", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__resource_group_replacement__node_name": "Resource group replacement", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__replacement": "Replacement", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__resourceGroup": "Resource group", "@sage/xtrem-master-data/nodes__resource-cost-category__the-cost-category-is-mandatory": "The {{costCategory}} cost category is mandatory.", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number__node_name": "Sequence number", "@sage/xtrem-master-data/nodes__sequence_number__property__components": "Components", "@sage/xtrem-master-data/nodes__sequence_number__property__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number__property__values": "Values", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_company_mandatory": "You must enter a sequence number for the {{companyId}} company.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_site_mandatory": "You must enter a sequence number for the {{siteId}} site.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__node_name": "Sequence number assignment", "@sage/xtrem-master-data/nodes__sequence_number_assignment__only_allowed_for_sales_invoice_credit_memo_fr_legislation": "Posted sequence numbers can only be assigned to sales invoices and credit memos for the French legislation.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__company": "Company", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__currentLegislation": "Current legislation", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__currentLegislationId": "Current legislation ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isActive": "Is active", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isAssignOnPosting": "Assign on posting", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isDefaultAssignment": "Default assignment", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isUsed": "Used", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__legislation": "Legislation", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumberAssignmentDocumentType": "Sequence number assignment document type", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__setupId": "Setup ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__site": "Site", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__node_name": "Sequence number assignment document type", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__displayOrder": "Display order", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__node": "Node", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeFactory": "Node factory", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeName": "Node name", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeValues": "Node values", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignmentModule": "Sequence number assignment module", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignments": "Sequence number assignments", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__setupId": "Setup ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__node_name": "Sequence number assignment module", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__name": "Name", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__nodes": "Nodes", "@sage/xtrem-master-data/nodes__sequence_number_assignment_node_forbidden": "The assigned document {{document}} could not be found.", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__node_name": "Sequence number Assignment Setup", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__property__modules": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_component__node_name": "Sequence number component", "@sage/xtrem-master-data/nodes__sequence_number_component__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__sequence_number_value__node_name": "Sequence number value", "@sage/xtrem-master-data/nodes__sequence_number_value__property__additionalInfo": "Additional info", "@sage/xtrem-master-data/nodes__sequence_number_value__property__company": "Company", "@sage/xtrem-master-data/nodes__sequence_number_value__property__period": "Period", "@sage/xtrem-master-data/nodes__sequence_number_value__property__periodDate": "Period date", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceNumber": "Sequence number", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceValue": "Sequence value", "@sage/xtrem-master-data/nodes__sequence_number_value__property__site": "Site", "@sage/xtrem-master-data/nodes__sequence-number__length_exceed": "Sequence number length exceeded. You need to enter a sequence number with less digits.", "@sage/xtrem-master-data/nodes__sequence-number__no_sequence_number_component": "Enter a sequence number component for the sequence number.", "@sage/xtrem-master-data/nodes__sequence-number__sequence-counter-monthly-reset-no-month-component": "The sequence number is defined for monthly reset. Enter a month in the components.", "@sage/xtrem-master-data/nodes__sequence-number__sequence-counter-not-at-company-level-definition-and-no-input-value-for-company": "The sequence number is defined at the company level. You must enter a company in the components.", "@sage/xtrem-master-data/nodes__sequence-number__sequence-counter-not-at-site-level-definition-and-no-input-value-for-site": "The sequence number is defined at the site level. You must enter a site in the components.", "@sage/xtrem-master-data/nodes__sequence-number__sequence-counter-yearly-reset-no-year-component": "The sequence number is defined for yearly reset. Enter a year in the components.", "@sage/xtrem-master-data/nodes__sequence-number__sequence-numeric-wrong-component": "The sequence number is numeric. You can only enter numeric components.", "@sage/xtrem-master-data/nodes__sequence-number-assignement/node_name_error": "{{node<PERSON>ame}} could not be found.", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__shift_detail__node_name": "Shift detail", "@sage/xtrem-master-data/nodes__shift_detail__property__duration": "Duration", "@sage/xtrem-master-data/nodes__shift_detail__property__formattedDuration": "Formatted duration", "@sage/xtrem-master-data/nodes__shift_detail__property__id": "ID", "@sage/xtrem-master-data/nodes__shift_detail__property__name": "Name", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftEnd": "Shift end", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftStart": "Shift start", "@sage/xtrem-master-data/nodes__site__create_new_site": "", "@sage/xtrem-master-data/nodes__site__create_new_site_fail": "", "@sage/xtrem-master-data/nodes__site_extension__corporation_and_site": "You cannot define the legal entity as {{legalEntity}} because the business entity is defined as a site.", "@sage/xtrem-master-data/nodes__site_extension__current_site_is_financial_site": "The current site is already a financial site.", "@sage/xtrem-master-data/nodes__site_extension__financial_site_mandatory": "This site is not a financial site. Enter a financial site.", "@sage/xtrem-master-data/nodes__site_extension__invalid_timezone": "The time zone value is not valid: ({{value}}).", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__standard__node_name": "Standard", "@sage/xtrem-master-data/nodes__standard__property__code": "Code", "@sage/xtrem-master-data/nodes__standard__property__id": "ID", "@sage/xtrem-master-data/nodes__standard__property__industrySector": "Industry sector", "@sage/xtrem-master-data/nodes__standard__property__name": "Name", "@sage/xtrem-master-data/nodes__standard__property__sdo": "Standard", "@sage/xtrem-master-data/nodes__standard__property__version": "Version", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__standard_industrial_classification__node_name": "Standard Industrial Classification", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__legislation": "Legislation", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicCode": "SIC code", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicDescription": "SIC description", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__supplier__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__supplier__create_new_supplier_fail": "", "@sage/xtrem-master-data/nodes__supplier__node_name": "Supplier", "@sage/xtrem-master-data/nodes__supplier__property__billByAddress": "Bill-by address", "@sage/xtrem-master-data/nodes__supplier__property__billBySupplier": "Bill-by supplier", "@sage/xtrem-master-data/nodes__supplier__property__category": "Category", "@sage/xtrem-master-data/nodes__supplier__property__certificates": "Certificates", "@sage/xtrem-master-data/nodes__supplier__property__deliveryMode": "Delivery mode", "@sage/xtrem-master-data/nodes__supplier__property__incoterm": "Incoterms rule", "@sage/xtrem-master-data/nodes__supplier__property__internalNote": "Internal note", "@sage/xtrem-master-data/nodes__supplier__property__isActive": "Active", "@sage/xtrem-master-data/nodes__supplier__property__itemPrices": "Item prices", "@sage/xtrem-master-data/nodes__supplier__property__items": "Items", "@sage/xtrem-master-data/nodes__supplier__property__parent": "Parent", "@sage/xtrem-master-data/nodes__supplier__property__paymentMethod": "Payment method", "@sage/xtrem-master-data/nodes__supplier__property__paymentTerm": "Payment term", "@sage/xtrem-master-data/nodes__supplier__property__payToAddress": "Pay-to address", "@sage/xtrem-master-data/nodes__supplier__property__payToSupplier": "Pay-to supplier", "@sage/xtrem-master-data/nodes__supplier__property__prices": "Prices", "@sage/xtrem-master-data/nodes__supplier__property__returnToAddress": "Return-to address", "@sage/xtrem-master-data/nodes__supplier__property__returnToSupplier": "Return-to supplier", "@sage/xtrem-master-data/nodes__supplier__property__standardIndustrialClassification": "Standard Industrial Classification", "@sage/xtrem-master-data/nodes__supplier__property__supplierType": "Supplier type", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier": "Get default supplier", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__site": "Site", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate": "Renew certificate", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__certificate": "Certificate", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__newCertificateDate": "New certificate date", "@sage/xtrem-master-data/nodes__supplier_certificate__node_name": "Supplier certificate", "@sage/xtrem-master-data/nodes__supplier_certificate__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__team__node_name": "Team", "@sage/xtrem-master-data/nodes__team__property__description": "Description", "@sage/xtrem-master-data/nodes__team__property__id": "ID", "@sage/xtrem-master-data/nodes__team__property__name": "Name", "@sage/xtrem-master-data/nodes__team__property__site": "Site", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__tool_resource__bulkMutation__bulkDelete": "Bulk delete", "@sage/xtrem-master-data/nodes__tool_resource__node_name": "Tool resource", "@sage/xtrem-master-data/nodes__tool_resource__property__consumptionMode": "Consumption mode", "@sage/xtrem-master-data/nodes__tool_resource__property__hoursTracked": "Hours tracked", "@sage/xtrem-master-data/nodes__tool_resource__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__tool_resource__property__quantity": "Quantity", "@sage/xtrem-master-data/nodes__tool_resource__property__unitProduced": "Unit produced", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__item": "The customer must have an item for the unit conversion.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__type": "You need to enter a 'Sales' flow type before you can enter a customer.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__flow_without_item": "You cannot have a unit conversion factor for a flow with no items.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_delete": "You cannot delete the unit conversion factor. The 'isStandard' property is true.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_property_update": "You cannot update the isStandard property.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_update": "You cannot update, delete or add a unit conversion factor if the 'isStandard' property is true.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__item__type": "You need to enter an item before you can enter a flow type.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__purchase__customer": "The purchase flow type and the customer cannot be combined.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__sales__supplier": "The sales flow type and the supplier cannot be combined.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__customer": "Only the supplier or the customer must be set but not both.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__item": "The supplier must have an item for the unit conversion.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__type": "You need to enter a 'Purchase' flow type before you can enter a supplier.", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__unit_conversion_factor__node_name": "Unit conversion factor", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__coefficient": "Factor", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__customer": "Customer", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__fromUnit": "From unit", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__isStandard": "Standard", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__supplier": "Supplier", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__toUnit": "To unit", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__type": "Type", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__unit_of_measure__decrease_not_possible_unit_of_measure_is_used": "You cannot decrease the decimal digits for a unit of measure that is being used.", "@sage/xtrem-master-data/nodes__unit_of_measure__node_name": "Unit of measure", "@sage/xtrem-master-data/nodes__unit_of_measure__property__conversionFactor": "Conversion factor", "@sage/xtrem-master-data/nodes__unit_of_measure__property__decimalDigits": "Decimal digits", "@sage/xtrem-master-data/nodes__unit_of_measure__property__description": "Description", "@sage/xtrem-master-data/nodes__unit_of_measure__property__id": "ID", "@sage/xtrem-master-data/nodes__unit_of_measure__property__isActive": "Active", "@sage/xtrem-master-data/nodes__unit_of_measure__property__name": "Name", "@sage/xtrem-master-data/nodes__unit_of_measure__property__symbol": "Symbol", "@sage/xtrem-master-data/nodes__unit_of_measure__property__type": "Type", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo": "Convert from to", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__conversionFactor": "Conversion factor", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__customer": "Customer", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__formatToUnitDecimalDigits": "Format to unit decimal digits", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__fromUnit": "From unit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__quantity": "Quantity", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__supplier": "Supplier", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__toUnit": "To unit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__type": "Type", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit": "Get purchase unit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__supplier": "Supplier", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor": "Get unit conversion factor", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__customer": "Customer", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__fromUnit": "From unit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__supplier": "Supplier", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__toUnit": "To unit", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__type": "Type", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__version_information__node_name": "Version information", "@sage/xtrem-master-data/nodes__version_information__property__text": "Text", "@sage/xtrem-master-data/nodes__version_information__property__version": "Version", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport": "Export", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-master-data/nodes__weekly_shift__no_daily_shift_on_full_week_shift": "A full-week shift cannot have daily shifts.", "@sage/xtrem-master-data/nodes__weekly_shift__node_name": "Weekly shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__capacity": "Capacity", "@sage/xtrem-master-data/nodes__weekly_shift__property__formattedCapacity": "Formatted capacity", "@sage/xtrem-master-data/nodes__weekly_shift__property__fridayShift": "Friday shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__id": "ID", "@sage/xtrem-master-data/nodes__weekly_shift__property__isFullWeek": "Full week", "@sage/xtrem-master-data/nodes__weekly_shift__property__mondayShift": "Monday shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__name": "Name", "@sage/xtrem-master-data/nodes__weekly_shift__property__saturdayShift": "Saturday shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__sundayShift": "Sunday shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__thursdayShift": "Thursday shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__tuesdayShift": "Tuesday shift", "@sage/xtrem-master-data/nodes__weekly_shift__property__wednesdayShift": "Wednesday shift", "@sage/xtrem-master-data/nodes__work_in_progress__node_name": "Work in progress", "@sage/xtrem-master-data/nodes__work_in_progress__property__actualQuantity": "Actual quantity", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentId": "Document ID", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentLine": "Document line", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentNumber": "Document number", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentType": "Document type", "@sage/xtrem-master-data/nodes__work_in_progress__property__endDate": "End date", "@sage/xtrem-master-data/nodes__work_in_progress__property__expectedQuantity": "Expected quantity", "@sage/xtrem-master-data/nodes__work_in_progress__property__item": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentLine": "Origin document line", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentType": "Origin document type", "@sage/xtrem-master-data/nodes__work_in_progress__property__outstandingQuantity": "Outstanding quantity", "@sage/xtrem-master-data/nodes__work_in_progress__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-master-data/nodes__work_in_progress__property__site": "Site", "@sage/xtrem-master-data/nodes__work_in_progress__property__startDate": "Start date", "@sage/xtrem-master-data/nodes__work_in_progress__property__status": "Status", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite": "Get work in progress quantity per item site", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentDate": "Current date", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentItem": "Current item", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentSite": "Current site", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentStatus": "Current status", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__originDocumentType": "Origin document type", "@sage/xtrem-master-data/nodes_sequence_number_assignment_company_leg": "Legislation {{legislation}} must be the same as your company legislation {{companyLegislation}}.", "@sage/xtrem-master-data/nodes_sequence_number_assignment_site_error": "Your site must belong to {{company}}.", "@sage/xtrem-master-data/nodes-resource-group-cannot-be-replaced-by-itself": "The resource group cannot be replaced by itself.", "@sage/xtrem-master-data/nodes-resource-group-replacement-resource-group-should-be-replaced-by-another-one-with-same-site": "The resource group must be replaced with another from the same site.", "@sage/xtrem-master-data/or": "Or", "@sage/xtrem-master-data/package__name": "Master", "@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available": "No price list available.", "@sage/xtrem-master-data/page__item_customer_price_panel__price_is_zero": "The price is equal to zero.", "@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available": "No price list available.", "@sage/xtrem-master-data/page-extensions__user_extension____navigationPanel__listItem__line11__title": "Selected dashboard", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__firstName": "Owner", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__lastName": "Owner", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__title": "Title", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____lookupDialogTitle": "Select dashboard", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____title": "Selected dashboard", "@sage/xtrem-master-data/pages__address_functions__businessentity_primary_active_address_mandatory": "Assign a primary, active address to the business entity.", "@sage/xtrem-master-data/pages__address_functions__company_primary_active_address_mandatory": "Assign a primary, active address to the company.", "@sage/xtrem-master-data/pages__address_functions__customer_primary_active_address_mandatory": "Assign a primary, active address to the customer.", "@sage/xtrem-master-data/pages__address_functions__site_primary_active_address_mandatory": "Assign a primary, active address to the site.", "@sage/xtrem-master-data/pages__address_functions__supplier_primary_active_address_mandatory": "Assign a primary, active address to the supplier.", "@sage/xtrem-master-data/pages__address_functions_businessentity_add_new____title": "New business entity address", "@sage/xtrem-master-data/pages__address_functions_businessentity_address_contacts____title": "Contacts for business entity address {{addressName}}", "@sage/xtrem-master-data/pages__address_functions_businessentity_edit____title": "Edit business entity address", "@sage/xtrem-master-data/pages__address_panel__contacts____dropdownActions__title__4": "Delete", "@sage/xtrem-master-data/pages__address_panel__edit____title": "Edit address", "@sage/xtrem-master-data/pages__address_panel__isPrimary_must_be_active": "A primary address must be active.", "@sage/xtrem-master-data/pages__address_panel__new____title": "New address", "@sage/xtrem-master-data/pages__address-contacts_panel_add_new____title": "New contact", "@sage/xtrem-master-data/pages__address-contacts_panel_edit____title": "Edit contact", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_active": "Active", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_inactive": "Inactive", "@sage/xtrem-master-data/pages__allergen____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__allergen____objectTypePlural": "Allergens", "@sage/xtrem-master-data/pages__allergen____objectTypeSingular": "Allergen", "@sage/xtrem-master-data/pages__allergen____title": "Allergen", "@sage/xtrem-master-data/pages__allergen__allergenImageBlock____title": "Pictogram", "@sage/xtrem-master-data/pages__allergen__allergenInformationBlock____title": "Allergen information", "@sage/xtrem-master-data/pages__allergen__generalSection____title": "General", "@sage/xtrem-master-data/pages__allergen__name____title": "Name", "@sage/xtrem-master-data/pages__already_used_message": "The previous sequence number has already been used to generate a document number.", "@sage/xtrem-master-data/pages__already_used_title": "Sequence number used", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__componentLength__title": "Sequence length", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__isDefault__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypePlural": "BOM revision sequence", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypeSingular": "BOM revision sequence", "@sage/xtrem-master-data/pages__bom_revision_sequence____title": "BOM revision sequence", "@sage/xtrem-master-data/pages__bom_revision_sequence__addComponent____title": "Add", "@sage/xtrem-master-data/pages__bom_revision_sequence__capital_letters_only": "An alphabetical sequence number component can only contain capital letters.", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentLength____title": "Sequence length", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__endValue": "End value", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__startValue": "Start value", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____title": "Components", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentsBlock____title": "Components", "@sage/xtrem-master-data/pages__bom_revision_sequence__default": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__bom_revision_sequence__digits_only": "A numeric sequence number component can only contain numbers.", "@sage/xtrem-master-data/pages__bom_revision_sequence__id____title": "ID", "@sage/xtrem-master-data/pages__bom_revision_sequence__invalid_range": "The start value cannot be after the end value.", "@sage/xtrem-master-data/pages__bom_revision_sequence__isDefault____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainSection____title": "General", "@sage/xtrem-master-data/pages__bom_revision_sequence__name____title": "Name", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__listItem__line6__title": "Tax ID", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__business_entity____objectTypePlural": "Business entities", "@sage/xtrem-master-data/pages__business_entity____objectTypeSingular": "Business entity", "@sage/xtrem-master-data/pages__business_entity____title": "Business entity", "@sage/xtrem-master-data/pages__business_entity__address_active": "Active", "@sage/xtrem-master-data/pages__business_entity__address_inactive": "Inactive", "@sage/xtrem-master-data/pages__business_entity__addresses____addButtonText": "Add address", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__concatenatedAddressWithoutName": "Address without name", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__contacts": "Contacts", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__isPrimary": "Primary address", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title": "Edit address", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__2": "Define as primary address", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__3": "Add contact", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__4": "Delete address and contacts", "@sage/xtrem-master-data/pages__business_entity__addresses____title": "Addresses", "@sage/xtrem-master-data/pages__business_entity__addressSection____title": "Addresses", "@sage/xtrem-master-data/pages__business_entity__contact_active": "Active", "@sage/xtrem-master-data/pages__business_entity__contact_inactive": "Inactive", "@sage/xtrem-master-data/pages__business_entity__contacts____addButtonText": "Add contact", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__isPrimary": "Primary contact", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__2": "Define as primary", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__3": "Delete", "@sage/xtrem-master-data/pages__business_entity__contacts____headerLabel__title": "Is Active", "@sage/xtrem-master-data/pages__business_entity__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__business_entity__country____lookupDialogTitle": "Select country", "@sage/xtrem-master-data/pages__business_entity__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__business_entity__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__business_entity__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__displayAddresses____columns__title": "Contacts", "@sage/xtrem-master-data/pages__business_entity__id____title": "ID", "@sage/xtrem-master-data/pages__business_entity__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__business_entity__isCustomer____title": "Customer", "@sage/xtrem-master-data/pages__business_entity__isSite____title": "Site", "@sage/xtrem-master-data/pages__business_entity__isSupplier____title": "Supplier", "@sage/xtrem-master-data/pages__business_entity__legalEntity____title": "Legal entity", "@sage/xtrem-master-data/pages__business_entity__mainBlock____title": "General", "@sage/xtrem-master-data/pages__business_entity__mainSection____title": "General", "@sage/xtrem-master-data/pages__business_entity__name____title": "Name", "@sage/xtrem-master-data/pages__business_entity__parent____lookupDialogTitle": "Select parent business entity", "@sage/xtrem-master-data/pages__business_entity__parent____title": "Parent business entity", "@sage/xtrem-master-data/pages__business_entity__roleBlock____title": "Roles", "@sage/xtrem-master-data/pages__business_entity__save____title": "Save", "@sage/xtrem-master-data/pages__business_entity__siret____title": "SIRET", "@sage/xtrem-master-data/pages__business_entity__taxIdNumber____title": "Tax ID", "@sage/xtrem-master-data/pages__business_entity__website____title": "Website", "@sage/xtrem-master-data/pages__business_entity_address_panel____title": "Business entity address panel", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine1____title": "Address line 1", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine2____title": "Address line 2", "@sage/xtrem-master-data/pages__business_entity_address_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__business_entity_address_panel__city____title": "City", "@sage/xtrem-master-data/pages__business_entity_address_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____lookupDialogTitle": "Select country", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryDetail____title": "Delivery detail", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____postfix": "day(s)", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____title": "Delivery lead time", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____title": "Delivery mode", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____lookupDialogTitle": "Select Incoterms rule", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____title": "Incoterms® rule", "@sage/xtrem-master-data/pages__business_entity_address_panel__informationBlock____title": "Shipping information", "@sage/xtrem-master-data/pages__business_entity_address_panel__isActiveShippingAddress____title": "Active", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimary____title": "Primary address", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimaryShippingAddress____title": "Primary ship-to address", "@sage/xtrem-master-data/pages__business_entity_address_panel__isShippingAddress____title": "Ship-to address", "@sage/xtrem-master-data/pages__business_entity_address_panel__locationPhoneNumber____title": "Phone number", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainBlock____title": "Address information", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainSection____title": "Address", "@sage/xtrem-master-data/pages__business_entity_address_panel__name____title": "Name", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____title": "Shipping site", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingBlock____title": "Shipping information", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingSection____title": "Information", "@sage/xtrem-master-data/pages__business_entity_address_panel__workDaysSelection____title": "Working days", "@sage/xtrem-master-data/pages__business_entity_contact_panel____title": "Business entity address contact panel", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____lookupDialogTitle": "Select address", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____title": "Address", "@sage/xtrem-master-data/pages__business_entity_contact_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__business_entity_contact_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__business_entity_contact_panel__email____title": "Email", "@sage/xtrem-master-data/pages__business_entity_contact_panel__firstName____title": "First name", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isPrimary____title": "Primary contact", "@sage/xtrem-master-data/pages__business_entity_contact_panel__lastName____title": "Last name", "@sage/xtrem-master-data/pages__business_entity_contact_panel__locationPhoneNumber____title": "Phone number", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainBlock____title": "Contact information", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_contact_panel__position____title": "Position", "@sage/xtrem-master-data/pages__business_entity_contact_panel__preferredName____title": "Preferred name", "@sage/xtrem-master-data/pages__business_entity_contact_panel__role____title": "Role", "@sage/xtrem-master-data/pages__business_entity_contact_panel__title____title": "Title", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____subtitle": "Choose a business entity to match your requirements.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____title": "Create a customer from a business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressBlock____title": "Ship-to address definition.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____addButtonText": "Add address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title": "Working days", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__concatenatedAddressWithoutName": "Address without name", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail": "Ship-to address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__incoterm__name": "Incoterms® rule", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isActive": "Ship-to address status", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isPrimary": "Primary ship-to address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__leadTime": "Delivery lead time", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__mode__name": "Delivery mode", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Shipment site", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__isPrimary": "Primary address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title": "Edit address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__2": "Define as primary address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__3": "Define as primary ship-to address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__4": "Delete address and contacts", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____title": "Addresses", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressSection____title": "Address", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____lookupDialogTitle": "Select business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____title": "Create from business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity_already_a_customer": "This business entity is already set as a customer.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialBlock____title": "Financial.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialSection____title": "Financial", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalBlock____title": "Select the business entity to create the customer from.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__primary_ship_to_address_mandatory": "The customer needs to have at least one active primary ship-to address.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____subtitle": "Choose a business entity to match your requirements.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____title": "Create a site from a business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____lookupDialogTitle": "Select business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____title": "Create from business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity_already_a_site": "This business entity is already set as a site.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____lookupDialogTitle": "Select financial site", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalBlock____title": "Select the business entity to create the site from.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__isFinance____title": "Finance", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____lookupDialogTitle": "Select company", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementBlock____title": "Management", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementSection____title": "Management", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____subtitle": "Choose a business entity to match your requirements.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____title": "Create a supplier from a business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____lookupDialogTitle": "Select business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____title": "Create from business entity", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity_already_a_supplier": "This business entity is already set as a supplier.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialBlock____title": "Financial.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialSection____title": "Financial", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalBlock____title": "Select the business entity to create the supplier from.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__capability_level____objectTypePlural": "Capability levels", "@sage/xtrem-master-data/pages__capability_level____objectTypeSingular": "Capability level", "@sage/xtrem-master-data/pages__capability_level____title": "Capability level", "@sage/xtrem-master-data/pages__capability_level__description____title": "Description", "@sage/xtrem-master-data/pages__capability_level__id____title": "ID", "@sage/xtrem-master-data/pages__capability_level__level____title": "Level", "@sage/xtrem-master-data/pages__capability_level__mainBlock____title": "General", "@sage/xtrem-master-data/pages__capability_level__mainSection____title": "General", "@sage/xtrem-master-data/pages__capability_level__name____title": "Name", "@sage/xtrem-master-data/pages__capability_panel____title": "Capability", "@sage/xtrem-master-data/pages__capability_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____lookupDialogTitle": "Select capability level", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____title": "Capability level", "@sage/xtrem-master-data/pages__capability_panel__create____title": "OK", "@sage/xtrem-master-data/pages__capability_panel__dateEndValid____title": "Active to", "@sage/xtrem-master-data/pages__capability_panel__dateStartValid____title": "Active from", "@sage/xtrem-master-data/pages__capability_panel__id____title": "ID", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__minCapabilityLevel__level": "Min. capability level", "@sage/xtrem-master-data/pages__capability_panel__machine____lookupDialogTitle": "Select machine", "@sage/xtrem-master-data/pages__capability_panel__machine____title": "Machine", "@sage/xtrem-master-data/pages__capability_panel__name____title": "Name", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__capability_panel__service____lookupDialogTitle": "Select service item", "@sage/xtrem-master-data/pages__capability_panel__service____title": "Service item", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__resourceGroup__name": "Resource group", "@sage/xtrem-master-data/pages__capability_panel__tool____lookupDialogTitle": "Select tool", "@sage/xtrem-master-data/pages__capability_panel__tool____title": "Tool", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_business_entity_address_title": "Edit business entity address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_company_address_title": "Edit company address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_customer_address_title": "Edit customer address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_site_address_title": "Edit site address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_supplier_address_title": "Edit supplier address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_business_entity_address_title": "New business entity address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_company_address_title": "New company address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_customer_address_title": "New customer address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_site_address_title": "New site address", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_supplier_address_title": "New supplier address", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__edit_contact_title": "Edit contact", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__new_contact_title": "New contact", "@sage/xtrem-master-data/pages__company____add_site": "Add site", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line10__title": "Legal form", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line11__title": "Sequence number value", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line8__title": "NAF (APE)", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__company____objectTypePlural": "Companies", "@sage/xtrem-master-data/pages__company____objectTypeSingular": "Company", "@sage/xtrem-master-data/pages__company____title": "Company", "@sage/xtrem-master-data/pages__company__addresses____addButtonText": "Add address", "@sage/xtrem-master-data/pages__company__addresses____columns__title__concatenatedAddressWithoutName": "Address without name", "@sage/xtrem-master-data/pages__company__addresses____columns__title__contacts": "Contacts", "@sage/xtrem-master-data/pages__company__addresses____columns__title__isPrimary": "Primary address", "@sage/xtrem-master-data/pages__company__addresses____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title": "Edit address", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__2": "Define as primary address", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__3": "Add contact", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__4": "Delete address and contacts", "@sage/xtrem-master-data/pages__company__addresses____title": "Addresses", "@sage/xtrem-master-data/pages__company__addressSection____title": "Addresses", "@sage/xtrem-master-data/pages__company__contacts____addButtonText": "Add contact", "@sage/xtrem-master-data/pages__company__contacts____columns__title__isPrimary": "Primary contact", "@sage/xtrem-master-data/pages__company__contacts____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__company__contacts____columns__title__role": "Role", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__2": "Define as primary", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__3": "Delete", "@sage/xtrem-master-data/pages__company__contacts____headerLabel__title": "Is Active", "@sage/xtrem-master-data/pages__company__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__company__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__company__country____lookupDialogTitle": "Select country", "@sage/xtrem-master-data/pages__company__creditLimitBlock____title": "Credit limit", "@sage/xtrem-master-data/pages__company__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__company__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__company__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__customerOnHoldCheck____title": "Customer on hold check", "@sage/xtrem-master-data/pages__company__description____title": "Description", "@sage/xtrem-master-data/pages__company__id____title": "ID", "@sage/xtrem-master-data/pages__company__isSequenceNumberIdUsed____title": "Used", "@sage/xtrem-master-data/pages__company__legalForm____title": "Legal form", "@sage/xtrem-master-data/pages__company__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-master-data/pages__company__legislation____placeholder": "Select ...", "@sage/xtrem-master-data/pages__company__mainBlock____title": "Company information", "@sage/xtrem-master-data/pages__company__mainSection____title": "General", "@sage/xtrem-master-data/pages__company__managementSection____title": "Management", "@sage/xtrem-master-data/pages__company__naf____title": "NAF (APE)", "@sage/xtrem-master-data/pages__company__name____title": "Name", "@sage/xtrem-master-data/pages__company__paymentTrackingBlock____title": "Payment tracking", "@sage/xtrem-master-data/pages__company__rcs____title": "RCS", "@sage/xtrem-master-data/pages__company__save____title": "Save", "@sage/xtrem-master-data/pages__company__sequenceNumberId____title": "Sequence number value", "@sage/xtrem-master-data/pages__company__siren____title": "SIREN", "@sage/xtrem-master-data/pages__company__siteBlock____title": "Organization", "@sage/xtrem-master-data/pages__compare__number_remaining_to__required": "The required combinations cannot exceed the remaining combinations.", "@sage/xtrem-master-data/pages__contact_panel__isPrimary_must_be_active": "A primary contact must be active.", "@sage/xtrem-master-data/pages__contact_selection_panel____title": "Contact selection panel", "@sage/xtrem-master-data/pages__contact_selection_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__email": "Email", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__firstName": "First name", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__lastName": "Last name", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__title": "Title", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____title": "Contacts", "@sage/xtrem-master-data/pages__contact_selection_panel__contactSelectionBlock____title": "Select contacts", "@sage/xtrem-master-data/pages__contact_selection_panel__ok____title": "OK", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__firstName": "First name", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__lastName": "Last name", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__title": "Title", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____title": "Selected contact", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__consumedLocationCapacity__title": "Required storage capacity", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isInternal__title": "Internal", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleItem__title": "Single item", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleLot__title": "Single lot", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__labelFormat__title": "Label format", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__sequenceNumber__title": "Sequence number", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__storageCapacity__title": "Storage capacity", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__container____objectTypePlural": "Containers", "@sage/xtrem-master-data/pages__container____objectTypeSingular": "Container", "@sage/xtrem-master-data/pages__container____title": "Container", "@sage/xtrem-master-data/pages__container__consumedLocationCapacity____title": "Required storage capacity", "@sage/xtrem-master-data/pages__container__id____title": "ID", "@sage/xtrem-master-data/pages__container__isInternal____title": "Internal", "@sage/xtrem-master-data/pages__container__isSingleItem____title": "Single item", "@sage/xtrem-master-data/pages__container__isSingleLot____title": "Single lot", "@sage/xtrem-master-data/pages__container__labelFormat____title": "Label format", "@sage/xtrem-master-data/pages__container__mainSection____title": "General", "@sage/xtrem-master-data/pages__container__name____title": "Name", "@sage/xtrem-master-data/pages__container__sequenceNumber____lookupDialogTitle": "Select sequence number", "@sage/xtrem-master-data/pages__container__sequenceNumber____title": "Sequence number", "@sage/xtrem-master-data/pages__container__storageCapacity____title": "Storage capacity", "@sage/xtrem-master-data/pages__container__type____title": "Container type", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line3__title": "Type", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__cost_category____objectTypePlural": "Cost categories", "@sage/xtrem-master-data/pages__cost_category____objectTypeSingular": "Cost category", "@sage/xtrem-master-data/pages__cost_category____title": "Cost category", "@sage/xtrem-master-data/pages__cost_category__costCategoryType____title": "Type", "@sage/xtrem-master-data/pages__cost_category__id____title": "ID", "@sage/xtrem-master-data/pages__cost_category__isMandatory____title": "Mandatory", "@sage/xtrem-master-data/pages__cost_category__mainSection____title": "General", "@sage/xtrem-master-data/pages__cost_category__name____title": "Name", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line_5__title": "ISO 3166-1 alpha-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line2__title": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line6__title": "EU Member State", "@sage/xtrem-master-data/pages__country____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__country____objectTypePlural": "Countries", "@sage/xtrem-master-data/pages__country____objectTypeSingular": "Country", "@sage/xtrem-master-data/pages__country____title": "Country", "@sage/xtrem-master-data/pages__country__continent____title": "Continent", "@sage/xtrem-master-data/pages__country__countryFlagBlock____title": "Flag", "@sage/xtrem-master-data/pages__country__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__country__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__country__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country__generalSection____title": "General", "@sage/xtrem-master-data/pages__country__id____title": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__country__isEuMember____title": "EU Member State", "@sage/xtrem-master-data/pages__country__iso31661Alpha3____title": "ISO 3166-1 alpha-3", "@sage/xtrem-master-data/pages__country__legislation____placeholder": "Select legislation", "@sage/xtrem-master-data/pages__country__legislation____title": "Legislation", "@sage/xtrem-master-data/pages__country__mainBlock____title": "Country information", "@sage/xtrem-master-data/pages__country__name____title": "Name", "@sage/xtrem-master-data/pages__country__regionLabel____title": "Label for region", "@sage/xtrem-master-data/pages__country__zipLabel____title": "Label for postal code", "@sage/xtrem-master-data/pages__country_invalid_id": "The ID must contain 2 characters.", "@sage/xtrem-master-data/pages__country_invalid_iso_code": "The code must contain 3 characters.", "@sage/xtrem-master-data/pages__create_test_data____title": "Create test data", "@sage/xtrem-master-data/pages__create_test_data__instructions____content": "", "@sage/xtrem-master-data/pages__create_test_data__linkField____title": "Confluence page", "@sage/xtrem-master-data/pages__create_test_data__mainBlock____title": "Information", "@sage/xtrem-master-data/pages__create_test_data__mainSection____title": "General", "@sage/xtrem-master-data/pages__create-test-data__instuctions": "This page gives access to development tools to create large data sets for testing purposes.\n            \nPlease refer to the Confluence page for detailed instructions on the values for each property.", "@sage/xtrem-master-data/pages__create-test-data__link_instuctions": "Detailed instructions on how to create test data.", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line_4__title": "Decimal places", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line3__title": "ISO 4217", "@sage/xtrem-master-data/pages__currency____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__currency____objectTypePlural": "Currencies", "@sage/xtrem-master-data/pages__currency____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency___id____title": "ID", "@sage/xtrem-master-data/pages__currency__addExchangeRate____title": "Add", "@sage/xtrem-master-data/pages__currency__addRateSection____title": "Add rate", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____title": "From", "@sage/xtrem-master-data/pages__currency__converterResult____placeholder": "Result", "@sage/xtrem-master-data/pages__currency__converterResult____title": "Result", "@sage/xtrem-master-data/pages__currency__converterSection____title": "Converter", "@sage/xtrem-master-data/pages__currency__converterToAmount____placeholder": "Amount to convert", "@sage/xtrem-master-data/pages__currency__converterToAmount____title": "Amount to convert", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__id": "ID", "@sage/xtrem-master-data/pages__currency__converterToCurrency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__currency__converterToCurrency____placeholder": "Select currency", "@sage/xtrem-master-data/pages__currency__converterToCurrency____title": "To", "@sage/xtrem-master-data/pages__currency__currencyRate____title": "Rate", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__dateRate": "Date rate", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__id": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__symbol": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__rate": "Rate", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__shortDescription": "Short description", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__image__title": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line2Right__title": "Short description", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line3Right__title": "Date rate", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__title__title": "Destination", "@sage/xtrem-master-data/pages__currency__currentExchangeRatesSection____title": "Details", "@sage/xtrem-master-data/pages__currency__decimalDigits____title": "Decimal digits", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__destinationCurrency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__currency__destinationCurrency____placeholder": "Select currency", "@sage/xtrem-master-data/pages__currency__destinationCurrency____title": "Destination currency", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderBlock____title": "Current rates", "@sage/xtrem-master-data/pages__currency__divisor____title": "Source currency", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__dateRate": "Date", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__name": "Name", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__rate": "Rate", "@sage/xtrem-master-data/pages__currency__exchangeRates____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__currency__exchangeRates____title": "Exchange rates", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____chart__xAxis__title": "Date", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____title": "Exchange rates", "@sage/xtrem-master-data/pages__currency__exchangeRatesSection____title": "Exchange rates", "@sage/xtrem-master-data/pages__currency__icon____title": "Flag", "@sage/xtrem-master-data/pages__currency__id____title": "ISO 4217", "@sage/xtrem-master-data/pages__currency__invalid_id": "The ID must contain 3 characters.", "@sage/xtrem-master-data/pages__currency__mainSection____title": "General", "@sage/xtrem-master-data/pages__currency__name____title": "Name", "@sage/xtrem-master-data/pages__currency__rateDate____placeholder": "Select ...", "@sage/xtrem-master-data/pages__currency__rateDate____title": "Rate date", "@sage/xtrem-master-data/pages__currency__rounding____title": "Rounding", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_message": "The currencies and date are already linked to an exchange rate. Do you want to keep the existing rate or apply your new one?", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_title": "This rate already exists.", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_title": "Add exchange rate", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_reverse_rate_confirmation_message": "The inverse rate already exists for the specified date. Do you want to keep the existing rate or apply a new one?", "@sage/xtrem-master-data/pages__currency__side_panel_add_inverse_currency_rate_confirmation_title": "Inverse rate already exists", "@sage/xtrem-master-data/pages__currency__symbol____title": "Symbol", "@sage/xtrem-master-data/pages__customer____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title": "Put on hold", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__2": "Remove on hold", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_4__title": "Primary country", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_5__title": "City", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line10__title": "Email", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line12__title": "Tax ID", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line13__title": "Status", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line18__title": "Minimum order amount", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line19__title": "Payment term", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line20__title": "On hold", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line21__title": "Customer category", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line22__title": "Credit limit", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line6__title": "Postal code", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line7__title": "Primary phone no.", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line8__title": "Primary contact", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line9__title": "Primary contact phone no.", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title": "All open statuses", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__2": "All", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__3": "Active", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__4": "Inactive", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__5": "On hold", "@sage/xtrem-master-data/pages__customer____objectTypePlural": "Customers", "@sage/xtrem-master-data/pages__customer____objectTypeSingular": "Customer", "@sage/xtrem-master-data/pages__customer____title": "Customer", "@sage/xtrem-master-data/pages__customer__addItem____title": "Add", "@sage/xtrem-master-data/pages__customer__addItemPriceLine____title": "Add", "@sage/xtrem-master-data/pages__customer__addresses____addButtonText": "Add address", "@sage/xtrem-master-data/pages__customer__addresses____columns__title": "Working days", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__concatenatedAddressWithoutName": "Address without name", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail": "Ship-to address", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__incoterm__name": "Incoterms® rule", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isActive": "Ship-to address status", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isPrimary": "Primary ship-to address", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__leadTime": "Delivery lead time", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__mode__name": "Delivery mode", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Shipment site", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__isPrimary": "Primary address", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title": "Edit address", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__2": "Define as primary address", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__3": "Define as primary ship-to address", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__4": "Add contact", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__5": "Delete address and contacts", "@sage/xtrem-master-data/pages__customer__addresses____title": "Addresses", "@sage/xtrem-master-data/pages__customer__addressSection____title": "Address", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_id": "This ID is already assigned to a customer.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_name": "This name is already assigned to a customer.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_taxIdNumber": "This tax identification number is already assigned to a customer.", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine1": "Line 1", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine2": "Line 2", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__businessEntity__name": "Business entity", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-master-data/pages__customer__billToAddress____title": "Primary bill-to address", "@sage/xtrem-master-data/pages__customer__billToCustomer____title": "Bill-to customer", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____columns__title__concatenatedAddress": "Primary bill-to address", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____dropdownActions__title": "Replace", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____title": "Primary bill-to address", "@sage/xtrem-master-data/pages__customer__category____columns__title__sequenceNumber__name": "Sequence number", "@sage/xtrem-master-data/pages__customer__category____lookupDialogTitle": "Select category", "@sage/xtrem-master-data/pages__customer__commercialBlock____title": "Commercial", "@sage/xtrem-master-data/pages__customer__commercialSection____title": "Commercial", "@sage/xtrem-master-data/pages__customer__contact_assigned_primary": "You cannot delete a primary contact.", "@sage/xtrem-master-data/pages__customer__contacts____addButtonText": "Add contact", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__isPrimary": "Primary contact", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__2": "Define as primary", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__3": "Delete", "@sage/xtrem-master-data/pages__customer__contacts____headerLabel__title": "Is Active", "@sage/xtrem-master-data/pages__customer__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__customer__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__customer__country____lookupDialogTitle": "Select country", "@sage/xtrem-master-data/pages__customer__createFromBusinessEntity____title": "Create from business entity", "@sage/xtrem-master-data/pages__customer__creditLimit____title": "Credit limit", "@sage/xtrem-master-data/pages__customer__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title___id": "ID", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__name": "Name", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__regionLabel": "Region label", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__zipLabel": "ZIP label", "@sage/xtrem-master-data/pages__customer__display_address_active": "Active", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__3": "Ship-to address", "@sage/xtrem-master-data/pages__customer__displayStatus____title": "Display status", "@sage/xtrem-master-data/pages__customer__financialBlock____title": "Financial", "@sage/xtrem-master-data/pages__customer__financialSection____title": "Financial", "@sage/xtrem-master-data/pages__customer__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__customer__internalNote____helperText": "Notes display on internal documents.", "@sage/xtrem-master-data/pages__customer__internalNote____title": "Internal notes", "@sage/xtrem-master-data/pages__customer__isOnHold____title": "On hold", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__customer__businessEntity__name": "Customer", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__endDate": "End date", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__fromQuantity": "From quantity", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__description": "Item description", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__price": "Price", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__priceReason__name": "Price reason", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__salesSite__id": "Sales site", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__startDate": "Start date", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__stockSite__id": "Stock site", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__toQuantity": "To quantity", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__unit__id": "Unit", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title__2": "Edit", "@sage/xtrem-master-data/pages__customer__itemPricesSection____title": "Item prices", "@sage/xtrem-master-data/pages__customer__items____columns__columns__item__name__title": "Category", "@sage/xtrem-master-data/pages__customer__items____columns__title__id": "Item-customer ID", "@sage/xtrem-master-data/pages__customer__items____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__description": "Item description", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____columns__title__maximumSalesQuantity": "Maximum sales quantity", "@sage/xtrem-master-data/pages__customer__items____columns__title__minimumSalesQuantity": "Minimum sales quantity", "@sage/xtrem-master-data/pages__customer__items____columns__title__name": "Item-customer name", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnit__name": "Sales unit", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnitToStockUnitConversion": "Stock unit conversion factor", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title__2": "Delete", "@sage/xtrem-master-data/pages__customer__items____title": "Items", "@sage/xtrem-master-data/pages__customer__itemSection____title": "Items", "@sage/xtrem-master-data/pages__customer__mainBlock____title": "General", "@sage/xtrem-master-data/pages__customer__mainSection____title": "General", "@sage/xtrem-master-data/pages__customer__minimumOrderAmount____title": "Minimum order amount", "@sage/xtrem-master-data/pages__customer__noteBlock____title": "Notes", "@sage/xtrem-master-data/pages__customer__notesSection____title": "Notes", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine1": "Line 1", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine2": "Line 2", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__businessEntity__name": "Business entity", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__postcode": "ZIP code", "@sage/xtrem-master-data/pages__customer__payByAddress____title": "Primary pay-by address", "@sage/xtrem-master-data/pages__customer__payByCustomer____title": "Bill-to customer", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____columns__title__concatenatedAddress": "Primary pay-by address", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____dropdownActions__title": "Replace", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____title": "Primary pay-by address", "@sage/xtrem-master-data/pages__customer__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory": "Assign a primary and active contact to the address.", "@sage/xtrem-master-data/pages__customer__primary_ship_to_address_mandatory": "The customer needs to have at least one active primary ship-to address.", "@sage/xtrem-master-data/pages__customer__put_on_hold": "Customer put on hold", "@sage/xtrem-master-data/pages__customer__putOnHold____title": "Put on hold", "@sage/xtrem-master-data/pages__customer__remove_on_hold": "Customer hold removed", "@sage/xtrem-master-data/pages__customer__removeOnHold____title": "Remove on hold", "@sage/xtrem-master-data/pages__customer__save____title": "Save", "@sage/xtrem-master-data/pages__customer__website____title": "Website", "@sage/xtrem-master-data/pages__customer_address_panel_new__isPrimaryShippingAddress_must_be_active": "Enable the primary ship-to address.", "@sage/xtrem-master-data/pages__customer_contact_list__primary_address": "Primary address", "@sage/xtrem-master-data/pages__customer_contact_list__primary_contact": "Primary contact", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypePlural": "Customer price reasons", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypeSingular": "Customer price reason", "@sage/xtrem-master-data/pages__customer_price_reason____title": "Customer price reason", "@sage/xtrem-master-data/pages__customer_price_reason__description____title": "Description", "@sage/xtrem-master-data/pages__customer_price_reason__mainSection____title": "General", "@sage/xtrem-master-data/pages__customer_price_reason__name____title": "Name", "@sage/xtrem-master-data/pages__customer_price_reason__priority____title": "Priority", "@sage/xtrem-master-data/pages__customer_price_reason__priority_already_exists": "Priority {{priority}} already exists.", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Sequence number management", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line_4__title": "Supplier", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line3__title": "Customer", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__sequenceNumber__title": "Sequence number", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypePlural": "Supplier and customer categories", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypeSingular": "Supplier and customer category", "@sage/xtrem-master-data/pages__customer_supplier_category____title": "Supplier and customer category", "@sage/xtrem-master-data/pages__customer_supplier_category__id____title": "ID", "@sage/xtrem-master-data/pages__customer_supplier_category__isCustomer____title": "Customer", "@sage/xtrem-master-data/pages__customer_supplier_category__isSequenceNumberManagement____title": "Sequence number management", "@sage/xtrem-master-data/pages__customer_supplier_category__isSupplier____title": "Supplier", "@sage/xtrem-master-data/pages__customer_supplier_category__mainSection____title": "General", "@sage/xtrem-master-data/pages__customer_supplier_category__name____title": "Name", "@sage/xtrem-master-data/pages__customer_supplier_category__sequenceNumber____title": "Sequence number", "@sage/xtrem-master-data/pages__customer-supplier__category_dialog_content": "The category you selected is linked to a sequence number. Do you want to generate a new ID, or keep the current one?", "@sage/xtrem-master-data/pages__customer-supplier__generate_ID": "Generate ID", "@sage/xtrem-master-data/pages__customer-supplier__keep_current_id-": "Keep current ID", "@sage/xtrem-master-data/pages__customer-supplier__select_id_number_title": "Select ID number", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-customer": "Select customer sequence number ID", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier": "Select supplier sequence number ID", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier-customer": "Select supplier and customer sequence number ID", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__formattedCapacity__title": "Capacity", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__isFullDay__title": "Full day", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__daily_shift____objectTypePlural": "Daily shifts", "@sage/xtrem-master-data/pages__daily_shift____objectTypeSingular": "Daily shift", "@sage/xtrem-master-data/pages__daily_shift____title": "Daily shift", "@sage/xtrem-master-data/pages__daily_shift___id____title": "ID", "@sage/xtrem-master-data/pages__daily_shift__addShiftDetail____title": "Add shift details", "@sage/xtrem-master-data/pages__daily_shift__detailsBlock____title": "Details", "@sage/xtrem-master-data/pages__daily_shift__formattedCapacity____title": "Capacity", "@sage/xtrem-master-data/pages__daily_shift__isFullDay____title": "Full day", "@sage/xtrem-master-data/pages__daily_shift__mainBlock____title": "General", "@sage/xtrem-master-data/pages__daily_shift__mainSection____title": "General", "@sage/xtrem-master-data/pages__daily_shift__name____title": "Name", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__formattedDuration": "Duration", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__id": "ID", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__name": "Name", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftEnd": "Shift end", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftStart": "Shift start", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____title": "Shift details", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__formattedDuration": "Duration", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftEnd": "Shift end", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftStart": "Shift start", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____lookupDialogTitle": "Select shift detail", "@sage/xtrem-master-data/pages__delete_page_dialog_content": "You are about to delete this record.", "@sage/xtrem-master-data/pages__delete_page_dialog_title": "Confirm deletion", "@sage/xtrem-master-data/pages__delete_page_Item_delete_supplier_price_dialog_content": "You are about to delete this line. This action cannot be undone after you save the document.", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title": "Duplicate", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title__duplicate": "Duplicate", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__delivery_mode____objectTypePlural": "Delivery modes", "@sage/xtrem-master-data/pages__delivery_mode____objectTypeSingular": "Delivery mode", "@sage/xtrem-master-data/pages__delivery_mode____title": "Delivery mode", "@sage/xtrem-master-data/pages__delivery_mode__description____title": "Description", "@sage/xtrem-master-data/pages__delivery_mode__id____title": "ID", "@sage/xtrem-master-data/pages__delivery_mode__name____title": "Name", "@sage/xtrem-master-data/pages__delivery_mode__section____title": "General", "@sage/xtrem-master-data/pages__email_exception": "Could not send email: ({{exception}}).", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2Right__title": "Resource", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__employee____objectTypePlural": "Employees", "@sage/xtrem-master-data/pages__employee____objectTypeSingular": "Employee", "@sage/xtrem-master-data/pages__employee____title": "Employee", "@sage/xtrem-master-data/pages__employee__firstName____title": "First name", "@sage/xtrem-master-data/pages__employee__id____title": "ID", "@sage/xtrem-master-data/pages__employee__image____title": "Image", "@sage/xtrem-master-data/pages__employee__isActive____title": "Active", "@sage/xtrem-master-data/pages__employee__lastName____title": "Last name", "@sage/xtrem-master-data/pages__employee__mainSection____title": "General", "@sage/xtrem-master-data/pages__employee__resource____columns__title__resourceGroup__type": "Type", "@sage/xtrem-master-data/pages__employee__resource____lookupDialogTitle": "Select resource", "@sage/xtrem-master-data/pages__employee__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__enter_email_address_and_last_name": "The email was not sent. You need to enter an email address and last name.", "@sage/xtrem-master-data/pages__ghs_classification____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__ghs_classification____objectTypePlural": "GHS classifications", "@sage/xtrem-master-data/pages__ghs_classification____objectTypeSingular": "GHS classification", "@sage/xtrem-master-data/pages__ghs_classification____title": "GHS classification", "@sage/xtrem-master-data/pages__ghs_classification__generalSection____title": "General", "@sage/xtrem-master-data/pages__ghs_classification__ghsClassificationInformationBlock____title": "GHS classification", "@sage/xtrem-master-data/pages__ghs_classification__hazard____title": "Hazards", "@sage/xtrem-master-data/pages__ghs_classification__id____title": "ID", "@sage/xtrem-master-data/pages__ghs_classification__idBlock____title": "ID", "@sage/xtrem-master-data/pages__ghs_classification__name____title": "Name", "@sage/xtrem-master-data/pages__ghs_classification__pictogram____title": "Pictogram", "@sage/xtrem-master-data/pages__ghs_classification__pictogramBlock____title": "Pictogram", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__title": "Efficiency", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__location__title": "Location", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__minCapabilityLevel__title": "Capability level", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__type__title": "Resource type", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__weeklyShift__title": "Weekly shift", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__group_resource____objectTypePlural": "Resource groups", "@sage/xtrem-master-data/pages__group_resource____objectTypeSingular": "Resource group", "@sage/xtrem-master-data/pages__group_resource____title": "Resource group", "@sage/xtrem-master-data/pages__group_resource__addCostCategory____title": "Add cost category", "@sage/xtrem-master-data/pages__group_resource__addReplacementLine____title": "Add", "@sage/xtrem-master-data/pages__group_resource__addResource____helperText": "Add resource", "@sage/xtrem-master-data/pages__group_resource__addResource____title": "Add resource", "@sage/xtrem-master-data/pages__group_resource__blockDetails____title": "Settings", "@sage/xtrem-master-data/pages__group_resource__blockReplacements____title": "Alternate resource groups", "@sage/xtrem-master-data/pages__group_resource__blockResources____title": "Resources", "@sage/xtrem-master-data/pages__group_resource__blockWeekly____title": "Weekly shift details", "@sage/xtrem-master-data/pages__group_resource__costBlock____title": "Cost", "@sage/xtrem-master-data/pages__group_resource__costSection____title": "Cost", "@sage/xtrem-master-data/pages__group_resource__description____title": "Description", "@sage/xtrem-master-data/pages__group_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__group_resource__efficiency____title": "Efficiency", "@sage/xtrem-master-data/pages__group_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__group_resource__id____title": "ID", "@sage/xtrem-master-data/pages__group_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__group_resource__location____lookupDialogTitle": "Select location", "@sage/xtrem-master-data/pages__group_resource__location____title": "Location", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____lookupDialogTitle": "Select capability level", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____title": "Capability level", "@sage/xtrem-master-data/pages__group_resource__name____title": "Name", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title___sortValue": "Priority", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__id": "ID", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__name": "Name", "@sage/xtrem-master-data/pages__group_resource__replacements____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__group_resource__replacements____title": "Replacements", "@sage/xtrem-master-data/pages__group_resource__resourceCapacity____title": "Weekly capacity", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Cost category type", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Mandatory", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costCategory__name": "Cost category", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costUnit__name": "Cost unit", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirect cost", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__runCost": "Run", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__setupCost": "Setup", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____title": "Resource cost categories", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title": "Type", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__weeklyShift__id": "Weekly shift", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__2": "Exclude", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__3": "Delete", "@sage/xtrem-master-data/pages__group_resource__resources____title": "Resources", "@sage/xtrem-master-data/pages__group_resource__section____title": "General", "@sage/xtrem-master-data/pages__group_resource__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__group_resource__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__group_resource__transferInResource____helperText": "Include", "@sage/xtrem-master-data/pages__group_resource__transferInResource____title": "Include", "@sage/xtrem-master-data/pages__group_resource__type____title": "Resource type", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____columns__title__dailyShift": "Daily shift", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____title": "Weekly details", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____columns__title__formattedCapacity": "Capacity", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____lookupDialogTitle": "Select weekly shift", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____title": "Weekly shift", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__incoterm____objectTypePlural": "Incoterms® rules", "@sage/xtrem-master-data/pages__incoterm____objectTypeSingular": "Incoterms® rule", "@sage/xtrem-master-data/pages__incoterm____title": "Incoterms® rule", "@sage/xtrem-master-data/pages__incoterm__description____title": "Description", "@sage/xtrem-master-data/pages__incoterm__id____title": "ID", "@sage/xtrem-master-data/pages__incoterm__name____title": "Name", "@sage/xtrem-master-data/pages__incoterm__section____title": "General", "@sage/xtrem-master-data/pages__indirect_cost_origin____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypePlural": "Indirect cost origins", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypeSingular": "Indirect cost origin", "@sage/xtrem-master-data/pages__indirect_cost_origin____title": "Indirect cost origin", "@sage/xtrem-master-data/pages__indirect_cost_origin__id____title": "ID", "@sage/xtrem-master-data/pages__indirect_cost_origin__mainSection____title": "General", "@sage/xtrem-master-data/pages__indirect_cost_origin__name____title": "Name", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line_4__title": "Calculation method", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypePlural": "Indirect cost sections", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypeSingular": "Indirect cost section", "@sage/xtrem-master-data/pages__indirect_cost_section____title": "Indirect cost section", "@sage/xtrem-master-data/pages__indirect_cost_section__addOrigin____title": "Add", "@sage/xtrem-master-data/pages__indirect_cost_section__calculationMethod____title": "Calculation method", "@sage/xtrem-master-data/pages__indirect_cost_section__id____title": "ID", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__id": "Indirect cost origin", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__name": "Name", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__percentage": "Percentage", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____title": "Details", "@sage/xtrem-master-data/pages__indirect_cost_section__mainSection____title": "General", "@sage/xtrem-master-data/pages__indirect_cost_section__name____title": "Name", "@sage/xtrem-master-data/pages__indirect_cost_section__totalPercentage____title": "Total percentage", "@sage/xtrem-master-data/pages__invalid-email": "The email address is incorrect: {{email}}.", "@sage/xtrem-master-data/pages__item____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__item____navigationPanel__dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__item____navigationPanel__emptyStateClickableText": "Create an item.", "@sage/xtrem-master-data/pages__item____navigationPanel__inlineActions__title__duplicate": "Duplicate", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__basePrice__title": "Base price", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__category__title": "Item category", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__commodityCode__title": "Commodity code", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__title": "Sales currency", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__eanNumber__title": "GTIN-13", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__expirationDate__title": "Expiration date management", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBomRevisionManaged__title": "Bill of material revision", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBought__title": "Purchased", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isManufactured__title": "Manufactured", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isPhantom__title": "Phantom", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isSold__title": "Sold", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isStockManaged__title": "Stock management", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line2Right__title": "GTIN-13", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line3Right__title": "Category", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotManagement__title": "Lot management", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotSequenceNumber__title": "Lot sequence number", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__maximumSalesQuantity__title": "Maximum sales quantity", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumPrice__title": "Minimum price", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumSalesQuantity__title": "Minimum sales quantity", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__purchaseUnit__title": "Purchase unit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__salesUnit__title": "Sales unit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberManagement__title": "Serial number management", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberSequenceNumber__title": "Serial number sequence", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__stockUnit__title": "Stock unit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__titleRight__title": "ID", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__volumeUnit__title": "Volume unit", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__weightUnit__title": "Weight unit", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__3": "In development", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__4": "Not renewed", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__5": "Obsolete", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__6": "Not usable", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__7": "Service", "@sage/xtrem-master-data/pages__item____objectTypePlural": "Items", "@sage/xtrem-master-data/pages__item____objectTypeSingular": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item___id____title": "ID", "@sage/xtrem-master-data/pages__item__addingNewAllergen____lookupDialogTitle": "Select allergen", "@sage/xtrem-master-data/pages__item__addingNewAllergen____title": "Adding new allergen", "@sage/xtrem-master-data/pages__item__addingNewGhsClassification____title": "Select GHS classification", "@sage/xtrem-master-data/pages__item__addItemSite____title": "Add", "@sage/xtrem-master-data/pages__item__addNewAllergen____title": "Select allergen", "@sage/xtrem-master-data/pages__item__addNewClassification____title": "Select GHS classification", "@sage/xtrem-master-data/pages__item__addSupplier____title": "Add", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__id": "ID", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__name": "Name", "@sage/xtrem-master-data/pages__item__allergens____dropdownActions__title__remove": "Remove", "@sage/xtrem-master-data/pages__item__allergens____title": "Allergens", "@sage/xtrem-master-data/pages__item__allergenSection____title": "Allergen", "@sage/xtrem-master-data/pages__item__basePrice____title": "Base price", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____helperText": "Select a sequence number or leave blank to enter manual BOM revision numbers.", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____lookupDialogTitle": "Select a BOM revision sequence number", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____title": "BOM revision sequence number", "@sage/xtrem-master-data/pages__item__capacity____title": "Capacity", "@sage/xtrem-master-data/pages__item__category____columns__title__sequenceNumber__name": "Sequence number", "@sage/xtrem-master-data/pages__item__category____lookupDialogTitle": "Select item category", "@sage/xtrem-master-data/pages__item__category____title": "Category", "@sage/xtrem-master-data/pages__item__commodityCode____title": "Commodity code", "@sage/xtrem-master-data/pages__item__commodityCodeEU____title": "EU commodity code", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__businessEntity__id": "Customer ID", "@sage/xtrem-master-data/pages__item__customers____columns__title__id": "Item-customer ID", "@sage/xtrem-master-data/pages__item__customers____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__item__customers____columns__title__maximumSalesQuantity": "Maximum sales quantity", "@sage/xtrem-master-data/pages__item__customers____columns__title__minimumSalesQuantity": "Minimum sales quantity", "@sage/xtrem-master-data/pages__item__customers____columns__title__name": "Item-customer", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnit__name": "Sales unit", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnitToStockUnitConversion": "Stock unit conversion factor", "@sage/xtrem-master-data/pages__item__customers____dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__item__customers____inlineActions__title__openLinePanel": "Open line panel", "@sage/xtrem-master-data/pages__item__customers____mobileCard__title__title": "Customer", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__item__customers____sidebar__headerDropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__item__customers____title": "Customers", "@sage/xtrem-master-data/pages__item__customerSection____title": "Customers", "@sage/xtrem-master-data/pages__item__customsUnitBlock____title": "Customs", "@sage/xtrem-master-data/pages__item__density____title": "Density", "@sage/xtrem-master-data/pages__item__description____title": "Description", "@sage/xtrem-master-data/pages__item__eanNumber____title": "GTIN-13", "@sage/xtrem-master-data/pages__item__financialBlock____title": "Financial", "@sage/xtrem-master-data/pages__item__financialSection____title": "Financial", "@sage/xtrem-master-data/pages__item__generateId": "Generate ID", "@sage/xtrem-master-data/pages__item__generateNewId": "The category you selected is already linked to a sequence number. Do you want to keep the ID or generate a new one?", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__id": "ID", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__name": "Name", "@sage/xtrem-master-data/pages__item__ghsClassifications____dropdownActions__title__remove": "Remove", "@sage/xtrem-master-data/pages__item__ghsClassifications____title": "GHS classifications", "@sage/xtrem-master-data/pages__item__ghsClassificationSection____title": "GHS classification", "@sage/xtrem-master-data/pages__item__good_stock_block_title": "Stock", "@sage/xtrem-master-data/pages__item__good_stock_unit_title": "Stock unit", "@sage/xtrem-master-data/pages__item__headerSection____title": "Header section", "@sage/xtrem-master-data/pages__item__image____title": "Image", "@sage/xtrem-master-data/pages__item__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__item__inventoryBlock____title": "Stock", "@sage/xtrem-master-data/pages__item__isBomRevisionManaged____title": "Bill of material revision", "@sage/xtrem-master-data/pages__item__isBought____title": "Purchased", "@sage/xtrem-master-data/pages__item__isExpiryManaged____title": "Expiration date management", "@sage/xtrem-master-data/pages__item__isManufactured____title": "Manufactured", "@sage/xtrem-master-data/pages__item__isPhantom____title": "Phantom", "@sage/xtrem-master-data/pages__item__isSold____title": "Sold", "@sage/xtrem-master-data/pages__item__isStockManaged____title": "Stock management", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__title": "Company", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__prodLeadTime": "day(s)", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__purchaseLeadTime": "day(s)", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__batchQuantity": "Batch quantity", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__economicOrderQuantity": "Economic order quantity", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__expectedQuantity": "Expected quantity", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__onOrderQuantity": "Order quantity", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__preferredProcess": "Preferred process", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__prodLeadTime": "Production lead time", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__replenishmentMethod": "Replenishment method", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__requiredQuantity": "Required quantity", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__safetyStock": "Safety stock", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__stdCostValue": "Unit cost", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__valuationMethod": "Cost type", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__edit": "Edit", "@sage/xtrem-master-data/pages__item__itemSites____title": "Item-sites", "@sage/xtrem-master-data/pages__item__keepCurrentId": "Use current ID", "@sage/xtrem-master-data/pages__item__lotManagement____title": "Lot management", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____lookupDialogTitle": "Select lot sequence number", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____title": "Lot sequence number", "@sage/xtrem-master-data/pages__item__mainBlock____title": "Management", "@sage/xtrem-master-data/pages__item__mainSection____title": "Information", "@sage/xtrem-master-data/pages__item__managementSection____title": "Management", "@sage/xtrem-master-data/pages__item__manufacturingBlock____title": "Manufacturing", "@sage/xtrem-master-data/pages__item__maximumSalesQuantity____title": "Maximum quantity", "@sage/xtrem-master-data/pages__item__minimumPrice____title": "Minimum price", "@sage/xtrem-master-data/pages__item__minimumSalesQuantity____title": "Minimum quantity", "@sage/xtrem-master-data/pages__item__name____title": "Name", "@sage/xtrem-master-data/pages__item__positionField1____title": "Position field 1", "@sage/xtrem-master-data/pages__item__positionField2____title": "Position field 2", "@sage/xtrem-master-data/pages__item__positionField3____title": "Position field 3", "@sage/xtrem-master-data/pages__item__priceSection____title": "Supplier prices", "@sage/xtrem-master-data/pages__item__purchase_unit_not_0_decimal_places": "The purchase unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.", "@sage/xtrem-master-data/pages__item__purchaseUnit____lookupDialogTitle": "Select purchase unit", "@sage/xtrem-master-data/pages__item__purchaseUnitBlock____title": "Purchase", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversion____title": "Stock unit conversion factor", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversionDedicated____title": "Dedicated factor", "@sage/xtrem-master-data/pages__item__sales_unit_not_0_decimal_places": "The sales unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.", "@sage/xtrem-master-data/pages__item__salesBlock____title": "Sales", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item__salesCurrency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__item__salesCurrency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title": "Name", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title__2": "ID", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title": "Name", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__2": "ID", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__3": "Company", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__currency__name": "Select currency", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__customer": "Select customer", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__priceReason__name": "Select price reason", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__salesSite": "Select site", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__stockSite": "Select site", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__charge": "Charge", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__businessEntity__id": "Customer ID", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__discount": "Discount", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__endDate": "Valid to", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__fromQuantity": "From quantity", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__price": "Price", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__priceReason__name": "Price reason", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite": "Sales site", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__startDate": "<PERSON>id from", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite": "Stock site", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__toQuantity": "To quantity", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__name": "Unit", "@sage/xtrem-master-data/pages__item__salesPrices____dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__item__salesPrices____inlineActions__title__openLinePanel": "Open line panel", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__title__title": "Customer", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__titleRight__title": "Price reason", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__item__salesPrices____sidebar__headerDropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__item__salesPrices____title": "Customer prices", "@sage/xtrem-master-data/pages__item__salesPricesSection____title": "Sales prices", "@sage/xtrem-master-data/pages__item__salesUnit____lookupDialogTitle": "Select sales unit", "@sage/xtrem-master-data/pages__item__salesUnitBlock____title": "Sales", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversion____title": "Stock unit conversion factor", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversionDedicated____title": "Dedicated factor", "@sage/xtrem-master-data/pages__item__saveItem____title": "Save", "@sage/xtrem-master-data/pages__item__selectId": "Select ID number", "@sage/xtrem-master-data/pages__item__serialNumberManagement____title": "Serial number management", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____lookupDialogTitle": "Select serial number sequence number", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____title": "Serial number sequence", "@sage/xtrem-master-data/pages__item__serialNumberUsage____title": "Serial number usage", "@sage/xtrem-master-data/pages__item__service_stock_block_title": "Unit", "@sage/xtrem-master-data/pages__item__service_stock_unit_title": "Base unit", "@sage/xtrem-master-data/pages__item__siteSection____title": "Sites", "@sage/xtrem-master-data/pages__item__status____title": "Status", "@sage/xtrem-master-data/pages__item__stock_unit_not_0_decimal_places": "The stock unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.", "@sage/xtrem-master-data/pages__item__stockUnit____lookupDialogTitle": "Select stock unit", "@sage/xtrem-master-data/pages__item__storageBlock____title": "Characteristics", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__currency__name__title": "ISO 4217", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title": "Name", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__2": "ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__3": "Company", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title": "Name", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__2": "ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__3": "Tax ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__4": "Country", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__currency__name": "Select currency", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__site": "Select site", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__supplier": "Select supplier", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__unit__name": "Select unit", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidFrom": "<PERSON>id from", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidTo": "Valid to", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__fromQuantity": "From quantity", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__price": "Price", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__priority": "Priority", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__site__businessEntity__id": "Site ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__businessEntity__id": "Supplier ID", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__toQuantity": "To quantity", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__decimalDigits": "Decimal digits", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__id": "Unit", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__name": "Unit of measure", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__symbol": "Symbol", "@sage/xtrem-master-data/pages__item__supplierPrices____dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__item__supplierPrices____inlineActions__title__openLinePanel": "Open line panel", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__title__title": "Supplier", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__titleRight__title": "Type", "@sage/xtrem-master-data/pages__item__supplierPrices____sidebar__headerDropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__item__supplierPrices____title": "Supplier prices", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title": "Name", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__2": "ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__3": "Tax ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__4": "Country", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__purchaseUnitOfMeasure": "Select unit", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__supplier": "Select supplier", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isDefaultItemSupplier": "Default supplier", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__minimumPurchaseQuantity": "Minimum purchase quantity", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseUnitOfMeasure": "Purchase unit", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__businessEntity__id": "Supplier ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemCode": "Item-supplier ID", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemName": "Item-supplier", "@sage/xtrem-master-data/pages__item__suppliers____dropdownActions__title__delete": "Delete", "@sage/xtrem-master-data/pages__item__suppliers____inlineActions__title__openLinePanel": "Open line panel", "@sage/xtrem-master-data/pages__item__suppliers____mobileCard__title__title": "Supplier", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__item__suppliers____sidebar__headerDropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__item__suppliers____title": "Suppliers", "@sage/xtrem-master-data/pages__item__supplierSection____title": "Suppliers", "@sage/xtrem-master-data/pages__item__type____title": "Type", "@sage/xtrem-master-data/pages__item__typeBlock____title": "Item information", "@sage/xtrem-master-data/pages__item__unitBlock____title": "Stock", "@sage/xtrem-master-data/pages__item__unitSection____title": "Units", "@sage/xtrem-master-data/pages__item__volume____title": "Volume", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__type": "Type", "@sage/xtrem-master-data/pages__item__volumeUnit____lookupDialogTitle": "Select volume unit", "@sage/xtrem-master-data/pages__item__weight____title": "Weight", "@sage/xtrem-master-data/pages__item__weightUnit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item__weightUnit____lookupDialogTitle": "Select weight unit", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Sequence number management", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__sequenceNumber__title": "Item ID sequence number", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__type__title": "Type", "@sage/xtrem-master-data/pages__item_category____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item_category____objectTypePlural": "Item categories", "@sage/xtrem-master-data/pages__item_category____objectTypeSingular": "Item category", "@sage/xtrem-master-data/pages__item_category____title": "Item category", "@sage/xtrem-master-data/pages__item_category__declarationsBlock____title": "Declarations", "@sage/xtrem-master-data/pages__item_category__generalBlock____title": "General", "@sage/xtrem-master-data/pages__item_category__generalSection____title": "General", "@sage/xtrem-master-data/pages__item_category__id____title": "ID", "@sage/xtrem-master-data/pages__item_category__isSequenceNumberManagement____title": "Sequence number management", "@sage/xtrem-master-data/pages__item_category__name____title": "Name", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____lookupDialogTitle": "Select Item ID sequence number", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____title": "Item ID sequence number", "@sage/xtrem-master-data/pages__item_category__type____title": "Declarations", "@sage/xtrem-master-data/pages__item_customer__edit____title": "Edit item-customer", "@sage/xtrem-master-data/pages__item_customer_panel____title": "Add item-customer", "@sage/xtrem-master-data/pages__item_customer_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_customer_panel__id____title": "Item-customer ID", "@sage/xtrem-master-data/pages__item_customer_panel__item____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__item_customer_panel__item____columns__title__salesUnitToStockUnitConversionDedicated": "Stock unit conversion factor", "@sage/xtrem-master-data/pages__item_customer_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__item_customer_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_panel__item_is_inactive": "The item is inactive. The status is {{status}}.", "@sage/xtrem-master-data/pages__item_customer_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_customer_panel__maximumSalesQuantity____title": "Maximum sales quantity", "@sage/xtrem-master-data/pages__item_customer_panel__minimumSalesQuantity____title": "Minimum sales quantity", "@sage/xtrem-master-data/pages__item_customer_panel__name____title": "Item-customer name", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____lookupDialogTitle": "Select sales unit", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____title": "Sales unit", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnitToStockUnitConversion____title": "Stock unit conversion factor", "@sage/xtrem-master-data/pages__item_customer_panel__save____title": "OK", "@sage/xtrem-master-data/pages__item_customer_price_panel____title": "Customer price", "@sage/xtrem-master-data/pages__item_customer_price_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____title": "Charge", "@sage/xtrem-master-data/pages__item_customer_price_panel__confirm____title": "Save", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____lookupDialogTitle": "Select customer", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____title": "Customer", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____title": "Discount", "@sage/xtrem-master-data/pages__item_customer_price_panel__edit____title": "Edit sales price", "@sage/xtrem-master-data/pages__item_customer_price_panel__endDate____title": "End date", "@sage/xtrem-master-data/pages__item_customer_price_panel__fromQuantity____title": "From quantity", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__new____title": "New sales price", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_greater_than_100": "Enter a percentage below 100.", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_is_negative": "Enter a positive percentage.", "@sage/xtrem-master-data/pages__item_customer_price_panel__price____title": "Price", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____lookupDialogTitle": "Select price reason", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____title": "Price reason", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____lookupDialogTitle": "Select sales site", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____title": "Sales site", "@sage/xtrem-master-data/pages__item_customer_price_panel__startDate____title": "Start date", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____lookupDialogTitle": "Select stock site", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____title": "Stock site", "@sage/xtrem-master-data/pages__item_customer_price_panel__toQuantity____title": "To quantity", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____lookupDialogTitle": "Select unit", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____title": "Unit of measure", "@sage/xtrem-master-data/pages__item_customer_price_panel__validUnits____title": "Valid units", "@sage/xtrem-master-data/pages__item_customer_price_view_panel____title": "Price list inquiry", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__idBlock____title": "ID", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title___id": "ID", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__customer__id": "Customer", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__endDate": "End date", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__fromQuantity": "From quantity", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__price": "Price", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__priceReason__name": "Price reason", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__id": "Sales site", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__name": "Sales site", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__startDate": "Start date", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__id": "Stock site", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__name": "Stock site", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__toQuantity": "To quantity", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__unit__id": "Unit", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__mainSection____title": "Items", "@sage/xtrem-master-data/pages__item_price_panel____title": "Supplier price", "@sage/xtrem-master-data/pages__item_price_panel___id____title": "ID", "@sage/xtrem-master-data/pages__item_price_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_price_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__item_price_panel__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__dateValid____title": "Validity date", "@sage/xtrem-master-data/pages__item_price_panel__edit____title": "Edit supplier price", "@sage/xtrem-master-data/pages__item_price_panel__fromDate____title": "Start date", "@sage/xtrem-master-data/pages__item_price_panel__fromQuantity____title": "From quantity", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title": "Description", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__2": "Decimal digits", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__3": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__stockUnit__description": "Description", "@sage/xtrem-master-data/pages__item_price_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__item_price_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__new____title": "New supplier price", "@sage/xtrem-master-data/pages__item_price_panel__price____title": "Price", "@sage/xtrem-master-data/pages__item_price_panel__priority____title": "Priority", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title": "Name", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__2": "ID", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__3": "ID", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__item_price_panel__site____title": "Site", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title": "Name", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__2": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__3": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title": "Name", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__2": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__3": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__4": "Decimal digits", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__country__name": "Country", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__currency__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-master-data/pages__item_price_panel__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-master-data/pages__item_price_panel__supplier____title": "Supplier", "@sage/xtrem-master-data/pages__item_price_panel__toDate____title": "End date", "@sage/xtrem-master-data/pages__item_price_panel__toQuantity____title": "To quantity", "@sage/xtrem-master-data/pages__item_price_panel__type____title": "Type", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__decimalDigits": "Decimal digits", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__id": "ID", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__name": "Name", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__symbol": "Symbol", "@sage/xtrem-master-data/pages__item_price_panel__unit____lookupDialogTitle": "Select unit", "@sage/xtrem-master-data/pages__item_price_panel__unit____title": "Unit of measure", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__averageDailyConsumption__title": "Average daily consumption", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__batchQuantity__title": "Batch quantity", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__economicOrderQuantity__title": "Economic order quantity", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__expectedQuantity__title": "Expected", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemCategory__title": "Item category", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemDescription__title": "Item description", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemId__title": "Item ID", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__preferredProcess__title": "Preferred process", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__postfix": "days", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__title": "Purchase lead time", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__reorderPoint__title": "Reorder point", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__replenishmentMethod__title": "Replenishment method", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__requiredQuantity__title": "Required", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__safetyStock__title": "Safety stock", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__stdCostValue__title": "Standard cost", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__title__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__valuationMethod__title": "Valuation method", "@sage/xtrem-master-data/pages__item_site____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item_site____objectTypePlural": "Item-sites", "@sage/xtrem-master-data/pages__item_site____objectTypeSingular": "Item-site", "@sage/xtrem-master-data/pages__item_site____title": "Item-site", "@sage/xtrem-master-data/pages__item_site___id____title": "ID", "@sage/xtrem-master-data/pages__item_site___valuationMethod____title": "Valuation method", "@sage/xtrem-master-data/pages__item_site__addItemSiteCost____title": "Add", "@sage/xtrem-master-data/pages__item_site__addSupplier____title": "Add", "@sage/xtrem-master-data/pages__item_site__averageDailyConsumption____title": "Average daily consumption", "@sage/xtrem-master-data/pages__item_site__batchQuantity____title": "Batch quantity", "@sage/xtrem-master-data/pages__item_site__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____lookupDialogTitle": "Select default location", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____title": "Released item", "@sage/xtrem-master-data/pages__item_site__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_site__costBlock____title": "Cost", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title": "Type", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title__2": "Mandatory", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__costCategory__name": "Cost category", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__forQuantity": "Quantity", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__fromDate": "Start date", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__isCalculated": "Calculated", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__totalCost": "Total cost", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__unitCost": "Unit cost", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__2": "Delete", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__3": "Add period", "@sage/xtrem-master-data/pages__item_site__costs____title": "Costs", "@sage/xtrem-master-data/pages__item_site__costsBlock____title": "Costs", "@sage/xtrem-master-data/pages__item_site__costSection____title": "Costs", "@sage/xtrem-master-data/pages__item_site__counting-in-progress": "Counting in progress", "@sage/xtrem-master-data/pages__item_site__countingInProgress____title": "Counting in progress", "@sage/xtrem-master-data/pages__item_site__countingInProgressMention____title": "Counting in progress mention", "@sage/xtrem-master-data/pages__item_site__economicOrderQuantity____title": "Economic order quantity", "@sage/xtrem-master-data/pages__item_site__edit____title": "Edit item-site", "@sage/xtrem-master-data/pages__item_site__expectedQuantity____title": "Expected quantity", "@sage/xtrem-master-data/pages__item_site__id____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____lookupDialogTitle": "Select default location", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____title": "Inbound", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____lookupDialogTitle": "Select indirect cost section", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____title": "Indirect cost section", "@sage/xtrem-master-data/pages__item_site__isOrderToOrder____title": "Order-to-order", "@sage/xtrem-master-data/pages__item_site__item____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__item_site__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__item_site__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__locationBlock____title": "Default location", "@sage/xtrem-master-data/pages__item_site__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_site__new____title": "New item-site", "@sage/xtrem-master-data/pages__item_site__onOrderQuantity____title": "Quantity change", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____lookupDialogTitle": "Select default location", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____title": "Outbound", "@sage/xtrem-master-data/pages__item_site__preferredProcess____title": "Preferred process", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____postfix": "day(s)", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____title": "Production lead time", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____postfix": "day(s)", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____title": "Purchase lead time", "@sage/xtrem-master-data/pages__item_site__qualityControlBlock____title": "Default quality control status", "@sage/xtrem-master-data/pages__item_site__reorderPoint____title": "Reorder point", "@sage/xtrem-master-data/pages__item_site__replenishmentBlock____title": "Replenishment", "@sage/xtrem-master-data/pages__item_site__replenishmentMethod____title": "Replenishment method", "@sage/xtrem-master-data/pages__item_site__replenishmentSection____title": "Replenishment", "@sage/xtrem-master-data/pages__item_site__requiredQuantity____title": "Required quantity", "@sage/xtrem-master-data/pages__item_site__safetyStock____title": "Safety stock", "@sage/xtrem-master-data/pages__item_site__saveItemSite____title": "Save", "@sage/xtrem-master-data/pages__item_site__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__item_site__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__item_site__stdCostValue____title": "Standard cost", "@sage/xtrem-master-data/pages__item_site__stockBlock____title": "Stock", "@sage/xtrem-master-data/pages__item_site__stockRulesSection____title": "Stock rules", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__isDefaultItemSupplier": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__minimumPurchaseOrderQuantity": "Minimum purchase quantity", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseUnit__name": "Unit", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__id": "Supplier ID", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__name": "Supplier name", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title__2": "Delete", "@sage/xtrem-master-data/pages__item_site__suppliers____title": "Suppliers", "@sage/xtrem-master-data/pages__item_site__suppliersBlock____title": "Suppliers", "@sage/xtrem-master-data/pages__item_site__suppliersSection____title": "Suppliers", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__listItem__line3__title": "Start date", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item_site_cost____objectTypePlural": "Item-site costs", "@sage/xtrem-master-data/pages__item_site_cost____objectTypeSingular": "Item-site cost", "@sage/xtrem-master-data/pages__item_site_cost____title": "Item-site cost", "@sage/xtrem-master-data/pages__item_site_cost___id____title": "ID", "@sage/xtrem-master-data/pages__item_site_cost__calculate____title": "Calculate", "@sage/xtrem-master-data/pages__item_site_cost__chartBlock____title": "Cost chart", "@sage/xtrem-master-data/pages__item_site_cost__costBlock____title": "Cost detail", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__isMandatory": "Mandatory", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____lookupDialogTitle": "Select cost category", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____title": "Cost category", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriod____title": "New period", "@sage/xtrem-master-data/pages__item_site_cost__forQuantity____title": "Quantity", "@sage/xtrem-master-data/pages__item_site_cost__fromDate____title": "Start date", "@sage/xtrem-master-data/pages__item_site_cost__isCalculated____title": "Calculated", "@sage/xtrem-master-data/pages__item_site_cost__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__name": "Item name", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__status": "Item status", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__type": "Item type", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site": "Site name", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site__id": "Site ID", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____lookupDialogTitle": "Select item-site", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____title": "Item-site", "@sage/xtrem-master-data/pages__item_site_cost__laborCost____title": "Labor", "@sage/xtrem-master-data/pages__item_site_cost__machineCost____title": "Machine", "@sage/xtrem-master-data/pages__item_site_cost__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_site_cost__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site_cost__site____title": "Site", "@sage/xtrem-master-data/pages__item_site_cost__toDate____title": "End date", "@sage/xtrem-master-data/pages__item_site_cost__toolCost____title": "Tool", "@sage/xtrem-master-data/pages__item_site_cost__totalCost____title": "Total cost", "@sage/xtrem-master-data/pages__item_site_cost__unitCost____title": "Unit cost", "@sage/xtrem-master-data/pages__item_site_cost__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_cost_panel____subtitle": "Item-site cost panel", "@sage/xtrem-master-data/pages__item_site_cost_panel____title": "Item-site cost panel", "@sage/xtrem-master-data/pages__item_site_cost_panel___id____title": "ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_site_cost_panel__chartBlock____title": "Cost chart", "@sage/xtrem-master-data/pages__item_site_cost_panel__costBlock____title": "Cost detail", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__costCategoryType": "Type", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__isMandatory": "Mandatory", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____lookupDialogTitle": "Select cost category", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____title": "Cost category", "@sage/xtrem-master-data/pages__item_site_cost_panel__costChart____title": "Cost chart", "@sage/xtrem-master-data/pages__item_site_cost_panel__forQuantity____title": "Quantity", "@sage/xtrem-master-data/pages__item_site_cost_panel__fromDate____title": "Start date", "@sage/xtrem-master-data/pages__item_site_cost_panel__invalid-from-date": "The start date cannot be earlier than today.", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__site__id__title": "Symbol", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__name": "Item name", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__id": "Site ID", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__name": "Site name", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____lookupDialogTitle": "Select item-site", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____title": "Item-site", "@sage/xtrem-master-data/pages__item_site_cost_panel__laborCost____title": "Labor", "@sage/xtrem-master-data/pages__item_site_cost_panel__machineCost____title": "Machine", "@sage/xtrem-master-data/pages__item_site_cost_panel__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site_cost_panel__save____title": "Save", "@sage/xtrem-master-data/pages__item_site_cost_panel__toDate____title": "End date", "@sage/xtrem-master-data/pages__item_site_cost_panel__toolCost____title": "Tool", "@sage/xtrem-master-data/pages__item_site_cost_panel__totalCost____title": "Total cost", "@sage/xtrem-master-data/pages__item_site_cost_panel__unitCost____title": "Unit cost", "@sage/xtrem-master-data/pages__item_site_cost_panel__version____title": "Version", "@sage/xtrem-master-data/pages__item_site_supplier____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypePlural": "Item-site-suppliers", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypeSingular": "Item-site-supplier", "@sage/xtrem-master-data/pages__item_site_supplier____title": "Item-site-supplier", "@sage/xtrem-master-data/pages__item_site_supplier___id____title": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_site_supplier__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_site_supplier__deleteItemSiteSupplier____title": "Delete", "@sage/xtrem-master-data/pages__item_site_supplier__identificationBlock____title": "Identification", "@sage/xtrem-master-data/pages__item_site_supplier__isDefaultItemSupplier____title": "Default item-site-supplier", "@sage/xtrem-master-data/pages__item_site_supplier__itemDetailsBlock____title": "Item details", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__name": "Item name", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__status": "Item status", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__type": "Item type", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site": "Site name", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site__id": "Site ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____lookupDialogTitle": "Select item-site", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____title": "Item-site", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__country__name": "Country", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____title": "Supplier", "@sage/xtrem-master-data/pages__item_site_supplier__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_site_supplier__minimumPurchaseOrderQuantity____title": "Minimum purchase quantity", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____postfix": "days", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____title": "Purchase lead time", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseUnit____lookupDialogTitle": "Select purchase unit", "@sage/xtrem-master-data/pages__item_site_supplier__saveItemSiteSupplier____title": "Save", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__taxIdNumber": "Tax ID", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-master-data/pages__item_supplier__item_cost_edit____title": "Edit item-site cost", "@sage/xtrem-master-data/pages__item_supplier__item_cost_new____title": "New item-site cost", "@sage/xtrem-master-data/pages__item_supplier__item_edit____title": "Edit item-site-supplier", "@sage/xtrem-master-data/pages__item_supplier__item_new____title": "New item-site-supplier", "@sage/xtrem-master-data/pages__item_supplier_price_panel____title": "Supplier price", "@sage/xtrem-master-data/pages__item_supplier_price_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_supplier_price_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____columns__title__id": "ISO 4217", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValid____title": "Date valid", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidFrom____title": "From date", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidTo____title": "To date", "@sage/xtrem-master-data/pages__item_supplier_price_panel__fromQuantity____title": "From quantity", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__price____title": "Price", "@sage/xtrem-master-data/pages__item_supplier_price_panel__priority____title": "Priority", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__country__name": "Country", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-master-data/pages__item_supplier_price_panel__toQuantity____title": "To quantity", "@sage/xtrem-master-data/pages__item_supplier_price_panel__type____title": "Type", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____lookupDialogTitle": "Select unit", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____title": "Unit of measure", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel____title": "Price list inquiry", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel___id____title": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__idBlock____title": "ID", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidFrom": "Validity start date", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidTo": "Validity end date", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__fromQuantity": "From quantity", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__site__id": "Site", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__toQuantity": "To quantity", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__mainSection____title": "Items", "@sage/xtrem-master-data/pages__item-customer_panel__conversion_negative_value": "The sales to stock conversion must not be less than or equal to 0.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_less_than_minimum_value": "The maximum sales quantity must not be less than the minimum sales quantity.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_negative_value": "The maximum sales quantity must not be less than 0.", "@sage/xtrem-master-data/pages__item-customer_panel__minimum_quantity_negative_value": "The minimum sales quantity must not be less than 0.", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_buy_to_order": "Buy-to-order", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_make_to_order": "Make-to-order", "@sage/xtrem-master-data/pages__item-site__preferred_process_cannot_be": "A stock item must be manufactured, purchased, or both.", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeFrom__title": "Active from", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeTo__title": "Active to", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__resourceGroup__title": "Resource group", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__weeklyShift__title": "Weekly shift", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__labor_resource____objectTypePlural": "Labor resources", "@sage/xtrem-master-data/pages__labor_resource____objectTypeSingular": "Labor resource", "@sage/xtrem-master-data/pages__labor_resource____title": "Labor resource", "@sage/xtrem-master-data/pages__labor_resource___id____title": "ID", "@sage/xtrem-master-data/pages__labor_resource__activeFrom____title": "Active from", "@sage/xtrem-master-data/pages__labor_resource__activeTo____title": "Active to", "@sage/xtrem-master-data/pages__labor_resource__addCapability____title": "Add", "@sage/xtrem-master-data/pages__labor_resource__addCostCategory____title": "Add cost category", "@sage/xtrem-master-data/pages__labor_resource__blockCapabilities____title": "Capabilities", "@sage/xtrem-master-data/pages__labor_resource__blockDetails____title": "Settings", "@sage/xtrem-master-data/pages__labor_resource__blockWeekly____title": "Weekly shift details", "@sage/xtrem-master-data/pages__labor_resource__cancelSidePanel____title": "Cancel", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__capabilityLevel__name": "Capability level", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateEndValid": "End date", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateStartValid": "Start date", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__machine__name": "Machine", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__service__name": "Service", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__tool__name": "Tool", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title__2": "Edit", "@sage/xtrem-master-data/pages__labor_resource__capabilities____title": "Capabilities", "@sage/xtrem-master-data/pages__labor_resource__capabilitiesSection____title": "Capabilities", "@sage/xtrem-master-data/pages__labor_resource__costBlock____title": "Cost", "@sage/xtrem-master-data/pages__labor_resource__costSection____title": "Cost", "@sage/xtrem-master-data/pages__labor_resource__description____title": "Description", "@sage/xtrem-master-data/pages__labor_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__labor_resource__efficiency____title": "Efficiency", "@sage/xtrem-master-data/pages__labor_resource__id____title": "ID", "@sage/xtrem-master-data/pages__labor_resource__isActive____title": "Active", "@sage/xtrem-master-data/pages__labor_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__labor_resource__location____lookupDialogTitle": "Select location", "@sage/xtrem-master-data/pages__labor_resource__location____title": "Location", "@sage/xtrem-master-data/pages__labor_resource__name____title": "Name", "@sage/xtrem-master-data/pages__labor_resource__resourceCapacity____title": "Weekly capacity", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Cost category type", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Mandatory", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costCategory__name": "Cost category", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costUnit__name": "Cost unit", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirect cost", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__runCost": "Run", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__setupCost": "Setup", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____title": "Resource cost categories", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____lookupDialogTitle": "Select resource group", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____title": "Resource group", "@sage/xtrem-master-data/pages__labor_resource__resourceImage____title": "Image", "@sage/xtrem-master-data/pages__labor_resource__saveSidePanel____title": "Save", "@sage/xtrem-master-data/pages__labor_resource__section____title": "General", "@sage/xtrem-master-data/pages__labor_resource__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__labor_resource__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__capacity": "Capacity", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__dailyShift": "Daily shift", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__day": "Day", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift1": "Shift 1", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift2": "Shift 2", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift3": "Shift 3", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift4": "Shift 4", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift5": "Shift 5", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____title": "Weekly details", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____columns__title__formattedCapacity": "Capacity", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____lookupDialogTitle": "Select weekly shift", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____title": "Weekly shift", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__postfix": "kg", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__title": "Used capacity", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isInternalIcon__title": "Internal", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isSingleLot__title": "Single lot", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__line2__title": "Container", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__location__title": "Location", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__sSingleItem__title": "Single item", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__titleRight__title": "Type", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__license_plate_number____objectTypePlural": "License plate numbers", "@sage/xtrem-master-data/pages__license_plate_number____objectTypeSingular": "License plate number", "@sage/xtrem-master-data/pages__license_plate_number____title": "License plate number", "@sage/xtrem-master-data/pages__license_plate_number___id____title": "ID", "@sage/xtrem-master-data/pages__license_plate_number__consumedCapacity____title": "Used capacity", "@sage/xtrem-master-data/pages__license_plate_number__container____columns__title__isInternal": "Internal", "@sage/xtrem-master-data/pages__license_plate_number__container____lookupDialogTitle": "Select container", "@sage/xtrem-master-data/pages__license_plate_number__container____title": "Container", "@sage/xtrem-master-data/pages__license_plate_number__containerType____title": "Container type", "@sage/xtrem-master-data/pages__license_plate_number__isInternal____title": "Internal", "@sage/xtrem-master-data/pages__license_plate_number__isSingleItem____title": "Single item", "@sage/xtrem-master-data/pages__license_plate_number__isSingleLot____title": "Single lot", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__license_plate_number__location____lookupDialogTitle": "Select location", "@sage/xtrem-master-data/pages__license_plate_number__location____title": "Location", "@sage/xtrem-master-data/pages__license_plate_number__locationSite____title": "Site", "@sage/xtrem-master-data/pages__license_plate_number__locationType____title": "Location type", "@sage/xtrem-master-data/pages__license_plate_number__mainSection____title": "General", "@sage/xtrem-master-data/pages__license_plate_number__mass_update__success": "License plate numbers updated.", "@sage/xtrem-master-data/pages__license_plate_number__number____title": "Number", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__license_plate_number__owner____lookupDialogTitle": "Select owner", "@sage/xtrem-master-data/pages__license_plate_number__owner____title": "Owner", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation____title": "LPN mass creation", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__cancelLicensePlateNumbers____title": "Cancel", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleItem": "Single item", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleLot": "Single lot", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__sequenceNumber__id": "Sequence number", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____lookupDialogTitle": "Select container", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____title": "Container", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleItem____title": "Single item", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleLot____title": "Single lot", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__title": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleItem": "Single item", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleLot": "Single lot", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__locationType__name": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__name": "Location", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__site__name": "Site", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____title": "License plate numbers", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbersBlock____title": "License plate numbers", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____columns__title__locationType__name": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____lookupDialogTitle": "Select location", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____title": "Location", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationSite____title": "Site", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationType____title": "Type", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__mainSection____title": "General", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__numberToCreate____title": "LPNs to generate", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__saveLicensePlateNumbers____title": "Create", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__searchButton____title": "Generate", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__dangerousGoodAllowed__title": "Dangerous goods allowed", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationType__title": "Type", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationZone__title": "Zone", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__location____objectTypePlural": "Locations", "@sage/xtrem-master-data/pages__location____objectTypeSingular": "Location", "@sage/xtrem-master-data/pages__location____title": "Location", "@sage/xtrem-master-data/pages__location__dangerousGoodAllowed____title": "Dangerous goods allowed", "@sage/xtrem-master-data/pages__location__id____title": "ID", "@sage/xtrem-master-data/pages__location__locationType____columns__title__locationCategory": "Category", "@sage/xtrem-master-data/pages__location__locationType____lookupDialogTitle": "Select location type", "@sage/xtrem-master-data/pages__location__locationType____title": "Type", "@sage/xtrem-master-data/pages__location__locationZone____columns__title__zoneType": "Type", "@sage/xtrem-master-data/pages__location__locationZone____lookupDialogTitle": "Select storage zone", "@sage/xtrem-master-data/pages__location__locationZone____title": "Zone", "@sage/xtrem-master-data/pages__location__name____title": "Name", "@sage/xtrem-master-data/pages__location__section____title": "General", "@sage/xtrem-master-data/pages__location__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__location__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__location_mass_creation____title": "Location mass creation", "@sage/xtrem-master-data/pages__location_mass_creation__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__location_mass_creation__createLocations____title": "Create", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationType__name__title": "Category", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationZone__name__title": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationType__name": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationZone__name": "Zone", "@sage/xtrem-master-data/pages__location_mass_creation__locations____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__location_mass_creation__locationsBlock____title": "Locations", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__lastSequenceUsed": "Last sequence used", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocationsRemaining": "Remaining allocations", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberOfCombinations": "Total allocations", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____lookupDialogTitle": "Select location sequence number", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____title": "Location sequence number", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____columns__title__locationCategory": "Category", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____lookupDialogTitle": "Select location type", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____title": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____columns__title__zoneType": "Type", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____lookupDialogTitle": "Select storage zone", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____title": "Zone", "@sage/xtrem-master-data/pages__location_mass_creation__mainSection____title": "General", "@sage/xtrem-master-data/pages__location_mass_creation__requiredCombinations____title": "Required combinations", "@sage/xtrem-master-data/pages__location_mass_creation__searchButton____title": "Search", "@sage/xtrem-master-data/pages__location_mass_creation__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__location_mass_creation__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__location_mass_creation__site____placeholder": "Select ...", "@sage/xtrem-master-data/pages__location_mass_creation__site____title": "Site", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__componentLength__title": "Sequence length", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberOfCombinations__title": "Number of locations", "@sage/xtrem-master-data/pages__location_sequence____objectTypePlural": "Location sequence number", "@sage/xtrem-master-data/pages__location_sequence____objectTypeSingular": "Location sequence number", "@sage/xtrem-master-data/pages__location_sequence____title": "Location sequence number", "@sage/xtrem-master-data/pages__location_sequence__addComponent____title": "Add", "@sage/xtrem-master-data/pages__location_sequence__capital_letters_only": "An alphabetical sequence number component can only contain capital letters.", "@sage/xtrem-master-data/pages__location_sequence__componentLength____title": "Sequence length", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__endValue": "End value", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__startValue": "Start value", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__location_sequence__components____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__location_sequence__components____title": "Components", "@sage/xtrem-master-data/pages__location_sequence__componentsBlock____title": "Components", "@sage/xtrem-master-data/pages__location_sequence__constant_invalid_length": "Check the length of the constant value.", "@sage/xtrem-master-data/pages__location_sequence__digits_only": "A numeric sequence number component can only contain numbers.", "@sage/xtrem-master-data/pages__location_sequence__id____title": "ID", "@sage/xtrem-master-data/pages__location_sequence__invalid_range": "The start value cannot be after the end value.", "@sage/xtrem-master-data/pages__location_sequence__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__location_sequence__mainSection____title": "General", "@sage/xtrem-master-data/pages__location_sequence__name____title": "Name", "@sage/xtrem-master-data/pages__location_sequence__numberOfCombinations____title": "Number of locations", "@sage/xtrem-master-data/pages__location_type____navigationPanel__listItem__locationCategory__title": "Category", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__2": "Internal", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__3": "Dock", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__4": "Customer", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__5": "Subcontracting", "@sage/xtrem-master-data/pages__location_type____objectTypePlural": "Location types", "@sage/xtrem-master-data/pages__location_type____objectTypeSingular": "Location type", "@sage/xtrem-master-data/pages__location_type____title": "Location type", "@sage/xtrem-master-data/pages__location_type__description____title": "Description", "@sage/xtrem-master-data/pages__location_type__id____title": "ID", "@sage/xtrem-master-data/pages__location_type__locationCategory____title": "Category", "@sage/xtrem-master-data/pages__location_type__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__location_type__mainSection____title": "General", "@sage/xtrem-master-data/pages__location_type__name____title": "Name", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__zoneType__title": "Type", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__2": "Frozen", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__3": "Sensitive", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__4": "Secured", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__5": "Restricted", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__6": "Hazard", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__7": "Chemical", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__8": "Magnetic", "@sage/xtrem-master-data/pages__location_zone____objectTypePlural": "Storage zones", "@sage/xtrem-master-data/pages__location_zone____objectTypeSingular": "Storage zone", "@sage/xtrem-master-data/pages__location_zone____title": "Storage zone", "@sage/xtrem-master-data/pages__location_zone__id____title": "ID", "@sage/xtrem-master-data/pages__location_zone__name____title": "Name", "@sage/xtrem-master-data/pages__location_zone__section____title": "General", "@sage/xtrem-master-data/pages__location_zone__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__location_zone__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__location_zone__zoneType____title": "Type", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeFrom__title": "Active from", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeTo__title": "Active to", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__title": "Efficiency", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__location__title": "Location", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__minCapabilityLevel__title": "Capability level", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__model__title": "Machine model", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__resourceGroup__title": "Resource group", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__serialNumber__title": "Machine serial number", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__weeklyShift__title": "Weekly shift", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__machine_resource____objectTypePlural": "Machine resources", "@sage/xtrem-master-data/pages__machine_resource____objectTypeSingular": "Machine resource", "@sage/xtrem-master-data/pages__machine_resource____title": "Machine resource", "@sage/xtrem-master-data/pages__machine_resource___id____title": "ID", "@sage/xtrem-master-data/pages__machine_resource__activeFrom____title": "Active from", "@sage/xtrem-master-data/pages__machine_resource__activeTo____title": "Active to", "@sage/xtrem-master-data/pages__machine_resource__addCostCategory____title": "Add cost category", "@sage/xtrem-master-data/pages__machine_resource__blockContract____title": "Contract", "@sage/xtrem-master-data/pages__machine_resource__blockDetails____title": "Settings", "@sage/xtrem-master-data/pages__machine_resource__blockWeekly____title": "Weekly shift details", "@sage/xtrem-master-data/pages__machine_resource__cancelSidePanel____title": "Cancel", "@sage/xtrem-master-data/pages__machine_resource__contractId____title": "ID", "@sage/xtrem-master-data/pages__machine_resource__contractName____title": "Name", "@sage/xtrem-master-data/pages__machine_resource__contractSection____title": "Contract", "@sage/xtrem-master-data/pages__machine_resource__costBlock____title": "Cost", "@sage/xtrem-master-data/pages__machine_resource__costSection____title": "Cost", "@sage/xtrem-master-data/pages__machine_resource__description____title": "Description", "@sage/xtrem-master-data/pages__machine_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__machine_resource__efficiency____title": "Efficiency", "@sage/xtrem-master-data/pages__machine_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__machine_resource__id____title": "ID", "@sage/xtrem-master-data/pages__machine_resource__isActive____title": "Active", "@sage/xtrem-master-data/pages__machine_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__machine_resource__location____lookupDialogTitle": "Select location", "@sage/xtrem-master-data/pages__machine_resource__location____title": "Location", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____lookupDialogTitle": "Select capability level", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____title": "Capability level", "@sage/xtrem-master-data/pages__machine_resource__model____title": "Machine model", "@sage/xtrem-master-data/pages__machine_resource__name____title": "Name", "@sage/xtrem-master-data/pages__machine_resource__resourceCapacity____title": "Weekly capacity", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Cost category type", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Mandatory", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costCategory__name": "Cost category", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costUnit__name": "Cost unit", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirect cost", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__runCost": "Run", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__setupCost": "Setup", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____title": "Resource cost categories", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____lookupDialogTitle": "Select resource group", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____title": "Resource group", "@sage/xtrem-master-data/pages__machine_resource__resourceImage____title": "Image", "@sage/xtrem-master-data/pages__machine_resource__section____title": "General", "@sage/xtrem-master-data/pages__machine_resource__serialNumber____title": "Machine serial number", "@sage/xtrem-master-data/pages__machine_resource__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__machine_resource__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__country": "Country", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__id": "ID", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__name": "Name", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__taxIdNumber": "Tax ID", "@sage/xtrem-master-data/pages__machine_resource__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-master-data/pages__machine_resource__weeklyDetails____columns__title__dailyShift": "Daily shift", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____columns__title__formattedCapacity": "Capacity", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____lookupDialogTitle": "Select weekly shift", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____title": "Weekly shift", "@sage/xtrem-master-data/pages__master_data__cancel": "Cancel", "@sage/xtrem-master-data/pages__master_data__confirm": "Confirm", "@sage/xtrem-master-data/pages__master_data__update": "Update", "@sage/xtrem-master-data/pages__master_data__warning-dialog-content": "You are about to update the sequence number value.", "@sage/xtrem-master-data/pages__multiple__location__creation__success_multi": "{{num}} locations created", "@sage/xtrem-master-data/pages__multiple_location_creation__success": "{{num}} location created", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__listItem__line3__title": "Business entity type", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__payment_term____objectTypePlural": "Payment terms", "@sage/xtrem-master-data/pages__payment_term____objectTypeSingular": "Payment term", "@sage/xtrem-master-data/pages__payment_term____title": "Payment term", "@sage/xtrem-master-data/pages__payment_term___id____title": "ID", "@sage/xtrem-master-data/pages__payment_term__amount": "Amount", "@sage/xtrem-master-data/pages__payment_term__blockDiscount____title": "Discount", "@sage/xtrem-master-data/pages__payment_term__blockDue____title": "Due date", "@sage/xtrem-master-data/pages__payment_term__blockPenalty____title": "Penalty", "@sage/xtrem-master-data/pages__payment_term__businessEntityType____title": "Business entity type", "@sage/xtrem-master-data/pages__payment_term__days____title": "Number of days", "@sage/xtrem-master-data/pages__payment_term__description____title": "Description", "@sage/xtrem-master-data/pages__payment_term__discountDate____title": "Day", "@sage/xtrem-master-data/pages__payment_term__discountFrom____title": "From", "@sage/xtrem-master-data/pages__payment_term__discountType____title": "Type", "@sage/xtrem-master-data/pages__payment_term__dueDateType____title": "Type", "@sage/xtrem-master-data/pages__payment_term__id____title": "ID", "@sage/xtrem-master-data/pages__payment_term__name____title": "Name", "@sage/xtrem-master-data/pages__payment_term__penaltyType____title": "Type", "@sage/xtrem-master-data/pages__payment_term__percentage": "Percentage", "@sage/xtrem-master-data/pages__payment_term__section____title": "General", "@sage/xtrem-master-data/pages__reason_code____objectTypePlural": "Reason codes", "@sage/xtrem-master-data/pages__reason_code____objectTypeSingular": "Reason code", "@sage/xtrem-master-data/pages__reason_code____title": "Reason code", "@sage/xtrem-master-data/pages__reason_code__id____title": "ID", "@sage/xtrem-master-data/pages__reason_code__isActive____title": "Active", "@sage/xtrem-master-data/pages__reason_code__mainSection____title": "General", "@sage/xtrem-master-data/pages__reason_code__name____title": "Name", "@sage/xtrem-master-data/pages__request_approval_dialog____title": "Approval request", "@sage/xtrem-master-data/pages__request_approval_dialog__approverSelectionBlock____title": "Select approver", "@sage/xtrem-master-data/pages__request_approval_dialog__default_approver": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__email_cannot_be_sent": "The request for approval cannot be sent by email.", "@sage/xtrem-master-data/pages__request_approval_dialog__email_exception_request": "Could not send request email. ({{exception}})", "@sage/xtrem-master-data/pages__request_approval_dialog__email_sent_to_approval": "Email sent to {{value}} for approval.", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____helperText": "A request for approval email will be sent to this address.", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____title": "To", "@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email": "Invalid email address: {{value}}", "@sage/xtrem-master-data/pages__request_approval_dialog__requestApprovalSection____title": "Request for approval", "@sage/xtrem-master-data/pages__request_approval_dialog__selectApprover____title": "Select approver", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__email": "Email", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__firstName": "Name", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__lastName": "Last name", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____lookupDialogTitle": "Select selected user", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____title": "Selected user", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_content": "You are about to send the approval email.", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_title": "Confirm send approval email", "@sage/xtrem-master-data/pages__request_approval_dialog__sendApprovalRequestButton____title": "Send", "@sage/xtrem-master-data/pages__request_approval_dialog__substitute_approver": "Substitute", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__email": "Email", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__firstName": "Name", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__lastName": "Last name", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__type": "Approver", "@sage/xtrem-master-data/pages__request_approval_dialog__users____title": "Users", "@sage/xtrem-master-data/pages__resource_functions__duration_in_hours_and_minutes": "{{hours}} hours {{minutes}} mins", "@sage/xtrem-master-data/pages__resource_group_transfer____title": "Resource group transfer", "@sage/xtrem-master-data/pages__resource_group_transfer__block____title": "ID", "@sage/xtrem-master-data/pages__resource_group_transfer__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__resource_group_transfer__confirm____title": "Save", "@sage/xtrem-master-data/pages__resource_group_transfer__mainSection____title": "General", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____columns__title__weeklyShift__id": "Weekly shift", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____lookupDialogTitle": "Select resource", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____title": "Resource", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____columns__title__weeklyShift__id": "Weekly shift", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____lookupDialogTitle": "Select resource group", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____title": "Resource group", "@sage/xtrem-master-data/pages__resource_group_transfer__type____title": "Resource group type", "@sage/xtrem-master-data/pages__select_sold_to_contact_button_text": "Select sold-to customer contact", "@sage/xtrem-master-data/pages__send_button_text": "Send", "@sage/xtrem-master-data/pages__send_email_panel____title": "Send sales quote", "@sage/xtrem-master-data/pages__send_email_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____helperText": "An email will be sent to this address.", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____title": "Email", "@sage/xtrem-master-data/pages__send_email_panel__emailFirstName____title": "First name", "@sage/xtrem-master-data/pages__send_email_panel__emailLastName____title": "Last name", "@sage/xtrem-master-data/pages__send_email_panel__emailTitles____title": "Title", "@sage/xtrem-master-data/pages__send_email_panel__selectSoldToContact____title": "Select sold-to customer contact", "@sage/xtrem-master-data/pages__send_email_panel__sendEmailBlock____title": "To", "@sage/xtrem-master-data/pages__send_email_panel__sendSalesOrderButton____title": "Send order", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_4__title": "Definition level", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_5__title": "Reset frequency", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line6__title": "Type", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line7__title": "Chronological control", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__sequence_number____objectTypePlural": "Sequence numbers", "@sage/xtrem-master-data/pages__sequence_number____objectTypeSingular": "Sequence number", "@sage/xtrem-master-data/pages__sequence_number____title": "Sequence number", "@sage/xtrem-master-data/pages__sequence_number__addComponent____title": "Add component", "@sage/xtrem-master-data/pages__sequence_number__componentLength____title": "Sequence number length", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__constant": "Constant", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__length": "Length", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title__2": "Component", "@sage/xtrem-master-data/pages__sequence_number__components____title": "Components", "@sage/xtrem-master-data/pages__sequence_number__componentsBlock____title": "Components", "@sage/xtrem-master-data/pages__sequence_number__createValue____title": "Create start value", "@sage/xtrem-master-data/pages__sequence_number__definitionLevel____title": "Definition level", "@sage/xtrem-master-data/pages__sequence_number__delete____title": "Delete", "@sage/xtrem-master-data/pages__sequence_number__id____title": "ID", "@sage/xtrem-master-data/pages__sequence_number__isChronological____title": "Chronological control", "@sage/xtrem-master-data/pages__sequence_number__isClearedByReset____title": "Force reset with tenant", "@sage/xtrem-master-data/pages__sequence_number__isUsed____title": "Used", "@sage/xtrem-master-data/pages__sequence_number__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-master-data/pages__sequence_number__mainSection____title": "General", "@sage/xtrem-master-data/pages__sequence_number__name____title": "Name", "@sage/xtrem-master-data/pages__sequence_number__propertiesBlock____title": "Sequence number details", "@sage/xtrem-master-data/pages__sequence_number__resetBlock____title": "Reset details", "@sage/xtrem-master-data/pages__sequence_number__rtzLevel____title": "Reset frequency", "@sage/xtrem-master-data/pages__sequence_number__save____title": "Save", "@sage/xtrem-master-data/pages__sequence_number__type____title": "Type", "@sage/xtrem-master-data/pages__sequence_number__updateValue____title": "Update value", "@sage/xtrem-master-data/pages__sequence_number_assignment_filter": "Filter", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup____title": "Sequence number assignment", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__addEditAssignmentLine____title": "Add", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____lookupDialogTitle": "Select company", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____title": "Company", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__isDefaultAssignment____title": "Include default values", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____title": "Legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__lineBlock____title": "Criteria", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__company__name": "Company", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isDefaultAssignment": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__legislation__name": "Legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__title": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeName": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__sequenceNumber__name": "Sequence number", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title__2": "Delete", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____title": "Results", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____placeholder": "Select site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____title": "Site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel____title": "Sequence number assignment panel", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____lookupDialogTitle": "Select company", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____placeholder": "Select company", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____title": "Company", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__edit____title": "Edit sequence number assignment", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isAssignOnPosting____title": "Assign on posting", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isUsed____title": "Used", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____lookupDialogTitle": "Select legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____placeholder": "Select legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____title": "Legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__new____title": "New sequence number assignment", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__save____title": "Save", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__id": "ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__legislation__id": "Legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__name": "Name", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____lookupDialogTitle": "Select sequence number", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____title": "Sequence number", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__columns__sequenceNumberAssignmentModule__id__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__nodeFactory__name": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__nodeName": "Document", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__sequenceNumberAssignmentModule__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__setupId": "Setup ID", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____lookupDialogTitle": "Select document type", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____placeholder": "Select sequence number", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____title": "Document type", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title": "Legislation", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____placeholder": "Select site", "@sage/xtrem-master-data/pages__sequence_number_value____title": "Create sequence number value", "@sage/xtrem-master-data/pages__sequence_number_value__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__sequence_number_value__confirm_action_dialog_content": "You are about to create the sequence number value.", "@sage/xtrem-master-data/pages__sequence_number_value__confirm-continue": "Continue", "@sage/xtrem-master-data/pages__sequence_number_value__definitionLevel____title": "Definition level", "@sage/xtrem-master-data/pages__sequence_number_value__id____title": "Sequence number", "@sage/xtrem-master-data/pages__sequence_number_value__minimumLength____title": "Length", "@sage/xtrem-master-data/pages__sequence_number_value__rtzLevel____title": "Reset frequency", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title___updateStamp": "Last updated on", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__id": "Company ID", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__name": "Company", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__newNextValue": "New next value", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__periodDate": "Period date", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__sequenceValue": "Next value", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__id": "Site ID", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__sequence_number_value_confirm_action_dialog_title": "Confirm", "@sage/xtrem-master-data/pages__sequence-number_value_add_new____title": "Create sequence number value", "@sage/xtrem-master-data/pages__sequence-number_value_create____title": "Create", "@sage/xtrem-master-data/pages__sequence-number_value_edit____title": "Update value", "@sage/xtrem-master-data/pages__sequence-number_value_update____title": "Update", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftEnd__title": "End time", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftStart__title": "Start time", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__shift_detail____objectTypePlural": "Shift details", "@sage/xtrem-master-data/pages__shift_detail____objectTypeSingular": "Shift detail", "@sage/xtrem-master-data/pages__shift_detail____title": "Shift detail", "@sage/xtrem-master-data/pages__shift_detail__formattedDuration____title": "Duration", "@sage/xtrem-master-data/pages__shift_detail__id____title": "ID", "@sage/xtrem-master-data/pages__shift_detail__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__shift_detail__mainSection____title": "General", "@sage/xtrem-master-data/pages__shift_detail__name____title": "Name", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____title": "End time", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____title": "Start time", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line_4__title": "Company", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line10__title": "Financial site", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__sequenceNumber__title": "Sequence number value", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__taxId__title": "Tax ID", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__site____objectTypePlural": "Sites", "@sage/xtrem-master-data/pages__site____objectTypeSingular": "Site", "@sage/xtrem-master-data/pages__site____title": "Site", "@sage/xtrem-master-data/pages__site__addresses____addButtonText": "Add address", "@sage/xtrem-master-data/pages__site__addresses____columns__title__concatenatedAddressWithoutName": "Address without name", "@sage/xtrem-master-data/pages__site__addresses____columns__title__isPrimary": "Primary address", "@sage/xtrem-master-data/pages__site__addresses____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title": "Edit address", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__2": "Define as primary address", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__3": "Add contact", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__4": "Delete address and contacts", "@sage/xtrem-master-data/pages__site__addresses____title": "Addresses", "@sage/xtrem-master-data/pages__site__addressSection____title": "Address", "@sage/xtrem-master-data/pages__site__already_exists_with_same_id": "This ID is already assigned to a site.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_name": "This name is already assigned to a site.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_taxIdNumber": "This tax identification number is already assigned to a site.", "@sage/xtrem-master-data/pages__site__contacts____addButtonText": "Add contact", "@sage/xtrem-master-data/pages__site__contacts____columns__title__isPrimary": "Primary contact", "@sage/xtrem-master-data/pages__site__contacts____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__2": "Define as primary", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__3": "Delete", "@sage/xtrem-master-data/pages__site__contacts____headerLabel__title": "Is active", "@sage/xtrem-master-data/pages__site__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__site__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__site__country____lookupDialogTitle": "Select country", "@sage/xtrem-master-data/pages__site__createFromBusinessEntity____title": "Create from business entity", "@sage/xtrem-master-data/pages__site__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__site__defaultLocation____lookupDialogTitle": "Select default location", "@sage/xtrem-master-data/pages__site__defaultLocation____title": "Default location", "@sage/xtrem-master-data/pages__site__description____title": "Description", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title": "Contacts", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__2": "Primary address", "@sage/xtrem-master-data/pages__site__financialSite____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__site__financialSite____lookupDialogTitle": "Select financial site", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createdBy": "Created by", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createStamp": "Created", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updatedBy": "Updated by", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updateStamp": "Updated", "@sage/xtrem-master-data/pages__site__groupRoleSites____title": "Authorization groups", "@sage/xtrem-master-data/pages__site__hierarchyChartContent____title": "Organization", "@sage/xtrem-master-data/pages__site__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__site__isFinance____title": "Finance", "@sage/xtrem-master-data/pages__site__isInventory____title": "Stock", "@sage/xtrem-master-data/pages__site__isLocationManaged____title": "Location management", "@sage/xtrem-master-data/pages__site__isManufacturing____title": "Manufacturing", "@sage/xtrem-master-data/pages__site__isProjectManagement____title": "Project management", "@sage/xtrem-master-data/pages__site__isPurchase____title": "Purchase", "@sage/xtrem-master-data/pages__site__isSales____title": "Sales", "@sage/xtrem-master-data/pages__site__isSequenceNumberIdUsed____title": "Sequence number used", "@sage/xtrem-master-data/pages__site__legalCompany____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__site__legalCompany____lookupDialogTitle": "Select company", "@sage/xtrem-master-data/pages__site__mainBlock____title": "General", "@sage/xtrem-master-data/pages__site__mainSection____title": "General", "@sage/xtrem-master-data/pages__site__managementSection____title": "Management", "@sage/xtrem-master-data/pages__site__save____title": "Save", "@sage/xtrem-master-data/pages__site__sequenceNumberId____title": "Sequence number value", "@sage/xtrem-master-data/pages__site__siteGroups____columns__title__isLegalCompany": "Company", "@sage/xtrem-master-data/pages__site__siteGroups____title": "Site groups", "@sage/xtrem-master-data/pages__site__siteGroupSection____title": "Site groups", "@sage/xtrem-master-data/pages__site__timeZone____title": "Time zone", "@sage/xtrem-master-data/pages__site__userGroupSection____title": "User groups", "@sage/xtrem-master-data/pages__site__website____title": "Website", "@sage/xtrem-master-data/pages__site_page_taxIdNumber_required_if_no_business_entity": "Tax ID is required if no business entity is selected.", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line3__title": "Standard", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line6__title": "Sector", "@sage/xtrem-master-data/pages__standard____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__standard____objectTypePlural": "Standards", "@sage/xtrem-master-data/pages__standard____objectTypeSingular": "Standard", "@sage/xtrem-master-data/pages__standard____title": "Standards", "@sage/xtrem-master-data/pages__standard__code____title": "Code", "@sage/xtrem-master-data/pages__standard__id____title": "ID", "@sage/xtrem-master-data/pages__standard__idBlock____title": "ID", "@sage/xtrem-master-data/pages__standard__industrySector____title": "Sector", "@sage/xtrem-master-data/pages__standard__mainBlock____title": "General", "@sage/xtrem-master-data/pages__standard__mainSection____title": "General", "@sage/xtrem-master-data/pages__standard__name____title": "Name", "@sage/xtrem-master-data/pages__standard__sdo____title": "Standard", "@sage/xtrem-master-data/pages__standard__version____title": "Version", "@sage/xtrem-master-data/pages__stock_journal_inquiry": "", "@sage/xtrem-master-data/pages__stock_posting_error": "", "@sage/xtrem-master-data/pages__stock_sequence_number____navigationPanel__listItem__counterLength__title": "Sequence length", "@sage/xtrem-master-data/pages__stock_sequence_number____navigationPanel__listItem__line2__title": "ID", "@sage/xtrem-master-data/pages__stock_sequence_number____navigationPanel__listItem__numberLocations__title": "Number of locations", "@sage/xtrem-master-data/pages__stock_sequence_number____navigationPanel__listItem__title__title": "Name", "@sage/xtrem-master-data/pages__stock_sequence_number____objectTypePlural": "Stock sequence number", "@sage/xtrem-master-data/pages__stock_sequence_number____objectTypeSingular": "Stock sequence number", "@sage/xtrem-master-data/pages__stock_sequence_number____title": "Stock sequence number", "@sage/xtrem-master-data/pages__stock_sequence_number___id____title": "ID", "@sage/xtrem-master-data/pages__stock_sequence_number__addComponent____title": "Add", "@sage/xtrem-master-data/pages__stock_sequence_number__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__stock_sequence_number__capital_letters_only": "An alphabetical sequence component can only contain capital letters.", "@sage/xtrem-master-data/pages__stock_sequence_number__components____columns__title__constant": "Constant", "@sage/xtrem-master-data/pages__stock_sequence_number__components____columns__title__endValue": "End value", "@sage/xtrem-master-data/pages__stock_sequence_number__components____columns__title__length": "Length", "@sage/xtrem-master-data/pages__stock_sequence_number__components____columns__title__startValue": "Start value", "@sage/xtrem-master-data/pages__stock_sequence_number__components____columns__title__type": "Type", "@sage/xtrem-master-data/pages__stock_sequence_number__components____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__stock_sequence_number__components____title": "Components", "@sage/xtrem-master-data/pages__stock_sequence_number__componentsBlock____title": "Components", "@sage/xtrem-master-data/pages__stock_sequence_number__counterLength____title": "Sequence length", "@sage/xtrem-master-data/pages__stock_sequence_number__digits_only": "A numeric sequence component can only contain a number.", "@sage/xtrem-master-data/pages__stock_sequence_number__id____title": "ID", "@sage/xtrem-master-data/pages__stock_sequence_number__invalid_range": "The start value must be before the end value.", "@sage/xtrem-master-data/pages__stock_sequence_number__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__stock_sequence_number__name____title": "Name", "@sage/xtrem-master-data/pages__stock_sequence_number__numberLocations____title": "Number of locations", "@sage/xtrem-master-data/pages__stock_sequence_number__saveLocationSequence____title": "Save", "@sage/xtrem-master-data/pages__stock_sequence_number__type____title": "Type", "@sage/xtrem-master-data/pages__supplier____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__isActive__title": "Active", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line10__title": "Customer category", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line7__title": "Minimum order amount", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line8__title": "Payment term", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__supplier____objectTypePlural": "Suppliers", "@sage/xtrem-master-data/pages__supplier____objectTypeSingular": "Supplier", "@sage/xtrem-master-data/pages__supplier____title": "Supplier", "@sage/xtrem-master-data/pages__supplier__addCertificate____title": "Add", "@sage/xtrem-master-data/pages__supplier__addItem____title": "Add", "@sage/xtrem-master-data/pages__supplier__addPriceLine____title": "Add", "@sage/xtrem-master-data/pages__supplier__addressAndContactBlock____title": "Address", "@sage/xtrem-master-data/pages__supplier__addresses____addButtonText": "Add address", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__concatenatedAddressWithoutName": "Address without name", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__isPrimary": "Primary address", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title": "Edit address", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__2": "Define as primary address", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__3": "Add contact", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__4": "Delete address and contacts", "@sage/xtrem-master-data/pages__supplier__addresses____title": "Addresses", "@sage/xtrem-master-data/pages__supplier__addressSection____title": "Address", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_id": "This ID is already assigned to a supplier.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_name": "This name is already assigned to a supplier.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_taxIdNumber": "This tax identification number is already assigned to a supplier.", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__concatenatedAddress": "Primary bill-by address", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__billByAddress____dropdownActions__title": "Replace", "@sage/xtrem-master-data/pages__supplier__billByAddress____title": "Primary bill-by address", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine1": "Line 1", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine2": "Line 2", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__businessEntity__name": "Business entity", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__country__name": "Country", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__postcode": "ZIP code", "@sage/xtrem-master-data/pages__supplier__billBySupplier____title": "Bill-by supplier", "@sage/xtrem-master-data/pages__supplier__category____columns__title__sequenceNumber__name": "Sequence number", "@sage/xtrem-master-data/pages__supplier__category____lookupDialogTitle": "Select category", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__certificationBody": "Certification body", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfCertification": "Date of certification", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfOriginalCertification": "Date of original certification", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__id": "Certificate reference", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__standard__id": "Standard", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__validUntil": "Valid until", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title__2": "Delete", "@sage/xtrem-master-data/pages__supplier__certificates____title": "Certificates", "@sage/xtrem-master-data/pages__supplier__certificateSection____title": "Certificates", "@sage/xtrem-master-data/pages__supplier__commercialBlock____title": "Commercial", "@sage/xtrem-master-data/pages__supplier__commercialSection____title": "Commercial", "@sage/xtrem-master-data/pages__supplier__contacts____addButtonText": "Add contact", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__isPrimary": "Primary contact", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__2": "Define as primary", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__3": "Delete", "@sage/xtrem-master-data/pages__supplier__contacts____headerLabel__title": "Is Active", "@sage/xtrem-master-data/pages__supplier__contactSection____title": "Contacts", "@sage/xtrem-master-data/pages__supplier__country____columns__title__id": "ISO 3166-1 alpha-2", "@sage/xtrem-master-data/pages__supplier__country____lookupDialogTitle": "Select country", "@sage/xtrem-master-data/pages__supplier__createFromBusinessEntity____title": "Create from business entity", "@sage/xtrem-master-data/pages__supplier__currency____lookupDialogTitle": "Select currency", "@sage/xtrem-master-data/pages__supplier__deliveryMode____lookupDialogTitle": "Select delivery mode", "@sage/xtrem-master-data/pages__supplier__deliveryMode____title": "Delivery mode", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title": "Contacts", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__2": "Primary address", "@sage/xtrem-master-data/pages__supplier__financialBlock____title": "Financial", "@sage/xtrem-master-data/pages__supplier__financialSection____title": "Financial", "@sage/xtrem-master-data/pages__supplier__imageBlock____title": "Image", "@sage/xtrem-master-data/pages__supplier__incoterm____lookupDialogTitle": "Select Incoterm rule", "@sage/xtrem-master-data/pages__supplier__incoterm____title": "Incoterms® rule", "@sage/xtrem-master-data/pages__supplier__internalNote____title": "Internal notes", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isActive": "Active", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isDefaultItemSupplier": "Default supplier", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__description": "Item description", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____columns__title__minimumPurchaseQuantity": "Minimum purchase quantity", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseLeadTime": "Purchase lead time", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__name": "Purchase unit of measure", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemCode": "Item-supplier code", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemName": "Item-supplier name", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title": "Edit", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title__2": "Delete", "@sage/xtrem-master-data/pages__supplier__items____title": "Items", "@sage/xtrem-master-data/pages__supplier__itemSection____title": "Items", "@sage/xtrem-master-data/pages__supplier__mainBlock____title": "General", "@sage/xtrem-master-data/pages__supplier__mainSection____title": "General", "@sage/xtrem-master-data/pages__supplier__minimumOrderAmount____title": "Minimum order amount", "@sage/xtrem-master-data/pages__supplier__noteBlock____title": "Notes", "@sage/xtrem-master-data/pages__supplier__noteSection____title": "Notes", "@sage/xtrem-master-data/pages__supplier__parent____lookupDialogTitle": "Select parent", "@sage/xtrem-master-data/pages__supplier__parent____title": "Parent", "@sage/xtrem-master-data/pages__supplier__paymentMethod____title": "Payment method", "@sage/xtrem-master-data/pages__supplier__paymentTerm____lookupDialogTitle": "Select payment term", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__concatenatedAddress": "Primary pay-to address", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__payToAddress____dropdownActions__title": "Replace", "@sage/xtrem-master-data/pages__supplier__payToAddress____title": "Primary pay-to address", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine1": "Line 1", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine2": "Line 2", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__businessEntity__name": "Business entity", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__country__name": "Country", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__postcode": "ZIP code", "@sage/xtrem-master-data/pages__supplier__payToSupplier____title": "Bill-by supplier", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__concatenatedAddress": "Primary return-to address", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__returnToAddress____dropdownActions__title": "Replace", "@sage/xtrem-master-data/pages__supplier__returnToAddress____title": "Primary return-to address", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine1": "Line 1", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine2": "Line 2", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__businessEntity__name": "Business entity", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__country__name": "Country", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__locationPhoneNumber": "Phone number", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__postcode": "ZIP code", "@sage/xtrem-master-data/pages__supplier__save____title": "Save", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__currency__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidFrom": "<PERSON>id from", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidTo": "Valid to", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__fromQuantity": "From quantity", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__description": "Item description", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__id": "Item ID", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__id": "Site", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__name": "Site", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__toQuantity": "To quantity", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title__2": "Edit", "@sage/xtrem-master-data/pages__supplier__supplierPrices____title": "Supplier prices", "@sage/xtrem-master-data/pages__supplier__website____title": "Website", "@sage/xtrem-master-data/pages__supplier_certificate_panel____title": "Supplier certificate", "@sage/xtrem-master-data/pages__supplier_certificate_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__supplier_certificate_panel__certificationBody____title": "Certification body", "@sage/xtrem-master-data/pages__supplier_certificate_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfCertification____title": "Certification date", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfOriginalCertification____title": "Original certification date", "@sage/xtrem-master-data/pages__supplier_certificate_panel__edit____title": "Edit supplier certificate", "@sage/xtrem-master-data/pages__supplier_certificate_panel__id____title": "Certificate reference", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainBlock____title": "Certificate", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__supplier_certificate_panel__new____title": "New supplier certificate", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__code": "Code", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__industrySector": "Sector", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__sdo": "Standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__version": "Version", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____lookupDialogTitle": "Select standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____title": "Standard", "@sage/xtrem-master-data/pages__supplier_certificate_panel__validUntil____title": "Validity end date", "@sage/xtrem-master-data/pages__supplier_item_panel____title": "Edit supplier item", "@sage/xtrem-master-data/pages__supplier_item_panel___id____title": "ID", "@sage/xtrem-master-data/pages__supplier_item_panel__cancel____title": "Cancel", "@sage/xtrem-master-data/pages__supplier_item_panel__confirm____title": "OK", "@sage/xtrem-master-data/pages__supplier_item_panel__isDefaultItemSupplier____title": "Default supplier", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__columns__purchaseUnit__name__title": "Name", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__columns__purchaseUnit__name__title__2": "ID", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__columns__purchaseUnit__name__title__3": "Symbol", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__columns__purchaseUnit__name__title__4": "ID", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__supplier_item_panel__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__supplier_item_panel__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__supplier_item_panel__minimumPurchaseQuantity____title": "Minimum purchase quantity", "@sage/xtrem-master-data/pages__supplier_item_panel__new____title": "New item-supplier", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseLeadTime____title": "Purchase lead time", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____lookupDialogTitle": "Select unit", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____title": "Unit", "@sage/xtrem-master-data/pages__supplier_item_panel__supplier____lookupDialogTitle": "Select supplier", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemCode____title": "Item-supplier ID", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemName____title": "Item-supplier name", "@sage/xtrem-master-data/pages__team____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__team____objectTypePlural": "Teams", "@sage/xtrem-master-data/pages__team____objectTypeSingular": "Team", "@sage/xtrem-master-data/pages__team____title": "Team", "@sage/xtrem-master-data/pages__team__description____title": "Description", "@sage/xtrem-master-data/pages__team__id____title": "ID", "@sage/xtrem-master-data/pages__team__mainBlock____title": "Details", "@sage/xtrem-master-data/pages__team__mainSection____title": "General", "@sage/xtrem-master-data/pages__team__name____title": "Name", "@sage/xtrem-master-data/pages__team__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__team__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__bulkActions__title": "Delete", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeFrom__title": "Active from", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeTo__title": "Active to", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__title": "Efficiency", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__image__title": "Image", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__line2__title": "Site", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__location__title": "Location", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__resourceGroup__title": "Resource group", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__weeklyShift__title": "Weekly shift", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__2": "Active", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__3": "Inactive", "@sage/xtrem-master-data/pages__tool_resource____objectTypePlural": "Tool resources", "@sage/xtrem-master-data/pages__tool_resource____objectTypeSingular": "Tool resource", "@sage/xtrem-master-data/pages__tool_resource____title": "Tool resource", "@sage/xtrem-master-data/pages__tool_resource___id____title": "ID", "@sage/xtrem-master-data/pages__tool_resource__activeFrom____title": "Active from", "@sage/xtrem-master-data/pages__tool_resource__activeTo____title": "Active to", "@sage/xtrem-master-data/pages__tool_resource__addCostCategory____title": "Add cost category", "@sage/xtrem-master-data/pages__tool_resource__blockDetails____title": "Settings", "@sage/xtrem-master-data/pages__tool_resource__blockWeekly____title": "Weekly shift details", "@sage/xtrem-master-data/pages__tool_resource__cancelSidePanel____title": "Cancel", "@sage/xtrem-master-data/pages__tool_resource__consumptionMode____title": "Consumption mode", "@sage/xtrem-master-data/pages__tool_resource__costBlock____title": "Cost", "@sage/xtrem-master-data/pages__tool_resource__costSection____title": "Cost", "@sage/xtrem-master-data/pages__tool_resource__description____title": "Description", "@sage/xtrem-master-data/pages__tool_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__tool_resource__efficiency____title": "Efficiency", "@sage/xtrem-master-data/pages__tool_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__tool_resource__hoursTracked____title": "Service life", "@sage/xtrem-master-data/pages__tool_resource__id____title": "ID", "@sage/xtrem-master-data/pages__tool_resource__isActive____title": "Active", "@sage/xtrem-master-data/pages__tool_resource__item____columns__title__category__name": "Category", "@sage/xtrem-master-data/pages__tool_resource__item____lookupDialogTitle": "Select item", "@sage/xtrem-master-data/pages__tool_resource__item____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__location____columns__title__locationType__id": "Type", "@sage/xtrem-master-data/pages__tool_resource__location____lookupDialogTitle": "Select location", "@sage/xtrem-master-data/pages__tool_resource__location____title": "Location", "@sage/xtrem-master-data/pages__tool_resource__name____title": "Name", "@sage/xtrem-master-data/pages__tool_resource__quantity____title": "Quantity", "@sage/xtrem-master-data/pages__tool_resource__resourceCapacity____title": "Weekly capacity", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Cost category type", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Mandatory", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costCategory__name": "Cost category", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costUnit__name": "Cost unit", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Indirect cost", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__runCost": "Run", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__setupCost": "Setup", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____title": "Resource cost categories", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____lookupDialogTitle": "Select resource group", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____title": "Resource group", "@sage/xtrem-master-data/pages__tool_resource__resourceImage____title": "Image", "@sage/xtrem-master-data/pages__tool_resource__saveSidePanel____title": "Save", "@sage/xtrem-master-data/pages__tool_resource__section____title": "General", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__name": "Company", "@sage/xtrem-master-data/pages__tool_resource__site____lookupDialogTitle": "Select site", "@sage/xtrem-master-data/pages__tool_resource__toolDetails____title": "Usage", "@sage/xtrem-master-data/pages__tool_resource__unitProduced____title": "Unit produced", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__capacity": "Capacity", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__dailyShift": "Daily shift", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__day": "Day", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift1": "Shift 1", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift2": "Shift 2", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift3": "Shift 3", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift4": "Shift 4", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift5": "Shift 5", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____title": "Weekly details", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____columns__title__formattedCapacity": "Capacity", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____lookupDialogTitle": "Select weekly shift", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____title": "Weekly shift", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line_4__title": "Decimal places", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line3__title": "Unit type", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypePlural": "Units of measure", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypeSingular": "Unit of measure", "@sage/xtrem-master-data/pages__unit_of_measure____title": "Unit of measure", "@sage/xtrem-master-data/pages__unit_of_measure__addConversion____title": "Add", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title": "Name", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title__2": "ID", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__item__name__title": "Category", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__lookupDialogTitle__item__name": "Select item", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__2": "Target unit", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__3": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__4": "Reverse factor", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__coefficient": "Factor", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__customer__businessEntity__name": "Customer", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__fromUnit__name": "Base unit", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__isStandard": "Standard", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__item__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__type": "Flow type", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____dropdownActions__title": "Delete", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____title": "Conversion", "@sage/xtrem-master-data/pages__unit_of_measure__conversionSection____title": "Conversion", "@sage/xtrem-master-data/pages__unit_of_measure__decimalDigits____title": "Decimal digits", "@sage/xtrem-master-data/pages__unit_of_measure__description____title": "Description", "@sage/xtrem-master-data/pages__unit_of_measure__generalSection____title": "General", "@sage/xtrem-master-data/pages__unit_of_measure__id____title": "ID", "@sage/xtrem-master-data/pages__unit_of_measure__isActive____title": "Active", "@sage/xtrem-master-data/pages__unit_of_measure__mainBlock____title": "General", "@sage/xtrem-master-data/pages__unit_of_measure__name____title": "Name", "@sage/xtrem-master-data/pages__unit_of_measure__symbol____title": "Symbol", "@sage/xtrem-master-data/pages__unit_of_measure__type____title": "Unit type", "@sage/xtrem-master-data/pages__utils__notification__custom_validation_error": "Validation errors occurred:\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__formattedCapacity__title": "Capacity", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__isFullWeek__title": "Full week", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__optionsMenu__title": "All", "@sage/xtrem-master-data/pages__weekly_shift____objectTypePlural": "Weekly shifts", "@sage/xtrem-master-data/pages__weekly_shift____objectTypeSingular": "Weekly shift", "@sage/xtrem-master-data/pages__weekly_shift____title": "Weekly shift", "@sage/xtrem-master-data/pages__weekly_shift___id____title": "ID", "@sage/xtrem-master-data/pages__weekly_shift__detailsBlock____title": "Details", "@sage/xtrem-master-data/pages__weekly_shift__formattedCapacity____title": "Capacity", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____lookupDialogTitle": "Select Friday shift", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____title": "Friday shift", "@sage/xtrem-master-data/pages__weekly_shift__id____title": "ID", "@sage/xtrem-master-data/pages__weekly_shift__isFullWeek____title": "Full week", "@sage/xtrem-master-data/pages__weekly_shift__mainBlock____title": "General", "@sage/xtrem-master-data/pages__weekly_shift__mainSection____title": "General", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____lookupDialogTitle": "Select Monday shift", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____title": "Monday shift", "@sage/xtrem-master-data/pages__weekly_shift__name____title": "Name", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____lookupDialogTitle": "Select Saturday shift", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____title": "Saturday shift", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____lookupDialogTitle": "Select Sunday shift", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____title": "Sunday shift", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____lookupDialogTitle": "Select Thursday shift", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____title": "Thursday shift", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____lookupDialogTitle": "Select Tuesday shift", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____title": "Tuesday shift", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__id": "ID", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__name": "Name", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____lookupDialogTitle": "Select Wednesday shift", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____title": "Wednesday shift", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_content": "You are about to delete this record.", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_title": "Confirm deletion", "@sage/xtrem-master-data/pages_currency_delete_inverse_rate_page_dialog_content": "The inverse rate already exists for the specified date. Do you want to keep the inverse rate or delete it?", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_content": "You are about to delete this record.", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_title": "Confirm deletion", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_content": "You are about to delete this record.", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_title": "Confirm deletion", "@sage/xtrem-master-data/pages_sidebar_block_title_definition": "Definition", "@sage/xtrem-master-data/pages_sidebar_block_title_price": "Price elements", "@sage/xtrem-master-data/pages_sidebar_block_title_ranges": "Ranges", "@sage/xtrem-master-data/pages_sidebar_tab_title_definition": "Definition", "@sage/xtrem-master-data/pages_sidebar_tab_title_information": "Information", "@sage/xtrem-master-data/pages_sidebar_tab_title_prices": "Price elements", "@sage/xtrem-master-data/pages_sidebar_tab_title_ranges": "Ranges", "@sage/xtrem-master-data/pages_site__address_mandatory": "Assign at least one address to the site.", "@sage/xtrem-master-data/pages_supplier__address_mandatory": "Assign at least one address to the supplier.", "@sage/xtrem-master-data/pages-cancel-keep": "Keep", "@sage/xtrem-master-data/pages-confirm-apply": "Apply new", "@sage/xtrem-master-data/pages-confirm-apply-new": "Apply new", "@sage/xtrem-master-data/pages-confirm-cancel": "Cancel", "@sage/xtrem-master-data/pages-confirm-continue": "Continue", "@sage/xtrem-master-data/pages-confirm-delete": "Delete", "@sage/xtrem-master-data/pages-confirm-no": "No", "@sage/xtrem-master-data/pages-confirm-send": "Send", "@sage/xtrem-master-data/pages-confirm-yes": "Yes", "@sage/xtrem-master-data/permission__create__name": "Create", "@sage/xtrem-master-data/permission__delete__name": "Delete", "@sage/xtrem-master-data/permission__manage__name": "Manage", "@sage/xtrem-master-data/permission__read__name": "Read", "@sage/xtrem-master-data/permission__update__name": "Update", "@sage/xtrem-master-data/sales-to-stock-unit-must-be-one": "Set the sales to stock unit conversion factor to 1 when the units are the same", "@sage/xtrem-master-data/service_options__allocation_transfer_option__name": "Allocation transfer option", "@sage/xtrem-master-data/service_options__bill_of_material_revision_service_option__name": "Bill of material revision service option", "@sage/xtrem-master-data/service_options__customer_360_view_option__name": "Customer 360 view option", "@sage/xtrem-master-data/service_options__datev_option__name": "DATEV option", "@sage/xtrem-master-data/service_options__fifo_valuation_method_option__name": "FIFO valuation method option", "@sage/xtrem-master-data/service_options__landed_cost_option__name": "Landed cost option", "@sage/xtrem-master-data/service_options__landed_cost_order_option__name": "Landed cost order option", "@sage/xtrem-master-data/service_options__landed_cost_stock_transfer_option__name": "Landed cost stock transfer option", "@sage/xtrem-master-data/service_options__order_to_order_option__name": "Order-to-order option", "@sage/xtrem-master-data/service_options__phantom_item_option__name": "Phantom item option", "@sage/xtrem-master-data/service_options__serial_number_option__name": "Serial number option", "@sage/xtrem-master-data/site-extension-financial-currency-not-defined": "The financial currency is not defined.", "@sage/xtrem-master-data/telephone-validation-error": "Invalid phone number", "@sage/xtrem-master-data/update-confirmation": "Record updated", "@sage/xtrem-master-data/use-existing-business-entity": "Business entity found: {{beName}} name, {{beId}} ID and {{beTaxId}} tax ID. Use this business entity?", "@sage/xtrem-master-data/value-must-be-greater-than-current-sequence": "The new value is smaller than the current value.", "@sage/xtrem-master-data/value-must-be-positive": "Value {{value}} must be positive.", "@sage/xtrem-master-data/value-must-not-exceed-the-length-of-sequence-number": "The new value needs to be less than the length of the sequence number.", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__addresses__title": "See addresses", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__contacts__title": "See contacts", "@sage/xtrem-master-data/widgets__customer_contact_list____title": "Customer contacts", "@sage/xtrem-master-data/widgets__system_version____title": "System version"}