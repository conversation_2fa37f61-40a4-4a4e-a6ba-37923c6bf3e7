{"@sage/xtrem-master-data/activity__allergen__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__bom_revision_sequence__name": "BOM revision sequence", "@sage/xtrem-master-data/activity__business_entity__name": "Entidad empresarial", "@sage/xtrem-master-data/activity__capability_level__name": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/activity__container__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__cost_category__name": "Categoría de coste", "@sage/xtrem-master-data/activity__currency__name": "Divisa", "@sage/xtrem-master-data/activity__customer__name": "Cliente", "@sage/xtrem-master-data/activity__customer_price_reason__name": "Motivo de precio para cliente", "@sage/xtrem-master-data/activity__customer_supplier_category__name": "Categoría de proveedor y de cliente", "@sage/xtrem-master-data/activity__daily_shift__name": "Turno diario", "@sage/xtrem-master-data/activity__delivery_mode__name": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/activity__employee__name": "Trabajador", "@sage/xtrem-master-data/activity__ghs_classification__name": "Clasificación según SGA", "@sage/xtrem-master-data/activity__group_resource__name": "Recurso de grupo", "@sage/xtrem-master-data/activity__incoterm__name": "Incoterms", "@sage/xtrem-master-data/activity__indirect_cost_origin__name": "Origen de coste indirecto", "@sage/xtrem-master-data/activity__indirect_cost_section__name": "Sección de coste indirecto", "@sage/xtrem-master-data/activity__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__item_category__name": "Categoría de artículo", "@sage/xtrem-master-data/activity__item_site__name": "Artículo-planta", "@sage/xtrem-master-data/activity__item_site_cost__name": "Coste de artículo-planta", "@sage/xtrem-master-data/activity__item_site_supplier__name": "Artículo-planta-proveedor", "@sage/xtrem-master-data/activity__labour_resource__name": "<PERSON>o de obra", "@sage/xtrem-master-data/activity__license_plate_number__name": "Número de contenedor interno", "@sage/xtrem-master-data/activity__location__name": "Ubicación", "@sage/xtrem-master-data/activity__location_sequence__name": "Secuencia de ubicación", "@sage/xtrem-master-data/activity__location_type__name": "Tipo de ubicación", "@sage/xtrem-master-data/activity__location_zone__name": "Área de almacenamiento", "@sage/xtrem-master-data/activity__machine_resource__name": "Máquina", "@sage/xtrem-master-data/activity__payment_term__name": "Condiciones de pago", "@sage/xtrem-master-data/activity__reason_code__name": "Código de motivo", "@sage/xtrem-master-data/activity__sequence_number__name": "Número de secuencia", "@sage/xtrem-master-data/activity__sequence_number_assignment__name": "Asignación de número de secuencia", "@sage/xtrem-master-data/activity__shift_detail__name": "Detalles de turno", "@sage/xtrem-master-data/activity__standard__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__supplier__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/activity__supplier_certificate__name": "Certificado de proveedor", "@sage/xtrem-master-data/activity__tool_resource__name": "Herramienta", "@sage/xtrem-master-data/activity__unit_of_measure__name": "Unidad de medida", "@sage/xtrem-master-data/activity__weekly_shift__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/business_entity_address_node_only_one_primary_contact": "", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological_control_must_be_active_fr_legislation": "Número de secuencia {{sequenceNumber}}: el control cronológico para la legislación francesa debe estar activo.", "@sage/xtrem-master-data/classes__sequence-number-generator__chronological-control-must-be-active": "Número de secuencia {{sequenceNumber}}: el control cronológico para la legislación francesa debe estar activo.", "@sage/xtrem-master-data/classes__sequence-number-generator__document_date_higher_than_next_date": "La fecha de documento {{currentDate}} es posterior a la del documento siguiente: {{previousDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document_date_lower_than_previous_date": "La fecha de documento {{currentDate}} es anterior a la del documento previo: {{previousDocumentDate}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-cannot-be-later-than-today": "La fecha de documento no puede ser posterior a la fecha actual.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-earlier-than-previous-document-date": "The document date {{current}} is earlier than the previous document date {{previousDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__document-date-later-than-next-document-date": "The document date {{current}} is later than the next document date {{nextDocument}}.", "@sage/xtrem-master-data/classes__sequence-number-generator__future_date_not_allowed": "La fecha de documento no puede ser posterior a la fecha actual.", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-component-type-value": "El tipo de componente {{type}} no es correcto.", "@sage/xtrem-master-data/classes__sequence-number-generator__invalid-enum-value": "El valor {{enumValue}} no es correcto.", "@sage/xtrem-master-data/classes__sequence-number-generator__invoice_date_higher_than_next_date": "La fecha del documento ({{currentDate}}) es posterior a la del documento siguiente ({{previousDocumentDate}}).", "@sage/xtrem-master-data/classes__sequence-number-generator__monthly-sequence-numbers-not-allowed": "Número de secuencia {{sequenceNumber}}: los números de secuencia mensuales no están permitidos.", "@sage/xtrem-master-data/classes__sequence-number-generator__no-sequence-number-assigned": "No se ha asignado ningún número de secuencia a este tipo de documento.", "@sage/xtrem-master-data/classes__sequence-number-generator__node-instance-is-required": "La instancia del nodo es obligatoria.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-at-application-level-definition-and-no-input-value-for-site": "The {{id}} sequence number is defined at the {{definitionLevel}} level and the site is not entered.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-counter-not-defined-at-application-level": "The {{id}} sequence number is not defined at the application level. You must enter a site or company in the sequence number components.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-instance-not-found": "Sequence number instance not found.", "@sage/xtrem-master-data/classes__sequence-number-generator__sequence-number-not-defined": "", "@sage/xtrem-master-data/client_functions__master_data__resync_status_continue": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/client_functions__master_data__resync_status_message": "¿Quieres actualizar el estado.", "@sage/xtrem-master-data/client_functions__master_data__resync_status_title": "Comprobar y actualizar estado.", "@sage/xtrem-master-data/client_functions__master_data_resync_submitted": "Resync document request submitted:({{batchTaskId}}).", "@sage/xtrem-master-data/company_node_only_one_primary_address": "Solo se permite una dirección principal.", "@sage/xtrem-master-data/company_node_only_one_primary_contact": "Solo puede haber un contacto principal.", "@sage/xtrem-master-data/control__item__landedCost_service_option_inactive": "Solo puedes crear un artículo que sea gastos de entrega si la opción de servicio de gastos de entrega está activa.", "@sage/xtrem-master-data/control-begin__sequence-number__definition_level_is_not_present_in_components": "El número de secuencia se ha definido a nivel \"{{definitionLevel}}\". Introduce un componente de ese tipo en la tabla de componentes.", "@sage/xtrem-master-data/control-begin__sequence-number__no_sequence_number_component": "Introduce un componente de número de secuencia.", "@sage/xtrem-master-data/control-begin__sequence-number__rtz_level_is_not_present_in_components": "Si la frecuencia de restablecimiento definida es \"{{rtzLevel}}\", tienes que introducir el tipo \"{{type}}\" en la tabla de componentes.", "@sage/xtrem-master-data/control-begin__sequence-number__sequence-numeric-wrong-component": "El número de secuencia es numérico. Solo puedes introducir componentes numéricos.", "@sage/xtrem-master-data/create": "Generar", "@sage/xtrem-master-data/create-confirmation": "<PERSON><PERSON> c<PERSON>o", "@sage/xtrem-master-data/data_types__address_entity_type_enum__name": "Address entity type enum", "@sage/xtrem-master-data/data_types__address_line_data_type__name": "Address line data type", "@sage/xtrem-master-data/data_types__amount_data_type__name": "Importe", "@sage/xtrem-master-data/data_types__amount_in_company_currency__name": "Importe en divisa de sociedad", "@sage/xtrem-master-data/data_types__amount_in_financial_site_currency__name": "Amount in financial site currency", "@sage/xtrem-master-data/data_types__amount_in_transaction_currency__name": "Importe en divisa de transacción", "@sage/xtrem-master-data/data_types__approval_status_enum__name": "Approval status enum", "@sage/xtrem-master-data/data_types__base_certificate_property_data_type__name": "Base certificate property data type", "@sage/xtrem-master-data/data_types__base_decimal__name": "Base decimal", "@sage/xtrem-master-data/data_types__base_display_status_enum__name": "Base display status enum", "@sage/xtrem-master-data/data_types__base_origin_enum__name": "Base origin enum", "@sage/xtrem-master-data/data_types__base_price__name": "Precio base", "@sage/xtrem-master-data/data_types__base_sequence_number_component_type_enum__name": "Base sequence number component type enum", "@sage/xtrem-master-data/data_types__base_status_enum__name": "Base status enum", "@sage/xtrem-master-data/data_types__bom_revision_sequence__name": "BOM revision sequence", "@sage/xtrem-master-data/data_types__business_entity__name": "Entidad empresarial", "@sage/xtrem-master-data/data_types__business_entity_id__name": "Id. de entidad empresarial", "@sage/xtrem-master-data/data_types__business_entity_type_enum__name": "Tipo de entidad empresarial", "@sage/xtrem-master-data/data_types__business_relation_type_enum__name": "Business relation type enum", "@sage/xtrem-master-data/data_types__capacity_percentage__name": "Porcentaje de capacidad", "@sage/xtrem-master-data/data_types__city_data_type__name": "Ciudad", "@sage/xtrem-master-data/data_types__coefficient_data_type__name": "Coeficiente", "@sage/xtrem-master-data/data_types__company_price_data_type__name": "Company price data type", "@sage/xtrem-master-data/data_types__constant_sequence_data_type__name": "Constant sequence data type", "@sage/xtrem-master-data/data_types__consumption_mode_enum__name": "Modo de consumo", "@sage/xtrem-master-data/data_types__contact_position_data_type__name": "Contact position data type", "@sage/xtrem-master-data/data_types__contact_property_data_type__name": "Contact property data type", "@sage/xtrem-master-data/data_types__contact_role_enum__name": "Contact role enum", "@sage/xtrem-master-data/data_types__container_type_enum__name": "Container type enum", "@sage/xtrem-master-data/data_types__cost_calculation_method_enum__name": "Cost calculation method enum", "@sage/xtrem-master-data/data_types__cost_category_type_enum__name": "Tipo de categoría de coste", "@sage/xtrem-master-data/data_types__cost_data_type__name": "Cost data type", "@sage/xtrem-master-data/data_types__cost_valuation_method_enum__name": "Cost valuation method enum", "@sage/xtrem-master-data/data_types__cost_value_data_type__name": "Cost value data type", "@sage/xtrem-master-data/data_types__currency__name": "Divisa", "@sage/xtrem-master-data/data_types__customer__name": "Cliente", "@sage/xtrem-master-data/data_types__customer_display_status_enum__name": "Customer display status enum", "@sage/xtrem-master-data/data_types__customer_on_hold_type_enum__name": "Customer on hold type enum", "@sage/xtrem-master-data/data_types__customer_supplier_category__name": "Categoría de proveedor y cliente", "@sage/xtrem-master-data/data_types__delivery_mode__name": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/data_types__discount_charge_calculation_basis_enum__name": "Discount charge calculation basis enum", "@sage/xtrem-master-data/data_types__discount_charge_calculation_rule_enum__name": "Discount charge calculation rule enum", "@sage/xtrem-master-data/data_types__discount_charge_sign_enum__name": "Discount charge sign enum", "@sage/xtrem-master-data/data_types__discount_charge_value_type_enum__name": "Discount charge value type enum", "@sage/xtrem-master-data/data_types__discount_or_penalty_type_enum__name": "Discount or penalty type enum", "@sage/xtrem-master-data/data_types__document_number__name": "Número de documento", "@sage/xtrem-master-data/data_types__due_date_type_enum__name": "Tipo de fecha de vencimiento", "@sage/xtrem-master-data/data_types__duration_data_type__name": "Duración", "@sage/xtrem-master-data/data_types__ean_number_data_type__name": "EAN number data type", "@sage/xtrem-master-data/data_types__efficiency_percentage__name": "Efficiency percentage", "@sage/xtrem-master-data/data_types__email_action_type_enum__name": "Email action type enum", "@sage/xtrem-master-data/data_types__exchange_rate__name": "Tipo de cambio", "@sage/xtrem-master-data/data_types__extra_large_string__name": "Extra large string", "@sage/xtrem-master-data/data_types__fake_site_reference_datatype__name": "", "@sage/xtrem-master-data/data_types__incoterm__name": "Incoterms", "@sage/xtrem-master-data/data_types__incoterm_data_type__name": "Incoterms", "@sage/xtrem-master-data/data_types__indirect_cost_percentage__name": "Indirect cost percentage", "@sage/xtrem-master-data/data_types__input_sequence_data_type__name": "Input sequence data type", "@sage/xtrem-master-data/data_types__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__item_category__name": "Categoría de artículo", "@sage/xtrem-master-data/data_types__item_category_type_enum__name": "Item category type enum", "@sage/xtrem-master-data/data_types__item_flow_type_enum__name": "Item flow type enum", "@sage/xtrem-master-data/data_types__item_price_type_enum__name": "Item price type enum", "@sage/xtrem-master-data/data_types__item_status_enum__name": "Item status enum", "@sage/xtrem-master-data/data_types__item_type_enum__name": "Item type enum", "@sage/xtrem-master-data/data_types__label_format_data_type__name": "Label format data type", "@sage/xtrem-master-data/data_types__large_string__name": "Large string", "@sage/xtrem-master-data/data_types__legal_entity_enum__name": "Legal entity enum", "@sage/xtrem-master-data/data_types__localized_sic_description_data_type__name": "Localized sic description data type", "@sage/xtrem-master-data/data_types__location__name": "Ubicación", "@sage/xtrem-master-data/data_types__location_category_enum__name": "Categoría de ubicación", "@sage/xtrem-master-data/data_types__location_sequence__name": "Secuencia de ubicación", "@sage/xtrem-master-data/data_types__lot_management_enum__name": "Lot management enum", "@sage/xtrem-master-data/data_types__master_data_company__name": "Master data company", "@sage/xtrem-master-data/data_types__master_data_site__name": "Master data site", "@sage/xtrem-master-data/data_types__medium_string__name": "Medium string", "@sage/xtrem-master-data/data_types__model_data_type__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__note__name": "<PERSON>a", "@sage/xtrem-master-data/data_types__order_cost_data_type__name": "Order cost data type", "@sage/xtrem-master-data/data_types__order_type_enum__name": "Order type enum", "@sage/xtrem-master-data/data_types__payment_method_enum__name": "Payment method enum", "@sage/xtrem-master-data/data_types__payment_term__name": "Condiciones de pago", "@sage/xtrem-master-data/data_types__payment_term_data_type__name": "Payment term data type", "@sage/xtrem-master-data/data_types__payment_term_discount_or_penalty_type_enum__name": "Payment term discount or penalty type enum", "@sage/xtrem-master-data/data_types__percentage__name": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/data_types__percentage_work_order_data_type__name": "Percentage work order data type", "@sage/xtrem-master-data/data_types__period_type_enum__name": "Period type enum", "@sage/xtrem-master-data/data_types__postcode_data_type__name": "Código postal", "@sage/xtrem-master-data/data_types__potency_percentage__name": "Potency percentage", "@sage/xtrem-master-data/data_types__preferred_process_enum__name": "Proceso preferente", "@sage/xtrem-master-data/data_types__price__name": "Precio", "@sage/xtrem-master-data/data_types__price_data_type__name": "Precio", "@sage/xtrem-master-data/data_types__price_in_sales_price__name": "Price in sales price", "@sage/xtrem-master-data/data_types__price_percentage__name": "Price percentage", "@sage/xtrem-master-data/data_types__quantity__name": "Cantidad", "@sage/xtrem-master-data/data_types__quantity_in_purchase_unit__name": "Cantidad en unidad de compra", "@sage/xtrem-master-data/data_types__quantity_in_sales_unit__name": "Cantidad en unidad de venta", "@sage/xtrem-master-data/data_types__quantity_in_stock_unit__name": "Cantidad en unidad de stock", "@sage/xtrem-master-data/data_types__quantity_in_unit__name": "Quantity in unit", "@sage/xtrem-master-data/data_types__quantity_in_volume_unit__name": "Quantity in volume unit", "@sage/xtrem-master-data/data_types__quantity_in_weight_unit__name": "Quantity in weight unit", "@sage/xtrem-master-data/data_types__reason_code__name": "Código de motivo", "@sage/xtrem-master-data/data_types__region_data_type__name": "Región", "@sage/xtrem-master-data/data_types__replenishment_method_enum__name": "Método de reaprovisionamiento", "@sage/xtrem-master-data/data_types__resource_cost__name": "Coste de recurso", "@sage/xtrem-master-data/data_types__resource_group_type_enum__name": "Tipo de grupo de recursos", "@sage/xtrem-master-data/data_types__run_time_data_type__name": "Run time data type", "@sage/xtrem-master-data/data_types__scrap_factor_percentage__name": "Scrap factor percentage", "@sage/xtrem-master-data/data_types__sequence_counter_definition_level_enum__name": "Sequence counter definition level enum", "@sage/xtrem-master-data/data_types__sequence_number__name": "Número de secuencia", "@sage/xtrem-master-data/data_types__sequence_number_reset_frequency_enum__name": "Sequence number reset frequency enum", "@sage/xtrem-master-data/data_types__sequence_number_type_enum__name": "Sequence number type enum", "@sage/xtrem-master-data/data_types__serial_number_management_enum__name": "Gestión de números de serie", "@sage/xtrem-master-data/data_types__serial_number_usage_enum__name": "Uso de número de serie", "@sage/xtrem-master-data/data_types__setup_time_data_type__name": "Setup time data type", "@sage/xtrem-master-data/data_types__shift_data_type__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/data_types__standard_property_data_type__name": "Standard property data type", "@sage/xtrem-master-data/data_types__stock_management_mode_enum__name": "Stock management mode enum", "@sage/xtrem-master-data/data_types__stock_quantity__name": "Cantidad de stock", "@sage/xtrem-master-data/data_types__stock_quantity_variance_percentage__name": "Porcentaje de desviación de cantidad de stock", "@sage/xtrem-master-data/data_types__stock_variation_value__name": "Stock variation value", "@sage/xtrem-master-data/data_types__supplier__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/data_types__supplier_item_property_data_type__name": "Supplier item property data type", "@sage/xtrem-master-data/data_types__supplier_type_enum__name": "Supplier type enum", "@sage/xtrem-master-data/data_types__symbol_data_type__name": "Símbolo", "@sage/xtrem-master-data/data_types__tax_calculation_status_enum__name": "Estado de cálculo de impuestos", "@sage/xtrem-master-data/data_types__telephone_number_data_type__name": "Telephone number data type", "@sage/xtrem-master-data/data_types__time_data_type__name": "Tiempo", "@sage/xtrem-master-data/data_types__time_zone__name": "Zona horaria", "@sage/xtrem-master-data/data_types__title_enum__name": "Title enum", "@sage/xtrem-master-data/data_types__unit_conversion_coefficient__name": "Coeficiente de conversión de unidades", "@sage/xtrem-master-data/data_types__unit_conversion_type_enum__name": "Unit conversion type enum", "@sage/xtrem-master-data/data_types__unit_of_measure__name": "Unidad de medida", "@sage/xtrem-master-data/data_types__unit_type_enum__name": "Unit type enum", "@sage/xtrem-master-data/data_types__version_data_type__name": "Versión", "@sage/xtrem-master-data/data_types__volume_percentage__name": "Porcentaj<PERSON>", "@sage/xtrem-master-data/data_types__week_days_enum__name": "Week days enum", "@sage/xtrem-master-data/data_types__weight_percentage__name": "Porcentaje de peso", "@sage/xtrem-master-data/data_types__work_in_progress_document_type_enum__name": "Work in progress document type enum", "@sage/xtrem-master-data/data_types__zone_type_enum__name": "Zone type enum", "@sage/xtrem-master-data/data-types/percentage__value_greater_than_a_maximum": "El valor de porcentaje ({{value}}) no debe ser superior a {{maxValue}}.", "@sage/xtrem-master-data/data-types/percentage__value_less_than_a_minimum": "El valor de porcentaje ({{value}}) debe ser superior a {{minValue}}.", "@sage/xtrem-master-data/data-types/percentage__value_not_in_allowed_range": "El valor de porcentaje ({{value}}) debe ser entre {{minValue}} y {{maxValue}}.", "@sage/xtrem-master-data/days": "día(s)", "@sage/xtrem-master-data/delete-confirmation": "El registro se ha eliminado.", "@sage/xtrem-master-data/delete-dialog-content": "¿Quieres eliminar este registro?", "@sage/xtrem-master-data/delete-dialog-title": "Confirmar eliminación", "@sage/xtrem-master-data/edit-create-customer-price": "<PERSON><PERSON><PERSON> pre<PERSON> de venta", "@sage/xtrem-master-data/edit-create-line": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/edit-create-supplier": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/edit-create-supplier-price": "<PERSON><PERSON><PERSON> pre<PERSON> de <PERSON>", "@sage/xtrem-master-data/email-validation-error": "La dirección de e-mail no es válida.", "@sage/xtrem-master-data/enums__address_entity_type__businessEntity": "Entidad empresarial", "@sage/xtrem-master-data/enums__address_entity_type__company": "Sociedad", "@sage/xtrem-master-data/enums__address_entity_type__customer": "Cliente", "@sage/xtrem-master-data/enums__address_entity_type__site": "Planta", "@sage/xtrem-master-data/enums__address_entity_type__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__approval_status__approved": "Approved", "@sage/xtrem-master-data/enums__approval_status__changeRequested": "Cambio solicitado", "@sage/xtrem-master-data/enums__approval_status__confirmed": "Confirmed", "@sage/xtrem-master-data/enums__approval_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__approval_status__pendingApproval": "Pendiente de aprobación", "@sage/xtrem-master-data/enums__approval_status__rejected": "Rejected", "@sage/xtrem-master-data/enums__base_display_status__approved": "Aprobado", "@sage/xtrem-master-data/enums__base_display_status__changeRequested": "Cambio solicitado", "@sage/xtrem-master-data/enums__base_display_status__closed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__confirmed": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__credited": "Credited", "@sage/xtrem-master-data/enums__base_display_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__error": "Error", "@sage/xtrem-master-data/enums__base_display_status__invoiced": "Facturado", "@sage/xtrem-master-data/enums__base_display_status__noVariance": "Sin desviación", "@sage/xtrem-master-data/enums__base_display_status__ordered": "Ordered", "@sage/xtrem-master-data/enums__base_display_status__paid": "Paid", "@sage/xtrem-master-data/enums__base_display_status__partiallyCredited": "Partially credited", "@sage/xtrem-master-data/enums__base_display_status__partiallyInvoiced": "Parcialmente <PERSON>", "@sage/xtrem-master-data/enums__base_display_status__partiallyOrdered": "Partially ordered", "@sage/xtrem-master-data/enums__base_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-master-data/enums__base_display_status__partiallyReceived": "Partially received", "@sage/xtrem-master-data/enums__base_display_status__partiallyReturned": "Partially returned", "@sage/xtrem-master-data/enums__base_display_status__partiallyShipped": "Parcialmente expedido", "@sage/xtrem-master-data/enums__base_display_status__pending": "Pendiente", "@sage/xtrem-master-data/enums__base_display_status__pendingApproval": "Pendiente de aprobación", "@sage/xtrem-master-data/enums__base_display_status__posted": "Posted", "@sage/xtrem-master-data/enums__base_display_status__postingError": "Error de contabilización", "@sage/xtrem-master-data/enums__base_display_status__postingInProgress": "Contabilización en curso", "@sage/xtrem-master-data/enums__base_display_status__quote": "Quote", "@sage/xtrem-master-data/enums__base_display_status__readyToProcess": "Listo para procesar", "@sage/xtrem-master-data/enums__base_display_status__readyToShip": "Listo para expedir", "@sage/xtrem-master-data/enums__base_display_status__received": "Recibido", "@sage/xtrem-master-data/enums__base_display_status__rejected": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_display_status__returned": "Returned", "@sage/xtrem-master-data/enums__base_display_status__shipped": "Expedido", "@sage/xtrem-master-data/enums__base_display_status__stockError": "Error de stock", "@sage/xtrem-master-data/enums__base_display_status__taxCalculationFailed": "Error de cálculo de impuestos", "@sage/xtrem-master-data/enums__base_display_status__variance": "Con desviación", "@sage/xtrem-master-data/enums__base_display_status__varianceApproved": "Desviación aprobada", "@sage/xtrem-master-data/enums__base_origin__direct": "Directo", "@sage/xtrem-master-data/enums__base_origin__invoice": "Factura", "@sage/xtrem-master-data/enums__base_origin__order": "Pedido", "@sage/xtrem-master-data/enums__base_origin__purchaseCreditMemo": "Factura rectificativa de compra", "@sage/xtrem-master-data/enums__base_origin__purchaseInvoice": "Factura de compra", "@sage/xtrem-master-data/enums__base_origin__purchaseOrder": "Pedido de compra", "@sage/xtrem-master-data/enums__base_origin__purchaseReceipt": "Recepción de compra", "@sage/xtrem-master-data/enums__base_origin__purchaseRequisition": "Solicitud de compra", "@sage/xtrem-master-data/enums__base_origin__purchaseReturn": "Devolución de compra", "@sage/xtrem-master-data/enums__base_origin__purchaseSuggestion": "Propuesta de compra", "@sage/xtrem-master-data/enums__base_origin__return": "Devolución", "@sage/xtrem-master-data/enums__base_origin__shipment": "Expedición", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__company": "Sociedad", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__day": "Día", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceAlpha": "Sequence alpha", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__site": "Planta", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__week": "Se<PERSON>", "@sage/xtrem-master-data/enums__base_sequence_number_component_type__year": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__approved": "Approved", "@sage/xtrem-master-data/enums__base_status__changeRequested": "Cambio solicitado", "@sage/xtrem-master-data/enums__base_status__closed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__confirmed": "Confirmed", "@sage/xtrem-master-data/enums__base_status__credited": "Credited", "@sage/xtrem-master-data/enums__base_status__draft": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__base_status__error": "Error", "@sage/xtrem-master-data/enums__base_status__inProgress": "En curso", "@sage/xtrem-master-data/enums__base_status__invoiced": "Invoiced", "@sage/xtrem-master-data/enums__base_status__noVariance": "Sin desviación", "@sage/xtrem-master-data/enums__base_status__partiallyCredited": "Partially credited", "@sage/xtrem-master-data/enums__base_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-master-data/enums__base_status__partiallyReceived": "Partially received", "@sage/xtrem-master-data/enums__base_status__partiallyReturned": "Partially returned", "@sage/xtrem-master-data/enums__base_status__partiallyShipped": "Partially shipped", "@sage/xtrem-master-data/enums__base_status__pending": "Pendiente", "@sage/xtrem-master-data/enums__base_status__pendingApproval": "Pendiente de aprobación", "@sage/xtrem-master-data/enums__base_status__posted": "Contabilizado", "@sage/xtrem-master-data/enums__base_status__postingError": "Error de contabilización", "@sage/xtrem-master-data/enums__base_status__postingInProgress": "Contabilización en curso", "@sage/xtrem-master-data/enums__base_status__quote": "Presupuesto", "@sage/xtrem-master-data/enums__base_status__readyToProcess": "Listo para procesar", "@sage/xtrem-master-data/enums__base_status__readyToShip": "Listo para expedir", "@sage/xtrem-master-data/enums__base_status__received": "Received", "@sage/xtrem-master-data/enums__base_status__rejected": "Rejected", "@sage/xtrem-master-data/enums__base_status__returned": "Returned", "@sage/xtrem-master-data/enums__base_status__shipped": "Expedido", "@sage/xtrem-master-data/enums__base_status__stockError": "Error de stock", "@sage/xtrem-master-data/enums__base_status__taxCalculationFailed": "Error de cálculo de impuestos", "@sage/xtrem-master-data/enums__base_status__variance": "Con desviación", "@sage/xtrem-master-data/enums__base_status__varianceApproved": "Desviación aprobada", "@sage/xtrem-master-data/enums__business_entity_type__all": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__business_entity_type__customer": "Cliente", "@sage/xtrem-master-data/enums__business_entity_type__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__business_relation_type__customer": "Cliente", "@sage/xtrem-master-data/enums__business_relation_type__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__consumption_mode__none": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__consumption_mode__quantity": "Cantidad", "@sage/xtrem-master-data/enums__consumption_mode__time": "Tiempo", "@sage/xtrem-master-data/enums__contact_role__commercialContact": "<PERSON>o comercial", "@sage/xtrem-master-data/enums__contact_role__financialContact": "Contacto financiero", "@sage/xtrem-master-data/enums__contact_role__mainContact": "Contacto principal", "@sage/xtrem-master-data/enums__container_type__barrel": "Barril", "@sage/xtrem-master-data/enums__container_type__bigBag": "Saco", "@sage/xtrem-master-data/enums__container_type__box": "Estuche", "@sage/xtrem-master-data/enums__container_type__container": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__other": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__container_type__pack": "Caja", "@sage/xtrem-master-data/enums__container_type__pallet": "Palé", "@sage/xtrem-master-data/enums__cost_calculation_method__compound": "Cascada", "@sage/xtrem-master-data/enums__cost_calculation_method__cumulate": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__budgeted": "Presupuestada", "@sage/xtrem-master-data/enums__cost_category_type__other": "Otra", "@sage/xtrem-master-data/enums__cost_category_type__simulated": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_category_type__standard": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__cost_valuation_method__averageCost": "Precio medio ponderado", "@sage/xtrem-master-data/enums__cost_valuation_method__fifoCost": "FIFO", "@sage/xtrem-master-data/enums__cost_valuation_method__standardCost": "<PERSON>ste <PERSON>", "@sage/xtrem-master-data/enums__customer_display_status__active": "Activo", "@sage/xtrem-master-data/enums__customer_display_status__inactive": "Inactivo", "@sage/xtrem-master-data/enums__customer_display_status__onHold": "Bloqueado", "@sage/xtrem-master-data/enums__customer_on_hold_type__blocking": "Bloqueo", "@sage/xtrem-master-data/enums__customer_on_hold_type__none": "Ninguna", "@sage/xtrem-master-data/enums__customer_on_hold_type__warning": "Aviso", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPrice": "<PERSON><PERSON> bruto", "@sage/xtrem-master-data/enums__discount_charge_calculation_basis__grossPriceAndCompound": "Precio bruto y cascada", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byLine": "<PERSON>r línea", "@sage/xtrem-master-data/enums__discount_charge_calculation_rule__byUnit": "Por unidad", "@sage/xtrem-master-data/enums__discount_charge_sign__decrease": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__discount_charge_sign__increase": "Aumentar", "@sage/xtrem-master-data/enums__discount_charge_value_type__amount": "Importe", "@sage/xtrem-master-data/enums__discount_charge_value_type__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/enums__discount_or_penalty_type__amount": "Importe", "@sage/xtrem-master-data/enums__discount_or_penalty_type__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDate": "A partir de fecha de factura", "@sage/xtrem-master-data/enums__due_date_type__afterInvoiceDateAndExtendedToEndOfMonth": "A partir de fecha de factura y hasta fin de mes", "@sage/xtrem-master-data/enums__due_date_type__afterTheEndOfTheMonthOfInvoiceDate": "A partir de fin de mes de fecha de factura", "@sage/xtrem-master-data/enums__email_action_type__approved": "Approved", "@sage/xtrem-master-data/enums__email_action_type__rejected": "Rejected", "@sage/xtrem-master-data/enums__item_category_type__allergen": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__item_category_type__ghsClassification": "Clasificación según SGA", "@sage/xtrem-master-data/enums__item_category_type__none": "Ninguna", "@sage/xtrem-master-data/enums__item_flow_type__manufactured": "Fabricado", "@sage/xtrem-master-data/enums__item_flow_type__purchased": "Comprado", "@sage/xtrem-master-data/enums__item_flow_type__sold": "Vendido", "@sage/xtrem-master-data/enums__item_flow_type__subcontracted": "Subcontratado", "@sage/xtrem-master-data/enums__item_price_type__discount": "Descuento", "@sage/xtrem-master-data/enums__item_price_type__normal": "Normal", "@sage/xtrem-master-data/enums__item_price_type__specialOffer": "Oferta especial", "@sage/xtrem-master-data/enums__item_status__active": "Activo", "@sage/xtrem-master-data/enums__item_status__inDevelopment": "En desarrollo", "@sage/xtrem-master-data/enums__item_status__notRenewed": "Sin renovar", "@sage/xtrem-master-data/enums__item_status__notUsable": "No utilizable", "@sage/xtrem-master-data/enums__item_status__obsolete": "Obsoleto", "@sage/xtrem-master-data/enums__item_type__good": "Bien", "@sage/xtrem-master-data/enums__item_type__landedCost": "Gastos de entrega", "@sage/xtrem-master-data/enums__item_type__service": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__legal_entity__corporation": "Persona jurídica", "@sage/xtrem-master-data/enums__legal_entity__physicalPerson": "Persona física", "@sage/xtrem-master-data/enums__location_category__customer": "Cliente", "@sage/xtrem-master-data/enums__location_category__dock": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__location_category__internal": "Interna", "@sage/xtrem-master-data/enums__location_category__subcontract": "Subcontratación", "@sage/xtrem-master-data/enums__location_category__virtual": "Virtual", "@sage/xtrem-master-data/enums__lot_management__lotManagement": "Lote", "@sage/xtrem-master-data/enums__lot_management__lotSublotManagement": "Lote y sublote", "@sage/xtrem-master-data/enums__lot_management__notManaged": "Sin gestionar", "@sage/xtrem-master-data/enums__order_type__closed": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__order_type__firm": "Firme", "@sage/xtrem-master-data/enums__order_type__planned": "Planificado", "@sage/xtrem-master-data/enums__order_type__suggested": "Propuesto", "@sage/xtrem-master-data/enums__payment_method__ACH": "ACH", "@sage/xtrem-master-data/enums__payment_method__cash": "Efectivo", "@sage/xtrem-master-data/enums__payment_method__creditCard": "Tarjeta de crédito", "@sage/xtrem-master-data/enums__payment_method__EFT": "TFE", "@sage/xtrem-master-data/enums__payment_method__printedCheck": "Cheque", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__amount": "Importe", "@sage/xtrem-master-data/enums__payment_term_discount_or_penalty_type__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/enums__payment_term_from__invoiceDate": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/enums__period_type__day": "Día", "@sage/xtrem-master-data/enums__period_type__month": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__period_type__week": "Se<PERSON>", "@sage/xtrem-master-data/enums__period_type__year": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__preferred_process__production": "Producción", "@sage/xtrem-master-data/enums__preferred_process__purchasing": "Compras", "@sage/xtrem-master-data/enums__replenishment_method__byMRP": "MRP", "@sage/xtrem-master-data/enums__replenishment_method__byReorderPoint": "Punto de pedido", "@sage/xtrem-master-data/enums__replenishment_method__notManaged": "Sin gestionar", "@sage/xtrem-master-data/enums__resource_group_type__labor": "<PERSON>o de obra", "@sage/xtrem-master-data/enums__resource_group_type__machine": "Máquina", "@sage/xtrem-master-data/enums__resource_group_type__subcontract": "Subcontratación", "@sage/xtrem-master-data/enums__resource_group_type__tool": "Herramienta", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__application": "Aplicación", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__company": "Sociedad", "@sage/xtrem-master-data/enums__sequence_counter_definition_level__site": "Planta", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__monthly": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__noReset": "Nunca", "@sage/xtrem-master-data/enums__sequence_number_reset_frequency__yearly": "Anualmente", "@sage/xtrem-master-data/enums__sequence_number_type__alphanumeric": "Alfanumérico", "@sage/xtrem-master-data/enums__sequence_number_type__numeric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__serial_number_management__managed": "Gestionado", "@sage/xtrem-master-data/enums__serial_number_management__notManaged": "Sin gestionar", "@sage/xtrem-master-data/enums__serial_number_usage__issueAndReceipt": "Entrada y salida", "@sage/xtrem-master-data/enums__serial_number_usage__issueOnly": "Solo salida", "@sage/xtrem-master-data/enums__stock_management_mode__byOrder": "Por pedido", "@sage/xtrem-master-data/enums__stock_management_mode__byProject": "Por proyecto", "@sage/xtrem-master-data/enums__stock_management_mode__onStock": "En stock", "@sage/xtrem-master-data/enums__supplier_type__chemical": "Industria química", "@sage/xtrem-master-data/enums__supplier_type__foodAndBeverage": "Industria de alimentación y bebidas", "@sage/xtrem-master-data/enums__supplier_type__other": "Otras industrias", "@sage/xtrem-master-data/enums__tax_calculation_status__done": "Calculado", "@sage/xtrem-master-data/enums__tax_calculation_status__failed": "Error", "@sage/xtrem-master-data/enums__tax_calculation_status__inProgress": "En curso", "@sage/xtrem-master-data/enums__tax_calculation_status__notDone": "Sin calcular", "@sage/xtrem-master-data/enums__title__dr": "Dr.", "@sage/xtrem-master-data/enums__title__family": "Familia", "@sage/xtrem-master-data/enums__title__master": "Master", "@sage/xtrem-master-data/enums__title__miss": "Miss", "@sage/xtrem-master-data/enums__title__mr": "Sr.", "@sage/xtrem-master-data/enums__title__mrs": "<PERSON><PERSON>.", "@sage/xtrem-master-data/enums__title__ms": "Srta.", "@sage/xtrem-master-data/enums__title__prof": "Prof.", "@sage/xtrem-master-data/enums__unit_conversion_type__other": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_conversion_type__purchase": "Compras", "@sage/xtrem-master-data/enums__unit_conversion_type__sales": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_type__area": "Á<PERSON>", "@sage/xtrem-master-data/enums__unit_type__each": "Numeración", "@sage/xtrem-master-data/enums__unit_type__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__unit_type__temperature": "Temperatura", "@sage/xtrem-master-data/enums__unit_type__time": "Tiempo", "@sage/xtrem-master-data/enums__unit_type__volume": "Volumen", "@sage/xtrem-master-data/enums__unit_type__weight": "Peso", "@sage/xtrem-master-data/enums__week_days__friday": "Viernes", "@sage/xtrem-master-data/enums__week_days__monday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__saturday": "Sábado", "@sage/xtrem-master-data/enums__week_days__sunday": "Domingo", "@sage/xtrem-master-data/enums__week_days__thursday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__tuesday": "<PERSON><PERSON>", "@sage/xtrem-master-data/enums__week_days__wednesday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__work_in_progress_document_type__materialNeed": "Necesidad de material", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseOrder": "Pedido de compra", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReceipt": "Recepción de compra", "@sage/xtrem-master-data/enums__work_in_progress_document_type__purchaseReturn": "Devolución de compra", "@sage/xtrem-master-data/enums__work_in_progress_document_type__salesOrder": "Pedido de venta", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferOrder": "Orden de transferencia de stock", "@sage/xtrem-master-data/enums__work_in_progress_document_type__stockTransferReceipt": "Recepción de transferencia de stock", "@sage/xtrem-master-data/enums__work_in_progress_document_type__workOrder": "Orden de fabricación", "@sage/xtrem-master-data/enums__zone_type__chemical": "Química", "@sage/xtrem-master-data/enums__zone_type__frozen": "Congelación", "@sage/xtrem-master-data/enums__zone_type__hazard": "P<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__magnetic": "Magnética", "@sage/xtrem-master-data/enums__zone_type__restricted": "Restringida", "@sage/xtrem-master-data/enums__zone_type__secured": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/enums__zone_type__sensitive": "Sensible", "@sage/xtrem-master-data/enums__zone_type__virtual": "Virtual", "@sage/xtrem-master-data/events__control__document_external_note_must_be_empty": "La nota externa debe estar en blanco si la propiedad \"isExternalNote\" es falsa.", "@sage/xtrem-master-data/events/control__address-control__postalcode-validation-error": "El código postal no es válido.", "@sage/xtrem-master-data/events/control__address-control__postcode-validation-error": "El código postal no es válido.", "@sage/xtrem-master-data/events/control__address-control__telephone-validation-error": "El número de teléfono no es válido.", "@sage/xtrem-master-data/events/control__address-control__zipcode-validation-error": "El código postal no es válido.", "@sage/xtrem-master-data/events/control__base_sequence_number_control_length": "The component sequence length needs to be the same as one of these values: {{lengths}}.", "@sage/xtrem-master-data/events/control__base_sequence_number_control_type": "The component type is not authorized: {{type}}.", "@sage/xtrem-master-data/events/control__business-entity__customer_already_exists_with_same_name": "Ya existe un cliente con este nombre.", "@sage/xtrem-master-data/events/control__business-entity__site_already_exists_with_same_name": "Ya existe una planta con este nombre.", "@sage/xtrem-master-data/events/control__business-entity__supplier_already_exists_with_same_name": "Ya existe un proveedor con este nombre.", "@sage/xtrem-master-data/events/control__cost-category__the_standard_cost_category_must_be_mandatory": "Introduce una categoría de coste estándar.", "@sage/xtrem-master-data/events/control__item__allergens-not-allowed": "Los alérgenos solo se pueden seleccionar para artículos que sean alimentos.", "@sage/xtrem-master-data/events/control__item__cannot-be-bom-revision-managed": "Un artículo con gestión de revisión de estructura de materiales debe ser fabricado.", "@sage/xtrem-master-data/events/control__item__cannot-be-phantom": "Un artículo fantasma se debe gestionar en stock y ser fabricado. No puede ser comprado ni vendido.", "@sage/xtrem-master-data/events/control__item__code-must-be-a-number": "El código {{ean<PERSON><PERSON>}} debe tener 13 dígitos.", "@sage/xtrem-master-data/events/control__item__empty-preferredProcess": "Un artículo de stock debe haberse fabricado, comprado o ambas cosas.", "@sage/xtrem-master-data/events/control__item__ghs-classification-not-allowed": "La clasificación según SGA solo se puede seleccionar para los artículos que sean químicos.", "@sage/xtrem-master-data/events/control__item__incorrect-value-for-economic-quantity": "Introduce una cantidad económica de pedido que sea múltiplo de la cantidad de tanda.", "@sage/xtrem-master-data/events/control__item__must-be-service": "El artículo debe ser un servicio.", "@sage/xtrem-master-data/events/control__item__must-be-service-or-landed-cost": "El artículo debe ser un servicio o gastos de entrega.", "@sage/xtrem-master-data/events/control__item__must-not-be-service": "El artículo no debe ser un servicio.", "@sage/xtrem-master-data/events/control__item__must-not-be-service-or-landed-cost": "El artículo no puede ser un servicio o gastos de entrega.", "@sage/xtrem-master-data/events/control__item__property-incorrect-for-not-stock-managed-items": "La propiedad {{property}} no es la que se utiliza para los artículos no gestionados en stock.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-and-landed-cost-items": "El campo \"{{property}}\" no está disponible para los artículos que son servicios o gastos de entrega.", "@sage/xtrem-master-data/events/control__item__property-not-managed-for-service-items": "La propiedad {{property}} no se gestiona para los artículos que son servicios.", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect": "Si el artículo es comprado o fabricado, el proceso preferente es obligatorio", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-manufacturing-items": "El proceso preferente para los artículos de stock que no estén fabricados no puede ser \"Producción\".", "@sage/xtrem-master-data/events/control__item_site__preferredProcess-incorrect-for-non-purchasing-items": "El proceso preferente para los artículos de stock que no estén fabricados no puede ser \"Compras\".", "@sage/xtrem-master-data/events/control__location_sequence_control__range_item_length": "The component sequence length needs to be the same as the component length.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_capital_letters": "El componente alfabético de un número de secuencia solo puede tener mayúsculas.", "@sage/xtrem-master-data/events/control__location_sequence_control_alpha_range": "El valor de inicio no puede ser superior al valor de fin.", "@sage/xtrem-master-data/events/control__location_sequence_control_number_value": "El componente numérico de un número de secuencia solo puede tener números.", "@sage/xtrem-master-data/events/control__location_sequence_control_numeric_range": "El valor de inicio no puede ser superior al valor de fin.", "@sage/xtrem-master-data/events/control__sequence-number__force-reset-with-tenant": "El número de secuencia se pone a cero cuando restableces la instancia.", "@sage/xtrem-master-data/events/control__time-control__end-date-cannot-be-empty": "Introduce la fecha de fin.", "@sage/xtrem-master-data/events/control__time-control__end-datetime-cannot-be-empty": "Introduce la hora de fin.", "@sage/xtrem-master-data/events/control__time-control__invalid-date-range": "El rango de fechas de {{start}} a {{end}} no es válido.", "@sage/xtrem-master-data/events/control__time-control__invalid-datetime-range": "El rango de horas de {{start}} a {{end}} no es válido.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-empty": "Introduce la fecha de inicio.", "@sage/xtrem-master-data/events/control__time-control__start-date-cannot-be-greater-than-end-date": "La fecha de inicio debe ser anterior a la fecha de fin.", "@sage/xtrem-master-data/events/control__time-control__start-datetime-cannot-be-empty": "Introduce la hora de inicio.", "@sage/xtrem-master-data/events/control__time-control__time-cannot-be-empty": "Introduce la hora.", "@sage/xtrem-master-data/events/control__time-control__time-format-HH-MM": "El formato de la hora {{timeToValidate}} debe ser HH:MM.", "@sage/xtrem-master-data/found-matching-business-entities": "Se han encontrado entidades empresariales coincidentes.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description": "Documentos imprimidos: {{numberOfDocuments}}", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_description_error": "No se han imprimido todos los documentos. Para más información, consulta el historial de tareas por lotes.", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_fail": "Error de impresión del informe {{reportName}}", "@sage/xtrem-master-data/function__common_send_client_bulk_notification_title_success": "Informe {{reportName}} imprimido", "@sage/xtrem-master-data/function__customer_price_reason_priority_already_exists": "Esta prioridad ya tiene un motivo de precio para cliente.", "@sage/xtrem-master-data/functions__business_entity__incorrect_format_siret": "El formato no es correcto. Utiliza el formato del número SIRET: {{format}}.", "@sage/xtrem-master-data/functions__business_entity__not-a-valid-tax-id": "El formato no es correcto. Utiliza el formato del NIF-IVA: {{format}}.", "@sage/xtrem-master-data/functions__common__download_file": "Descargar archivo", "@sage/xtrem-master-data/functions__common__history": "Historial", "@sage/xtrem-master-data/functions__common__invalid_characters": "El número de secuencia contiene caracteres que no son válidos.", "@sage/xtrem-master-data/functions__common__sequence_number_id_is_in_use": "Asigna un número de secuencia que no se esté utilizando.", "@sage/xtrem-master-data/functions__common__sequence_number_id_must_have_two_characters": "El número de secuencia debe contener dos caracteres.", "@sage/xtrem-master-data/functions__exchange-rate__no-rate-found-for-this-currency-pair": "No se ha encontrado ningún tipo de cambio para esta pareja de divisas.", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-enum-value": "El valor {{enumValue}} no es correcto.", "@sage/xtrem-master-data/functions__sequence-number-lib__invalid-length": "La longitud de {{sequenceNumberId}} no es correcta.", "@sage/xtrem-master-data/functions__sequence-number-lib__no_company_sequence_number_value_defined": "Introduce el número de secuencia de la sociedad {{companyId}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__no_site_sequence_number_value_defined": "Introduce el número de secuencia de la planta {{siteId}}.", "@sage/xtrem-master-data/functions__sequence-number-lib__no-component-of-type": "{{sequenceNumberId}}: sin componente de tipo {{componentType}}", "@sage/xtrem-master-data/functions__sequence-number-lib__sequence-number-exceeded": "El número de secuencia {{sequenceNumberId}} se ha sobrepasado.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__different-unit-type": "Las unidades {{fromUnit}} y {{toUnit}} no son del mismo tipo.", "@sage/xtrem-master-data/functions__unit-of-measure-lib__no-factors-for-the-units": "No se ha definido ningún coeficiente de conversión entre las unidades {{fromUnit}} y {{toUnit}}.", "@sage/xtrem-master-data/generate": "Generar", "@sage/xtrem-master-data/info": "Información", "@sage/xtrem-master-data/invalid-period": "El periodo {{dates}} no es correcto.", "@sage/xtrem-master-data/invalid-quantity-range": "El rango de cantidades {{qtyRange}} no es correcto.", "@sage/xtrem-master-data/item__price-cannot-be-negative": "El precio no puede ser negativo.", "@sage/xtrem-master-data/item-not-sold": "Define el artículo {{itemName}} como vendido.", "@sage/xtrem-master-data/item-site-supplier-record-created": "Proveed<PERSON> de artículo-planta creado", "@sage/xtrem-master-data/item-site-supplier-updated.": "Proveedor de artículo-planta actualizado", "@sage/xtrem-master-data/item-site-updated.": "Artículo-planta actualizado", "@sage/xtrem-master-data/mailer_no_mailer_redirect_url_provided": "Introduce una URL de redireccionamiento para el e-mail en el archivo de configuración.", "@sage/xtrem-master-data/menu_item__declarations": "Declaraciones", "@sage/xtrem-master-data/menu_item__dev-tools": "Herramientas de desarrollo", "@sage/xtrem-master-data/menu_item__employee": "Trabajador", "@sage/xtrem-master-data/menu_item__features": "Configuración de funcionalidades", "@sage/xtrem-master-data/menu_item__features-inventory": "Stock", "@sage/xtrem-master-data/menu_item__features-items": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__features-manufacturing": "", "@sage/xtrem-master-data/menu_item__features-purchasing": "Compras", "@sage/xtrem-master-data/menu_item__features-resources": "Recursos", "@sage/xtrem-master-data/menu_item__features-sales": "Ventas", "@sage/xtrem-master-data/menu_item__features-stock": "Stock", "@sage/xtrem-master-data/menu_item__finance": "Contabilidad", "@sage/xtrem-master-data/menu_item__integrations": "Integraciones", "@sage/xtrem-master-data/menu_item__inventory": "Stock", "@sage/xtrem-master-data/menu_item__inventory-data": "Datos de stock", "@sage/xtrem-master-data/menu_item__item-data": "Datos de artículo", "@sage/xtrem-master-data/menu_item__items": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/menu_item__licence-plate-data": "Datos de número de contenedor interno", "@sage/xtrem-master-data/menu_item__location-data": "Datos de ubicación", "@sage/xtrem-master-data/menu_item__manufacturing": "Producción", "@sage/xtrem-master-data/menu_item__purchasing": "Compras", "@sage/xtrem-master-data/menu_item__resources": "Recursos", "@sage/xtrem-master-data/menu_item__resources-data": "Datos de recurso", "@sage/xtrem-master-data/menu_item__sales": "Ventas", "@sage/xtrem-master-data/menu_item__stock": "Stock", "@sage/xtrem-master-data/menu_item__stock-data": "Datos de stock", "@sage/xtrem-master-data/multiple-existing-business-entities": "Se han encontrado varias entidades empresariales coincidentes. Selecciona una del campo \"Entidad empresarial\".", "@sage/xtrem-master-data/node__base_document__no_validation_email_allowed": "There is no email validation allowed {{document}}", "@sage/xtrem-master-data/node_base_resource_location_site_mismatch": "La ubicación debe tener la misma planta que el recurso.", "@sage/xtrem-master-data/node-extensions__company_extension__property__addresses": "Direcciones", "@sage/xtrem-master-data/node-extensions__company_extension__property__contacts": "Contactos", "@sage/xtrem-master-data/node-extensions__company_extension__property__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__company_extension__property__currency": "Divisa", "@sage/xtrem-master-data/node-extensions__company_extension__property__customerOnHoldCheck": "Gestión de cliente bloqueado", "@sage/xtrem-master-data/node-extensions__company_extension__property__isSequenceNumberIdUsed": "Id. de número de secuencia utilizado", "@sage/xtrem-master-data/node-extensions__company_extension__property__priceScale": "Escala de precios", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryAddress": "Dirección principal", "@sage/xtrem-master-data/node-extensions__company_extension__property__primaryContact": "Principal", "@sage/xtrem-master-data/node-extensions__company_extension__property__sequenceNumberId": "Id. de número de secuencia", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser": "Disponible para usuario actual", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__nodeName": "Nombre de nodo", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__options": "Opciones", "@sage/xtrem-master-data/node-extensions__company_extension__query__isAccessibleForCurrentUser__parameter__propertyOrOperation": "Propiedad u operación", "@sage/xtrem-master-data/node-extensions__country_extension__property__currency": "Divisa", "@sage/xtrem-master-data/node-extensions__site_extension__property__businessEntity": "Entidad empresarial", "@sage/xtrem-master-data/node-extensions__site_extension__property__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__currency": "Divisa", "@sage/xtrem-master-data/node-extensions__site_extension__property__defaultLocation": "Ubicación por defecto", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialCurrency": "Divisa financiera", "@sage/xtrem-master-data/node-extensions__site_extension__property__financialSite": "Planta financiera", "@sage/xtrem-master-data/node-extensions__site_extension__property__isFinance": "Contabilidad", "@sage/xtrem-master-data/node-extensions__site_extension__property__isInventory": "Stock", "@sage/xtrem-master-data/node-extensions__site_extension__property__isLocationManaged": "Gestión de ubicación", "@sage/xtrem-master-data/node-extensions__site_extension__property__isManufacturing": "Producción", "@sage/xtrem-master-data/node-extensions__site_extension__property__isProjectManagement": "Gestión de proyectos", "@sage/xtrem-master-data/node-extensions__site_extension__property__isPurchase": "Compra", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSales": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/node-extensions__site_extension__property__isSequenceNumberIdUsed": "Id. de número de secuencia utilizado", "@sage/xtrem-master-data/node-extensions__site_extension__property__itemSites": "Artículos-plantas", "@sage/xtrem-master-data/node-extensions__site_extension__property__primaryAddress": "Dirección principal", "@sage/xtrem-master-data/node-extensions__site_extension__property__sequenceNumberId": "Id. de número de secuencia", "@sage/xtrem-master-data/node-extensions__site_extension__property__siret": "SIRET", "@sage/xtrem-master-data/node-extensions__site_extension__property__stockSite": "Planta de stock", "@sage/xtrem-master-data/node-extensions__site_extension__property__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/node-extensions__site_extension__property__timeZone": "Zona horaria", "@sage/xtrem-master-data/node-extensions__site_extension__query__timezones": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__address__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__address__node_name": "Dirección", "@sage/xtrem-master-data/nodes__address__property__addressLine1": "Línea de dirección 1", "@sage/xtrem-master-data/nodes__address__property__addressLine2": "Línea de dirección 2", "@sage/xtrem-master-data/nodes__address__property__city": "Ciudad", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddress": "Dirección asociada", "@sage/xtrem-master-data/nodes__address__property__concatenatedAddressWithoutName": "Dirección asociada sin nombre", "@sage/xtrem-master-data/nodes__address__property__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address__property__locationPhoneNumber": "Número de teléfono de ubicación", "@sage/xtrem-master-data/nodes__address__property__name": "Nombre", "@sage/xtrem-master-data/nodes__address__property__postcode": "Código postal", "@sage/xtrem-master-data/nodes__address__property__region": "Región", "@sage/xtrem-master-data/nodes__address_base__node_name": "Dirección base", "@sage/xtrem-master-data/nodes__address_base__property__address": "Dirección", "@sage/xtrem-master-data/nodes__address_base__property__addressLine1": "Línea de dirección 1", "@sage/xtrem-master-data/nodes__address_base__property__addressLine2": "Línea de dirección 2", "@sage/xtrem-master-data/nodes__address_base__property__city": "Ciudad", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddress": "Dirección asociada", "@sage/xtrem-master-data/nodes__address_base__property__concatenatedAddressWithoutName": "Dirección asociada sin nombre", "@sage/xtrem-master-data/nodes__address_base__property__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__address_base__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__address_base__property__locationPhoneNumber": "Número de teléfono de ubicación", "@sage/xtrem-master-data/nodes__address_base__property__name": "Nombre", "@sage/xtrem-master-data/nodes__address_base__property__postcode": "Código postal", "@sage/xtrem-master-data/nodes__address_base__property__region": "Región", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__allergen__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__allergen__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__allergen__property__id": "Id.", "@sage/xtrem-master-data/nodes__allergen__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__allergen__property__name": "Nombre", "@sage/xtrem-master-data/nodes__allergen__property__pictogram": "Pictograma", "@sage/xtrem-master-data/nodes__base_business_relation__node_name": "Relación de negocio base", "@sage/xtrem-master-data/nodes__base_business_relation__property__businessEntity": "Entidad empresarial", "@sage/xtrem-master-data/nodes__base_business_relation__property__category": "Categoría", "@sage/xtrem-master-data/nodes__base_business_relation__property__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_business_relation__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__base_business_relation__property__id": "Id.", "@sage/xtrem-master-data/nodes__base_business_relation__property__image": "Imagen", "@sage/xtrem-master-data/nodes__base_business_relation__property__internalNote": "Nota interna", "@sage/xtrem-master-data/nodes__base_business_relation__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__base_business_relation__property__legalEntity": "Entidad jurídica", "@sage/xtrem-master-data/nodes__base_business_relation__property__minimumOrderAmount": "Importe mínimo de pedido", "@sage/xtrem-master-data/nodes__base_business_relation__property__name": "Nombre", "@sage/xtrem-master-data/nodes__base_business_relation__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryAddress": "Dirección principal", "@sage/xtrem-master-data/nodes__base_business_relation__property__primaryContact": "Contacto principal", "@sage/xtrem-master-data/nodes__base_business_relation__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__base_business_relation__property__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/nodes__base_capability__node_name": "Aptitud base", "@sage/xtrem-master-data/nodes__base_capability__property__capabilityLevel": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/nodes__base_capability__property__dateEndValid": "Fecha de fin de validez", "@sage/xtrem-master-data/nodes__base_capability__property__dateRangeValidity": "<PERSON>ngo de fech<PERSON> de validez", "@sage/xtrem-master-data/nodes__base_capability__property__dateStartValid": "Fecha de inicio de validez", "@sage/xtrem-master-data/nodes__base_capability__property__id": "Id.", "@sage/xtrem-master-data/nodes__base_capability__property__name": "Nombre", "@sage/xtrem-master-data/nodes__base_certificate__node_name": "Certificado base", "@sage/xtrem-master-data/nodes__base_certificate__property__certificationBody": "Organismo de certificación", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfCertification": "Fecha de certificación", "@sage/xtrem-master-data/nodes__base_certificate__property__dateOfOriginalCertification": "Fecha de certificación original", "@sage/xtrem-master-data/nodes__base_certificate__property__id": "Id.", "@sage/xtrem-master-data/nodes__base_certificate__property__standard": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_certificate__property__validUntil": "<PERSON><PERSON><PERSON><PERSON> hasta", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo": "Actualizar lín<PERSON>", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentIds": "Ids. de documentos", "@sage/xtrem-master-data/nodes__base_document__asyncMutation__updateLineTo__parameter__documentLineIds": "Ids. de líneas de documento", "@sage/xtrem-master-data/nodes__base_document__bulkMutation__bulkResync": "Bulk resync", "@sage/xtrem-master-data/nodes__base_document__header_currency_not_updatable": "No puedes cambiar la divisa de este registro.", "@sage/xtrem-master-data/nodes__base_document__id_already_exists": "El id. ya existe. No se asignará ningún número de secuencia a este documento.", "@sage/xtrem-master-data/nodes__base_document__lines_mandatory": "The document needs at least one line.", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail": "Enviar solicitud de aprobación", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__document": "Documento", "@sage/xtrem-master-data/nodes__base_document__mutation__sendApprovalRequestMail__parameter__user": "Usuario", "@sage/xtrem-master-data/nodes__base_document__no_financial_site": "No se ha encontrado ninguna planta financiera para la sociedad actual.", "@sage/xtrem-master-data/nodes__base_document__node_name": "Documento base", "@sage/xtrem-master-data/nodes__base_document__order_date_not_updatable": "No puedes cambiar la fecha de pedido si el registro está en curso.", "@sage/xtrem-master-data/nodes__base_document__property__approvalPage": "Página de aprobación", "@sage/xtrem-master-data/nodes__base_document__property__approvalStatus": "Estado de aprobación", "@sage/xtrem-master-data/nodes__base_document__property__approvalUrl": "URL de aprobación", "@sage/xtrem-master-data/nodes__base_document__property__businessEntityAddress": "Dirección de entidad empresarial", "@sage/xtrem-master-data/nodes__base_document__property__canPrint": "Imprimible", "@sage/xtrem-master-data/nodes__base_document__property__canUpdateClosedDocument": "Se puede actualizar documento cerrado", "@sage/xtrem-master-data/nodes__base_document__property__companyCurrency": "Divisa de sociedad", "@sage/xtrem-master-data/nodes__base_document__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__base_document__property__date": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__displayStatus": "Estado", "@sage/xtrem-master-data/nodes__base_document__property__documentDate": "Fecha de documento", "@sage/xtrem-master-data/nodes__base_document__property__documentUrl": "URL de documento", "@sage/xtrem-master-data/nodes__base_document__property__externalNote": "Nota externa", "@sage/xtrem-master-data/nodes__base_document__property__financialSite": "Planta financiera", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForResync": "Forzar actualización de resincronización", "@sage/xtrem-master-data/nodes__base_document__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-master-data/nodes__base_document__property__internalNote": "Nota interna", "@sage/xtrem-master-data/nodes__base_document__property__isExternalNote": "Nota externa", "@sage/xtrem-master-data/nodes__base_document__property__isOverwriteNote": "Nota sobrescrita", "@sage/xtrem-master-data/nodes__base_document__property__isPrinted": "Impreso", "@sage/xtrem-master-data/nodes__base_document__property__isSent": "Enviado", "@sage/xtrem-master-data/nodes__base_document__property__isTransferHeaderNote": "Nota de documento añadida", "@sage/xtrem-master-data/nodes__base_document__property__isTransferLineNote": "Nota de línea a<PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__lines": "Líneas", "@sage/xtrem-master-data/nodes__base_document__property__number": "Número", "@sage/xtrem-master-data/nodes__base_document__property__page": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document__property__postingDate": "Fecha de contabilización", "@sage/xtrem-master-data/nodes__base_document__property__site": "Planta", "@sage/xtrem-master-data/nodes__base_document__property__siteAddress": "Dirección de planta", "@sage/xtrem-master-data/nodes__base_document__property__status": "Estado", "@sage/xtrem-master-data/nodes__base_document__property__stockSite": "Planta de stock", "@sage/xtrem-master-data/nodes__base_document__property__text": "Texto", "@sage/xtrem-master-data/nodes__base_document__property__transactionCurrency": "Divisa de transacción", "@sage/xtrem-master-data/nodes__base_document__update_not_allowed_status_closed": "El documento {{number}} está cerrado. No puedes actualizar el registro.", "@sage/xtrem-master-data/nodes__base_document_item_line__node_name": "Línea de artículo de documento base", "@sage/xtrem-master-data/nodes__base_document_item_line__property__document": "Documento", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentId": "Id. de documento", "@sage/xtrem-master-data/nodes__base_document_item_line__property__documentNumber": "Número de documento", "@sage/xtrem-master-data/nodes__base_document_item_line__property__externalNote": "Nota externa", "@sage/xtrem-master-data/nodes__base_document_item_line__property__forceUpdateForStock": "Forzar actualización de Stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__internalNote": "Nota interna", "@sage/xtrem-master-data/nodes__base_document_item_line__property__isExternalNote": "Nota externa", "@sage/xtrem-master-data/nodes__base_document_item_line__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemDescription": "Descripción de artículo", "@sage/xtrem-master-data/nodes__base_document_item_line__property__itemSite": "Artículo-planta", "@sage/xtrem-master-data/nodes__base_document_item_line__property__origin": "Origen", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantity": "Cantidad", "@sage/xtrem-master-data/nodes__base_document_item_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__site": "Planta", "@sage/xtrem-master-data/nodes__base_document_item_line__property__siteLinkedAddress": "Dirección de planta", "@sage/xtrem-master-data/nodes__base_document_item_line__property__status": "Estado", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSite": "Planta de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockSiteLinkedAddress": "Dirección de planta de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unit": "Unidad", "@sage/xtrem-master-data/nodes__base_document_item_line__property__unitToStockUnitConversionFactor": "Coeficiente de conversión de unidad a unidad de stock", "@sage/xtrem-master-data/nodes__base_document_line__node_name": "Línea de documento base", "@sage/xtrem-master-data/nodes__base_document_line__property__documentId": "Id. de documento", "@sage/xtrem-master-data/nodes__base_document_line__property__documentNumber": "Número de documento", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__node_name": "Consulta de línea de documento base", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__commodityCode": "Código de mercancías", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__company": "Sociedad", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__date": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__fromItem": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__itemCategory": "Categoría de artículo", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__lines": "Líneas", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__site": "Planta", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__toItem": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_document_line_inquiry__property__user": "Usuario", "@sage/xtrem-master-data/nodes__base_line_discount_charge__improper_calculation_rule": "La regla de cálculo no es correcta. Utiliza la regla por unidad para los porcentajes.", "@sage/xtrem-master-data/nodes__base_line_discount_charge__node_name": "Gasto o descuento de línea base", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__amount": "Importe", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basis": "Base imponible", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__basisDeterminated": "Base determinada", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationBasis": "Base de cálculo", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__calculationRule": "Regla de cálculo", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__sign": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__value": "Valor", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueDeterminated": "Valor determinado", "@sage/xtrem-master-data/nodes__base_line_discount_charge__property__valueType": "<PERSON><PERSON><PERSON> de valor", "@sage/xtrem-master-data/nodes__base_line_to_line__node_name": "De línea base a línea", "@sage/xtrem-master-data/nodes__base_line_to_line__property__amount": "Importe", "@sage/xtrem-master-data/nodes__base_line_to_line__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__base_line_to_line__property__from": "De", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantity": "Cantidad", "@sage/xtrem-master-data/nodes__base_line_to_line__property__quantityInStockUnit": "Cantidad en unidad de stock", "@sage/xtrem-master-data/nodes__base_line_to_line__property__stockUnit": "Unidad de stock", "@sage/xtrem-master-data/nodes__base_line_to_line__property__to": "A", "@sage/xtrem-master-data/nodes__base_line_to_line__property__unit": "Unidad", "@sage/xtrem-master-data/nodes__base_resource__node_name": "Recurso base", "@sage/xtrem-master-data/nodes__base_resource__property__activeFrom": "Activo desde", "@sage/xtrem-master-data/nodes__base_resource__property__activeRange": "Rango de fechas de activación", "@sage/xtrem-master-data/nodes__base_resource__property__activeTo": "Activo hasta", "@sage/xtrem-master-data/nodes__base_resource__property__description": "Descripción", "@sage/xtrem-master-data/nodes__base_resource__property__efficiency": "Rendimiento", "@sage/xtrem-master-data/nodes__base_resource__property__id": "Id.", "@sage/xtrem-master-data/nodes__base_resource__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__base_resource__property__location": "Ubicación", "@sage/xtrem-master-data/nodes__base_resource__property__name": "Nombre", "@sage/xtrem-master-data/nodes__base_resource__property__resourceCostCategories": "Categorías de costes de recursos", "@sage/xtrem-master-data/nodes__base_resource__property__resourceImage": "Imagen de recurso", "@sage/xtrem-master-data/nodes__base_resource__property__site": "Planta", "@sage/xtrem-master-data/nodes__base_resource__property__weeklyShift": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number__node_name": "Base sequence number", "@sage/xtrem-master-data/nodes__base_sequence_number__property__componentLength": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number__property__components": "Componentes", "@sage/xtrem-master-data/nodes__base_sequence_number__property__definitionLevel": "Nivel de definición", "@sage/xtrem-master-data/nodes__base_sequence_number__property__id": "Id", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isChronological": "Cronológico", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isClearedByReset": "Puesto a cero por restablecimiento", "@sage/xtrem-master-data/nodes__base_sequence_number__property__isUsed": "En uso", "@sage/xtrem-master-data/nodes__base_sequence_number__property__legislation": "Legislación", "@sage/xtrem-master-data/nodes__base_sequence_number__property__minimumLength": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number__property__name": "Nombre", "@sage/xtrem-master-data/nodes__base_sequence_number__property__rtzLevel": "Rtz level", "@sage/xtrem-master-data/nodes__base_sequence_number__property__sequenceNumberAssignments": "Asignaciones de números de secuencia", "@sage/xtrem-master-data/nodes__base_sequence_number__property__type": "Tipo", "@sage/xtrem-master-data/nodes__base_sequence_number__query__getDocumentNodeNames": "Obtener nombres de nodos de documento", "@sage/xtrem-master-data/nodes__base_sequence_number_component__node_name": "Base sequence number component", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__base_sequence_number_component__property__type": "Tipo", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__bom_revision_sequence__asyncMutation__asyncExport__parameter__id": "Id", "@sage/xtrem-master-data/nodes__bom_revision_sequence__node_name": "BOM revision sequence", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__components": "Componentes", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isDefault": "Por defecto", "@sage/xtrem-master-data/nodes__bom_revision_sequence__property__isSequenceGenerated": "", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__asyncMutation__asyncExport__parameter__id": "Id", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__node_name": "BOM revision sequence component", "@sage/xtrem-master-data/nodes__bom_revision_sequence_component__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__business_entity__address_mandatory": "La entidad empresarial debe tener al menos una dirección.", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__business_entity__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__business_entity__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__business_entity__incorrect_format_siret": "El formato no es correcto. Utiliza el formato del número SIRET: {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__node_name": "Entidad empresarial", "@sage/xtrem-master-data/nodes__business_entity__not-a-valid-tax-id": "El formato no es correcto. Utiliza el formato del NIF-IVA: {{format}}.", "@sage/xtrem-master-data/nodes__business_entity__primary_address": "La entidad empresarial solo puede tener una dirección principal.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_active": "Activa la dirección principal de la entidad empresarial.", "@sage/xtrem-master-data/nodes__business_entity__primary_address_mandatory": "Asigna al menos una dirección principal a la entidad empresarial.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact": "La dirección debe tener al menos un contacto principal.", "@sage/xtrem-master-data/nodes__business_entity__primary_contact_active": "Activa el contacto principal de la dirección.", "@sage/xtrem-master-data/nodes__business_entity__property__addresses": "Direcciones", "@sage/xtrem-master-data/nodes__business_entity__property__contacts": "Contactos", "@sage/xtrem-master-data/nodes__business_entity__property__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__business_entity__property__customer": "Cliente", "@sage/xtrem-master-data/nodes__business_entity__property__id": "Id.", "@sage/xtrem-master-data/nodes__business_entity__property__image": "Imagen", "@sage/xtrem-master-data/nodes__business_entity__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__business_entity__property__isCustomer": "Cliente", "@sage/xtrem-master-data/nodes__business_entity__property__isSite": "Planta", "@sage/xtrem-master-data/nodes__business_entity__property__isSupplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__legalEntity": "Entidad jurídica", "@sage/xtrem-master-data/nodes__business_entity__property__name": "Nombre", "@sage/xtrem-master-data/nodes__business_entity__property__parent": "Entidad empresarial primaria", "@sage/xtrem-master-data/nodes__business_entity__property__primaryAddress": "Dirección principal", "@sage/xtrem-master-data/nodes__business_entity__property__primaryContact": "Principal", "@sage/xtrem-master-data/nodes__business_entity__property__siret": "SIRET", "@sage/xtrem-master-data/nodes__business_entity__property__site": "Planta", "@sage/xtrem-master-data/nodes__business_entity__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__business_entity__property__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/nodes__business_entity__property__website": "Sitio web", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__business_entity_address__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__business_entity_address__node_name": "Dirección de entidad empresarial", "@sage/xtrem-master-data/nodes__business_entity_address__property__businessEntity": "Entidad empresarial", "@sage/xtrem-master-data/nodes__business_entity_address__property__concatenatedAddress": "Dirección asociada", "@sage/xtrem-master-data/nodes__business_entity_address__property__contacts": "Contactos", "@sage/xtrem-master-data/nodes__business_entity_address__property__deliveryDetail": "Detalles de entrega", "@sage/xtrem-master-data/nodes__business_entity_address__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__business_entity_address__property__primaryContact": "Contacto principal", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__business_entity_contact__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__business_entity_contact__node_name": "Contacto de entidad empresarial", "@sage/xtrem-master-data/nodes__business_entity_contact__property__address": "Dirección", "@sage/xtrem-master-data/nodes__business_entity_contact__property__businessEntity": "Entidad empresarial", "@sage/xtrem-master-data/nodes__business_entity_contact__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__business-entity-type-control-customer": "Ya existen clientes con estas condiciones de pago.", "@sage/xtrem-master-data/nodes__business-entity-type-control-supplier": "Ya existen proveedores con estas condiciones de pago.", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__capability_level__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__capability_level__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__capability_level__node_name": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/nodes__capability_level__property__description": "Descripción", "@sage/xtrem-master-data/nodes__capability_level__property__id": "Id.", "@sage/xtrem-master-data/nodes__capability_level__property__level": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__capability_level__property__name": "Nombre", "@sage/xtrem-master-data/nodes__company__address_mandatory": "Asigna al menos una dirección a la sociedad.", "@sage/xtrem-master-data/nodes__company__primary_address": "La sociedad solo puede tener una dirección principal.", "@sage/xtrem-master-data/nodes__company__primary_address_mandatory": "Selecciona al menos una dirección principal para la sociedad.", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__company_address__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__company_address__node_name": "Dirección de sociedad", "@sage/xtrem-master-data/nodes__company_address__property__company": "Sociedad", "@sage/xtrem-master-data/nodes__company_address__property__contacts": "Contactos", "@sage/xtrem-master-data/nodes__company_address__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__company_contact__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__company_contact__node_name": "Contacto de sociedad", "@sage/xtrem-master-data/nodes__company_contact__property__address": "Dirección", "@sage/xtrem-master-data/nodes__company_contact__property__company": "Sociedad", "@sage/xtrem-master-data/nodes__company_contact__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__contact__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__contact__node_name": "Contacto", "@sage/xtrem-master-data/nodes__contact__property__email": "E-mail", "@sage/xtrem-master-data/nodes__contact__property__firstName": "Nombre", "@sage/xtrem-master-data/nodes__contact__property__image": "Imagen", "@sage/xtrem-master-data/nodes__contact__property__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact__property__locationPhoneNumber": "Número de teléfono de ubicación", "@sage/xtrem-master-data/nodes__contact__property__position": "Puesto", "@sage/xtrem-master-data/nodes__contact__property__preferredName": "Nombre preferente", "@sage/xtrem-master-data/nodes__contact__property__role": "Rol", "@sage/xtrem-master-data/nodes__contact__property__title": "Tratamiento", "@sage/xtrem-master-data/nodes__contact_base__node_name": "Contacto base", "@sage/xtrem-master-data/nodes__contact_base__not-a-valid-email": "{{email}} no es un e-mail válido.", "@sage/xtrem-master-data/nodes__contact_base__property__contact": "Contacto", "@sage/xtrem-master-data/nodes__contact_base__property__displayName": "Nombre visible", "@sage/xtrem-master-data/nodes__contact_base__property__email": "E-mail", "@sage/xtrem-master-data/nodes__contact_base__property__firstName": "Nombre", "@sage/xtrem-master-data/nodes__contact_base__property__image": "Imagen", "@sage/xtrem-master-data/nodes__contact_base__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__contact_base__property__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__contact_base__property__locationPhoneNumber": "Número de teléfono de ubicación", "@sage/xtrem-master-data/nodes__contact_base__property__position": "Puesto", "@sage/xtrem-master-data/nodes__contact_base__property__preferredName": "Nombre preferente", "@sage/xtrem-master-data/nodes__contact_base__property__role": "Tipo", "@sage/xtrem-master-data/nodes__contact_base__property__title": "Tratamiento", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__container__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__container__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__container__property__consumedLocationCapacity": "Capacidad de ubicación consumida", "@sage/xtrem-master-data/nodes__container__property__id": "Id.", "@sage/xtrem-master-data/nodes__container__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__container__property__isInternal": "Interno", "@sage/xtrem-master-data/nodes__container__property__isSingleItem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__container__property__isSingleLot": "Monolote", "@sage/xtrem-master-data/nodes__container__property__labelFormat": "Formato de etiqueta", "@sage/xtrem-master-data/nodes__container__property__name": "Nombre", "@sage/xtrem-master-data/nodes__container__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__container__property__storageCapacity": "Capacidad de almacenamiento", "@sage/xtrem-master-data/nodes__container__property__type": "Tipo", "@sage/xtrem-master-data/nodes__container__sequence-number-not-required": "El número de secuencia no es obligatorio en el contenedor externo.", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__cost_category__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__cost_category__node_name": "Categoría de coste", "@sage/xtrem-master-data/nodes__cost_category__property__costCategoryType": "Tipo de categoría de coste", "@sage/xtrem-master-data/nodes__cost_category__property__id": "Id.", "@sage/xtrem-master-data/nodes__cost_category__property__isMandatory": "Obligatoria", "@sage/xtrem-master-data/nodes__cost_category__property__name": "Nombre", "@sage/xtrem-master-data/nodes__cost-category__can-have-only-one-cost-type-of": "Solo puede haber un tipo de coste {{costCategoryType}}.", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__currency__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__currency__deleting-record": "Eliminando el registro {{exchangeRateId}} con {{exchangeRateLength}} cifras de los tipos de cambio.", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate": "Guardar tipo de cambio", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__base": "Base", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__dateRate": "Fecha de tipo de cambio", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__destination": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__mutation__saveExchangeRate__parameter__rate": "Tipo de cambio", "@sage/xtrem-master-data/nodes__currency__node_name": "Divisa", "@sage/xtrem-master-data/nodes__currency__property__currentExchangeRates": "Tipos de cambio actuales", "@sage/xtrem-master-data/nodes__currency__property__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__currency__property__exchangeRates": "Tipos de cambio", "@sage/xtrem-master-data/nodes__currency__property__exchangeRatesDestinationInverse": "Exchange rates destination inverse", "@sage/xtrem-master-data/nodes__currency__property__icon": "Icono", "@sage/xtrem-master-data/nodes__currency__property__id": "Id.", "@sage/xtrem-master-data/nodes__currency__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__currency__property__lastUpdate": "Última actualización", "@sage/xtrem-master-data/nodes__currency__property__name": "Nombre", "@sage/xtrem-master-data/nodes__currency__property__rounding": "Redondeo", "@sage/xtrem-master-data/nodes__currency__property__symbol": "Símbolo", "@sage/xtrem-master-data/nodes__currency_id": "El identificador debe contener tres caracteres.", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__customer__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__customer__at_least_one_active_delivery_address_mandatory": "Asigna al menos una dirección de entrega activa al cliente.", "@sage/xtrem-master-data/nodes__customer__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__customer__enter_a_shipping_address_for_customer": "Introduce una dirección de expedición para el cliente.", "@sage/xtrem-master-data/nodes__customer__incorrect_format_siret": "{{siret}} no es un número SIRET válido. El formato esperado es {{format}}.", "@sage/xtrem-master-data/nodes__customer__node_name": "Cliente", "@sage/xtrem-master-data/nodes__customer__not-a-valid-tax-id": "{{taxIdNumber}} no es un NIF-IVA válido. El formato esperado es {{format}}.", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address": "El cliente solo puede tener una dirección de expedición principal.", "@sage/xtrem-master-data/nodes__customer__primary_delivery_address_mandatory": "Asigna una dirección de expedición principal activa al cliente.", "@sage/xtrem-master-data/nodes__customer__primary_ship_to_address_mandatory": "El cliente debe tener al menos una dirección de expedición principal activa.", "@sage/xtrem-master-data/nodes__customer__property__billToAddress": "Dirección de facturación", "@sage/xtrem-master-data/nodes__customer__property__billToCustomer": "Cliente facturado", "@sage/xtrem-master-data/nodes__customer__property__category": "Categoría", "@sage/xtrem-master-data/nodes__customer__property__creditLimit": "Límite de crédito", "@sage/xtrem-master-data/nodes__customer__property__deliveryAddresses": "Direcciones de expedición", "@sage/xtrem-master-data/nodes__customer__property__displayStatus": "Estado", "@sage/xtrem-master-data/nodes__customer__property__isOnHold": "Bloqueado", "@sage/xtrem-master-data/nodes__customer__property__itemPrices": "Precios de artículos", "@sage/xtrem-master-data/nodes__customer__property__items": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer__property__payByAddress": "Dirección de pago", "@sage/xtrem-master-data/nodes__customer__property__payByCustomer": "Cliente pagador", "@sage/xtrem-master-data/nodes__customer__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-master-data/nodes__customer__property__primaryShipToAddress": "Dirección de expedición principal", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__customer_price_reason__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__customer_price_reason__node_name": "Motivo de precio para cliente", "@sage/xtrem-master-data/nodes__customer_price_reason__property__description": "Descripción", "@sage/xtrem-master-data/nodes__customer_price_reason__property__id": "Id.", "@sage/xtrem-master-data/nodes__customer_price_reason__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__customer_price_reason__property__name": "Nombre", "@sage/xtrem-master-data/nodes__customer_price_reason__property__priority": "Prioridad", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__customer_supplier_category__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__customer_supplier_category__change_not_possible_category_is_used_on_customer": "Solo puedes cambiar un tipo de categoría de cliente que no esté ya asignado a un cliente.", "@sage/xtrem-master-data/nodes__customer_supplier_category__node_name": "Categoría de proveedor y de cliente", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__id": "Id.", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isCustomer": "Cliente", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSequenceNumberManagement": "Gestión de números de secuencia", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__isSupplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__name": "Nombre", "@sage/xtrem-master-data/nodes__customer_supplier_category__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__customer-minimum__order_amount-cannot-be-negative": "El importe mínimo del pedido no puede ser negativo.", "@sage/xtrem-master-data/nodes__customer-supplier-category__customer_or_supplier": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" o \"Proveedor\".", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-cannot-be-set": "El número de secuencia no se puede definir.", "@sage/xtrem-master-data/nodes__customer-supplier-category__sequence-number-is-mandatory": "El número de secuencia es obligatorio.", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__daily_shift__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__daily_shift__no_details_on_full_day_shift": "Un turno de día completo no puede tener detalles del turno.", "@sage/xtrem-master-data/nodes__daily_shift__no_overlap_on_shift_details": "Los detalles de los turnos no se pueden superponer.", "@sage/xtrem-master-data/nodes__daily_shift__node_name": "Turno diario", "@sage/xtrem-master-data/nodes__daily_shift__property__capacity": "Capacidad", "@sage/xtrem-master-data/nodes__daily_shift__property__formattedCapacity": "Capacidad formateada", "@sage/xtrem-master-data/nodes__daily_shift__property__id": "Id.", "@sage/xtrem-master-data/nodes__daily_shift__property__isFullDay": "<PERSON>ía completo", "@sage/xtrem-master-data/nodes__daily_shift__property__name": "Nombre", "@sage/xtrem-master-data/nodes__daily_shift__property__shiftDetails": "Detalles de turno", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__daily_shift_detail__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__daily_shift_detail__node_name": "Detalles de turno diario", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__dailyShift": "Turno diario", "@sage/xtrem-master-data/nodes__daily_shift_detail__property__shiftDetail": "Detalles de turno", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__delivery_detail__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__delivery_detail__node_name": "Detalles de entrega", "@sage/xtrem-master-data/nodes__delivery_detail__property__address": "Dirección", "@sage/xtrem-master-data/nodes__delivery_detail__property__incoterm": "Incoterms", "@sage/xtrem-master-data/nodes__delivery_detail__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__delivery_detail__property__isFridayWorkDay": "Viernes laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__isMondayWorkDay": "<PERSON><PERSON> laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__isPrimary": "Principal", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSaturdayWorkDay": "Sábado laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__isSundayWorkDay": "Domingo laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__isThursdayWorkDay": "Jueves laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__isTuesdayWorkDay": "Martes laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__isWednesdayWorkDay": "Miércoles laborable", "@sage/xtrem-master-data/nodes__delivery_detail__property__leadTime": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__delivery_detail__property__mode": "Modo", "@sage/xtrem-master-data/nodes__delivery_detail__property__shipmentSite": "Planta de expedición", "@sage/xtrem-master-data/nodes__delivery_detail__property__workDaysSelection": "Selección de días laborables", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__delivery_mode__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__delivery_mode__node_name": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/nodes__delivery_mode__property__description": "Descripción", "@sage/xtrem-master-data/nodes__delivery_mode__property__id": "Id.", "@sage/xtrem-master-data/nodes__delivery_mode__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__delivery_mode__property__name": "Nombre", "@sage/xtrem-master-data/nodes__detailed_resource__node_name": "Recurso <PERSON>", "@sage/xtrem-master-data/nodes__detailed_resource__property__efficiency": "Rendimiento", "@sage/xtrem-master-data/nodes__detailed_resource__property__location": "Ubicación", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceCostCategories": "Categorías de costes de recursos", "@sage/xtrem-master-data/nodes__detailed_resource__property__resourceGroup": "Grupo de recursos", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__dev_tools__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__dev_tools__node_name": "Herramientas de desarrollo", "@sage/xtrem-master-data/nodes__dev_tools__property__id": "Id.", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__employee__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__employee__node_name": "Trabajador", "@sage/xtrem-master-data/nodes__employee__property__firstName": "Nombre", "@sage/xtrem-master-data/nodes__employee__property__id": "Id.", "@sage/xtrem-master-data/nodes__employee__property__image": "Imagen", "@sage/xtrem-master-data/nodes__employee__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__employee__property__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__employee__property__name": "Nombre", "@sage/xtrem-master-data/nodes__employee__property__resource": "Recurso", "@sage/xtrem-master-data/nodes__employee__property__site": "Planta", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__exchange_rate__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__exchange_rate__node_name": "Tipo de cambio", "@sage/xtrem-master-data/nodes__exchange_rate__property__base": "Base", "@sage/xtrem-master-data/nodes__exchange_rate__property__dateRate": "Fecha de tipo de cambio", "@sage/xtrem-master-data/nodes__exchange_rate__property__destination": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__property__divisor": "Divisor", "@sage/xtrem-master-data/nodes__exchange_rate__property__rate": "Tipo impositivo", "@sage/xtrem-master-data/nodes__exchange_rate__property__shortDescription": "Descripción corta", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate": "Convertir a", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__amount": "Importe", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__base": "Base", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__destination": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__exchange_rate__query__convertRate__parameter__rateDate": "Fecha de tipo de cambio", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_naf": "Utiliza el siguiente formato para el número NAF: {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_rcs": "Utiliza el siguiente formato para el número RCS: {{format}}", "@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_siren": "Utiliza el siguiente formato para el número SIREN: {{format}}", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__ghs_classification__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__ghs_classification__node_name": "Clasificación según SGA", "@sage/xtrem-master-data/nodes__ghs_classification__property__hazard": "P<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__ghs_classification__property__id": "Id.", "@sage/xtrem-master-data/nodes__ghs_classification__property__name": "Nombre", "@sage/xtrem-master-data/nodes__ghs_classification__property__pictogram": "Pictograma", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__group_resource__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__group_resource__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__group_resource__node_name": "Grupo de recursos", "@sage/xtrem-master-data/nodes__group_resource__property__efficiency": "Rendimiento", "@sage/xtrem-master-data/nodes__group_resource__property__minCapabilityLevel": "<PERSON><PERSON> m<PERSON> de a<PERSON>", "@sage/xtrem-master-data/nodes__group_resource__property__replacements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__group_resource__property__resources": "Recursos", "@sage/xtrem-master-data/nodes__group_resource__property__type": "Tipo", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__incoterm__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__incoterm__node_name": "Incoterms", "@sage/xtrem-master-data/nodes__incoterm__property__description": "Descripción", "@sage/xtrem-master-data/nodes__incoterm__property__id": "Id.", "@sage/xtrem-master-data/nodes__incoterm__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__incoterm__property__name": "Nombre", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__indirect_cost_origin__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__indirect_cost_origin__node_name": "Origen de coste indirecto", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__id": "Id.", "@sage/xtrem-master-data/nodes__indirect_cost_origin__property__name": "Nombre", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__indirect_cost_section__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__indirect_cost_section__node_name": "Sección de coste indirecto", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__calculationMethod": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__id": "Id.", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__lines": "Líneas", "@sage/xtrem-master-data/nodes__indirect_cost_section__property__name": "Nombre", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__node_name": "Línea de sección de coste indirecto", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostOrigin": "Origen de coste indirecto", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__indirectCostSection": "Sección de coste indirecto", "@sage/xtrem-master-data/nodes__indirect_cost_section_line__property__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item__bom_revision_sequence_number_not_managed": "You can only add a revision number to a BOM if the item is BOM revision managed.", "@sage/xtrem-master-data/nodes__item__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__item__cannot_change_purchased_property_supplier_exists": "No puedes cambiar la propiedad comprada. Ya existen algunos registros en la página de proveedores.", "@sage/xtrem-master-data/nodes__item__cannot_change_sold_property_customers_exists": "No puedes cambiar la propiedad vendida. Ya existen algunos registros en la página de clientes.", "@sage/xtrem-master-data/nodes__item__commodity_code_EU_format": "Utiliza el siguiente formato para el código de mercancías: {{format}}", "@sage/xtrem-master-data/nodes__item__commodity_code_format": "Utiliza el formato {{format}} para el código de mercancías.", "@sage/xtrem-master-data/nodes__item__community_code_EU_format": "{{communityCodeEU}} no es un código de mercancías de la UE válido. El formato esperado es {{format}}.", "@sage/xtrem-master-data/nodes__item__density-cannot-be-negative": "La densidad no puede ser negativa.", "@sage/xtrem-master-data/nodes__item__expiration_mangement_cannot_be_enabled_without_lot_management": "No se puede habilitar la gestión de la caducidad con artículos que no se gestionen por lote.", "@sage/xtrem-master-data/nodes__item__mandatory_property": "Introduce un valor para los artículos gestionados por número de serie.", "@sage/xtrem-master-data/nodes__item__min-price-cannot-be-set-if-no-currency-is-defined": "Selecciona una divisa para poder introducir un precio mínimo.", "@sage/xtrem-master-data/nodes__item__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__not-a-volume-measure": "La unidad {{unitOfMeasure}} no es una unidad de volumen.", "@sage/xtrem-master-data/nodes__item__not-a-weight-measure": "La unidad {{unitOfMeasure}} no es una unidad de peso.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-negative": "El precio no puede ser negativo.", "@sage/xtrem-master-data/nodes__item__price-cannot-be-set-if-no-currency-is-defined": "Selecciona una divisa para poder introducir un precio base.", "@sage/xtrem-master-data/nodes__item__property__allergens": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__basePrice": "Precio base", "@sage/xtrem-master-data/nodes__item__property__bomRevisionSequenceNumber": "BOM revision sequence number", "@sage/xtrem-master-data/nodes__item__property__capacity": "Capacidad", "@sage/xtrem-master-data/nodes__item__property__category": "Categoría", "@sage/xtrem-master-data/nodes__item__property__classifications": "Clasificaciones", "@sage/xtrem-master-data/nodes__item__property__commodityCode": "Código de mercancías", "@sage/xtrem-master-data/nodes__item__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__item__property__customerPrices": "Precios para clientes", "@sage/xtrem-master-data/nodes__item__property__customers": "Clientes", "@sage/xtrem-master-data/nodes__item__property__density": "Densidad", "@sage/xtrem-master-data/nodes__item__property__description": "Descripción", "@sage/xtrem-master-data/nodes__item__property__eanNumber": "EAN", "@sage/xtrem-master-data/nodes__item__property__id": "Id.", "@sage/xtrem-master-data/nodes__item__property__image": "Imagen", "@sage/xtrem-master-data/nodes__item__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__item__property__isBomRevisionManaged": "Gestión de revisión de estructura de materiales", "@sage/xtrem-master-data/nodes__item__property__isBought": "Comprado", "@sage/xtrem-master-data/nodes__item__property__isExpiryManaged": "Gestión de caducidad", "@sage/xtrem-master-data/nodes__item__property__isManufactured": "Fabricado", "@sage/xtrem-master-data/nodes__item__property__isPhantom": "Fantasma", "@sage/xtrem-master-data/nodes__item__property__isPotencyManagement": "Gestión de concentración", "@sage/xtrem-master-data/nodes__item__property__isSold": "Vendido", "@sage/xtrem-master-data/nodes__item__property__isStockManaged": "Gestión de stock", "@sage/xtrem-master-data/nodes__item__property__isTraceabilityManagement": "Gestión de trazabilidad", "@sage/xtrem-master-data/nodes__item__property__itemSites": "Artículos-plantas", "@sage/xtrem-master-data/nodes__item__property__lotManagement": "Gestión de lotes", "@sage/xtrem-master-data/nodes__item__property__lotSequenceNumber": "Número de secuencia de lote", "@sage/xtrem-master-data/nodes__item__property__maximumSalesQuantity": "Cantidad máxima de venta", "@sage/xtrem-master-data/nodes__item__property__minimumPrice": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__minimumSalesQuantity": "Cantidad mínima de venta", "@sage/xtrem-master-data/nodes__item__property__name": "Nombre", "@sage/xtrem-master-data/nodes__item__property__prices": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item__property__purchaseUnit": "Unidad de compra", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversion": "Conversión de unidad de compra a stock", "@sage/xtrem-master-data/nodes__item__property__purchaseUnitToStockUnitConversionDedicated": "Coeficiente de conversión de unidad de compra a stock específico", "@sage/xtrem-master-data/nodes__item__property__salesUnit": "Unidad de venta", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversion": "Conversión de unidad de venta a stock", "@sage/xtrem-master-data/nodes__item__property__salesUnitToStockUnitConversionDedicated": "Coeficiente de conversión de unidad de venta a stock específico", "@sage/xtrem-master-data/nodes__item__property__serialNumberManagement": "Gestión de números de serie", "@sage/xtrem-master-data/nodes__item__property__serialNumberSequenceNumber": "Número de secuencia de número de serie", "@sage/xtrem-master-data/nodes__item__property__serialNumberUsage": "Uso de número de serie", "@sage/xtrem-master-data/nodes__item__property__status": "Estado", "@sage/xtrem-master-data/nodes__item__property__stockUnit": "Unidad de stock", "@sage/xtrem-master-data/nodes__item__property__supplierPrices": "<PERSON>cios de proveedores", "@sage/xtrem-master-data/nodes__item__property__suppliers": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/nodes__item__property__type": "Tipo", "@sage/xtrem-master-data/nodes__item__property__useSupplierSerialNumbers": "Utilizar números de serie de proveedor", "@sage/xtrem-master-data/nodes__item__property__volume": "Volumen", "@sage/xtrem-master-data/nodes__item__property__volumeUnit": "Unidad de volumen", "@sage/xtrem-master-data/nodes__item__property__weight": "Peso", "@sage/xtrem-master-data/nodes__item__property__weightUnit": "Unidad de peso", "@sage/xtrem-master-data/nodes__item__purchase_unit_not_0_decimal_places": "La unidad de compra {{unitOfMeasure}} no puede tener decimales en los artículos que se gestionan por número de serie.", "@sage/xtrem-master-data/nodes__item__sales_unit_not_0_decimal_places": "La unidad de venta {{unitOfMeasure}} no puede tener decimales en los artículos que se gestionan por número de serie.", "@sage/xtrem-master-data/nodes__item__standard_unit_of_measure_not_changeable": "La conversión de {{from}} a {{to}} es estándar. No puedes modificarla.", "@sage/xtrem-master-data/nodes__item__stock_unit_not_0_decimal_places": "La unidad de stock {{unitOfMeasure}} no puede tener decimales en los artículos que se gestionan por número de serie.", "@sage/xtrem-master-data/nodes__item__the_lot_and_serial_number_cannot_use_the_same_sequence_number": "El lote y el número de serie no pueden utilizar el mismo número de secuencia.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_lot": "Esta propiedad no se puede utilizar con artículos que no se gestionen por lote.", "@sage/xtrem-master-data/nodes__item__the_property_cannot_be_used_with_items_not_managed_by_serial_number": "No se puede utilizar una secuencia de número de serie para los artículos que no se gestionen por número de serie.", "@sage/xtrem-master-data/nodes__item__volume-cannot-be-negative": "El volumen no puede ser negativo.", "@sage/xtrem-master-data/nodes__item__weight-cannot-be-negative": "El peso no puede ser negativo.", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_allergen__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_allergen__node_name": "Alérgeno de artículo", "@sage/xtrem-master-data/nodes__item_allergen__property__allergen": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_allergen__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_category__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used": "No puedes cambiar un tipo de categoría de artículo en uso.", "@sage/xtrem-master-data/nodes__item_category__change_not_possible_item_category_is_used_on_supplier": "Solo puedes cambiar un tipo de categoría de proveedor que no esté ya asignado a un proveedor.", "@sage/xtrem-master-data/nodes__item_category__node_name": "Categoría de artículo", "@sage/xtrem-master-data/nodes__item_category__property__id": "Id.", "@sage/xtrem-master-data/nodes__item_category__property__isSequenceNumberManagement": "Gestión de números de secuencia", "@sage/xtrem-master-data/nodes__item_category__property__name": "Nombre", "@sage/xtrem-master-data/nodes__item_category__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__item_category__property__type": "Tipo", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_classifications__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_classifications__node_name": "Clasificaciones de artículo", "@sage/xtrem-master-data/nodes__item_classifications__property__classification": "Clasificación", "@sage/xtrem-master-data/nodes__item_classifications__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_customer__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_customer__node_name": "Artículo-cliente", "@sage/xtrem-master-data/nodes__item_customer__property__customer": "Cliente", "@sage/xtrem-master-data/nodes__item_customer__property__id": "Id.", "@sage/xtrem-master-data/nodes__item_customer__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__item_customer__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer__property__maximumSalesQuantity": "Cantidad máxima de venta", "@sage/xtrem-master-data/nodes__item_customer__property__minimumSalesQuantity": "Cantidad mínima de venta", "@sage/xtrem-master-data/nodes__item_customer__property__name": "Nombre", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnit": "Unidad de venta", "@sage/xtrem-master-data/nodes__item_customer__property__salesUnitToStockUnitConversion": "Conversión de unidad de venta a stock", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_customer_price__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_customer_price__node_name": "Precio de artículo-cliente", "@sage/xtrem-master-data/nodes__item_customer_price__property__charge": "Gasto", "@sage/xtrem-master-data/nodes__item_customer_price__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__item_customer_price__property__customer": "Cliente", "@sage/xtrem-master-data/nodes__item_customer_price__property__discount": "Descuento", "@sage/xtrem-master-data/nodes__item_customer_price__property__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/nodes__item_customer_price__property__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__item_customer_price__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__price": "Precio", "@sage/xtrem-master-data/nodes__item_customer_price__property__priceReason": "Motivo de precio", "@sage/xtrem-master-data/nodes__item_customer_price__property__salesSite": "Planta de venta", "@sage/xtrem-master-data/nodes__item_customer_price__property__startDate": "Fecha de inicio", "@sage/xtrem-master-data/nodes__item_customer_price__property__stockSite": "Planta de stock", "@sage/xtrem-master-data/nodes__item_customer_price__property__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/nodes__item_customer_price__property__unit": "Unidad", "@sage/xtrem-master-data/nodes__item_customer_price__property__validUnits": "Unidades válidas", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice": "Obtener precio de venta", "@sage/xtrem-master-data/nodes__item_customer_price__query__getSalesPrice__parameter__priceParameters": "Configuración de precio", "@sage/xtrem-master-data/nodes__item_not_active": "Quita los artículos inactivos para poder cambiar el estado del documento.", "@sage/xtrem-master-data/nodes__item_price__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_price__node_name": "Precio de artículo", "@sage/xtrem-master-data/nodes__item_price__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__item_price__property__dateValid": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__dateValidFrom": "Fecha de inicio de validez", "@sage/xtrem-master-data/nodes__item_price__property__dateValidTo": "Fecha de fin de validez", "@sage/xtrem-master-data/nodes__item_price__property__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__price": "Precio", "@sage/xtrem-master-data/nodes__item_price__property__priority": "Prioridad", "@sage/xtrem-master-data/nodes__item_price__property__site": "Planta", "@sage/xtrem-master-data/nodes__item_price__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/nodes__item_price__property__type": "Tipo", "@sage/xtrem-master-data/nodes__item_price__property__unit": "Unidad", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice": "Obtener precio compra", "@sage/xtrem-master-data/nodes__item_price__query__getPurchasePrice__parameter__priceParameters": "Configuración de precio", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_site__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_site__deletion_forbidden": "Este artículo-planta no se puede eliminar porque ya se está utilizando en un registro.", "@sage/xtrem-master-data/nodes__item_site__node_name": "Artículo-planta", "@sage/xtrem-master-data/nodes__item_site__property__batchQuantity": "Cantidad de tanda", "@sage/xtrem-master-data/nodes__item_site__property__completedProductDefaultLocation": "Completed product default location", "@sage/xtrem-master-data/nodes__item_site__property__costs": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__defaultSupplier": "Proveedor por defecto", "@sage/xtrem-master-data/nodes__item_site__property__economicOrderQuantity": "Cantidad económica de pedido", "@sage/xtrem-master-data/nodes__item_site__property__expectedQuantity": "Cantidad prevista", "@sage/xtrem-master-data/nodes__item_site__property__id": "Id.", "@sage/xtrem-master-data/nodes__item_site__property__inboundDefaultLocation": "Ubicación de entrada por defecto", "@sage/xtrem-master-data/nodes__item_site__property__indirectCostSection": "Sección de coste indirecto", "@sage/xtrem-master-data/nodes__item_site__property__isOrderToOrder": "Pedido por pedido", "@sage/xtrem-master-data/nodes__item_site__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__itemSiteCost": "Coste de artículo-planta", "@sage/xtrem-master-data/nodes__item_site__property__outboundDefaultLocation": "Ubicación de salida por defecto", "@sage/xtrem-master-data/nodes__item_site__property__preferredProcess": "Proceso preferente", "@sage/xtrem-master-data/nodes__item_site__property__prodLeadTime": "Plazo de producción", "@sage/xtrem-master-data/nodes__item_site__property__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/nodes__item_site__property__reorderPoint": "Punto de pedido", "@sage/xtrem-master-data/nodes__item_site__property__replenishmentMethod": "Método de reaprovisionamiento", "@sage/xtrem-master-data/nodes__item_site__property__requiredQuantity": "Cantidad necesaria", "@sage/xtrem-master-data/nodes__item_site__property__safetyStock": "Stock de seguridad", "@sage/xtrem-master-data/nodes__item_site__property__site": "Planta", "@sage/xtrem-master-data/nodes__item_site__property__stdCostValue": "Valor de coste estándar", "@sage/xtrem-master-data/nodes__item_site__property__stockUnit": "Unidad de stock", "@sage/xtrem-master-data/nodes__item_site__property__suppliers": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/nodes__item_site__property__valuationMethod": "Método de valoración", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite": "Obtener artículo-planta valorado", "@sage/xtrem-master-data/nodes__item_site__query__getValuedItemSite__parameter__searchCriteria": "Buscar criterios", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_site_cost__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_site_cost__node_name": "Coste de artículo-planta", "@sage/xtrem-master-data/nodes__item_site_cost__property__costCategory": "Categoría de coste", "@sage/xtrem-master-data/nodes__item_site_cost__property__forQuantity": "Para cantidad", "@sage/xtrem-master-data/nodes__item_site_cost__property__fromDate": "Fecha de inicio", "@sage/xtrem-master-data/nodes__item_site_cost__property__indirectCost": "Coste indirecto", "@sage/xtrem-master-data/nodes__item_site_cost__property__isCalculated": "Calculado", "@sage/xtrem-master-data/nodes__item_site_cost__property__isUpdatingPreviousCost": "Actualización de coste anterior", "@sage/xtrem-master-data/nodes__item_site_cost__property__itemSite": "Artículo-planta", "@sage/xtrem-master-data/nodes__item_site_cost__property__laborCost": "Coste de mano de obra", "@sage/xtrem-master-data/nodes__item_site_cost__property__machineCost": "Coste de m<PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__property__materialCost": "Coste de material", "@sage/xtrem-master-data/nodes__item_site_cost__property__stockUnit": "Unidad de stock", "@sage/xtrem-master-data/nodes__item_site_cost__property__toDate": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/nodes__item_site_cost__property__toolCost": "Coste de <PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__property__totalCost": "Coste total", "@sage/xtrem-master-data/nodes__item_site_cost__property__unitCost": "Coste unitario", "@sage/xtrem-master-data/nodes__item_site_cost__property__version": "Versión", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost": "Obtener coste de artículo-planta", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__effectiveDate": "Fecha efectiva", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_cost__query__getItemSiteCost__parameter__site": "Planta", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_site_supplier__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_site_supplier__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__item_site_supplier__node_name": "Artículo-planta-proveedor", "@sage/xtrem-master-data/nodes__item_site_supplier__property__isDefaultItemSupplier": "Proveedor de artículo por defecto", "@sage/xtrem-master-data/nodes__item_site_supplier__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSite": "Artículo-planta", "@sage/xtrem-master-data/nodes__item_site_supplier__property__itemSupplier": "Art<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__minimumPurchaseOrderQuantity": "Cantidad mínima de pedido de compra", "@sage/xtrem-master-data/nodes__item_site_supplier__property__priority": "Prioridad", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/nodes__item_site_supplier__property__purchaseUnit": "Unidad de compra", "@sage/xtrem-master-data/nodes__item_site_supplier__property__site": "Planta", "@sage/xtrem-master-data/nodes__item_site_supplier__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_site_supplier__property__uStoredPriority": "Prioridad almacenada", "@sage/xtrem-master-data/nodes__item_site_supplier__suppliers-dont-match": "El proveedor debe ser el mismo que el proveedor indicado en el artículo-proveedor.", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_supplier__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_supplier__node_name": "Art<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__id": "Id.", "@sage/xtrem-master-data/nodes__item_supplier__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__item_supplier__property__isDefaultItemSupplier": "Proveedor de artículo por defecto", "@sage/xtrem-master-data/nodes__item_supplier__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__minimumPurchaseQuantity": "Cantidad mínima de compra", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/nodes__item_supplier__property__purchaseUnitOfMeasure": "Unidad de compra", "@sage/xtrem-master-data/nodes__item_supplier__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemCode": "Código de artículo <PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierItemName": "Nombre de artículo de <PERSON>", "@sage/xtrem-master-data/nodes__item_supplier__property__supplierPriority": "Prioridad de proveedor", "@sage/xtrem-master-data/nodes__item_supplier__purchase_unit_forbidden": "Define un coeficiente de conversión de la unidad de stock {{stockUnit}} a la unidad de compra {{purchaseUnit}} para el artículo {{item}}.", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__item_supplier_price__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__item_supplier_price__node_name": "<PERSON><PERSON> de proveedor de artículo-", "@sage/xtrem-master-data/nodes__item_supplier_price__property__currency": "Divisa", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValid": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidFrom": "Fecha de inicio de validez", "@sage/xtrem-master-data/nodes__item_supplier_price__property__dateValidTo": "Fecha de fin de validez", "@sage/xtrem-master-data/nodes__item_supplier_price__property__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__itemSupplier": "<PERSON><PERSON><PERSON><PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/nodes__item_supplier_price__property__price": "Precio", "@sage/xtrem-master-data/nodes__item_supplier_price__property__priority": "Prioridad", "@sage/xtrem-master-data/nodes__item_supplier_price__property__site": "Planta", "@sage/xtrem-master-data/nodes__item_supplier_price__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/nodes__item_supplier_price__property__type": "Tipo", "@sage/xtrem-master-data/nodes__item_supplier_price__property__unit": "Unidad", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice": "Obtener precio compra", "@sage/xtrem-master-data/nodes__item_supplier_price__query__getPurchasePrice__parameter__priceParameters": "Configuración de precio", "@sage/xtrem-master-data/nodes__item-category__sequence-number-cannot-be-set": "El número de secuencia no se puede definir.", "@sage/xtrem-master-data/nodes__item-category__sequence-number-is-mandatory": "El número de secuencia es obligatorio.", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_infinite_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_cannot_put_specific_to_infinite": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate_improper_range": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromDate-greater-than-toDate": "", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity_improper_range": "El rango de cantidades no se puede superponer a otro rango de cantidades.", "@sage/xtrem-master-data/nodes__item-customer-price__fromQuantity-greater-than-toQuantity": "Introduce una cantidad de origen inferior a la cantidad de destino.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_infinite_range": "Si una línea contiene un rango de tiempos determinado, el nuevo rango de fechas no puede cubrir un periodo infinito.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_cannot_put_specific_to_infinite": "Si una línea contiene un periodo infinito, el nuevo rango de fechas no puede cubrir un rango de tiempos determinado.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate_improper_range": "El rango de fechas no se puede superponer a otro rango de fechas.", "@sage/xtrem-master-data/nodes__item-customer-price__startDate-greater-than-endDate": "Introduce una fecha de inicio anterior a la fecha de fin.", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-range": "El rango de cantidades {{qtyRange}} no es correcto.", "@sage/xtrem-master-data/nodes__item-price__invalid-quantity-unit-of-measure": "La unidad {{uom}} no es una unidad de cantidad.", "@sage/xtrem-master-data/nodes__item-price__price-cannot-be-negative": "El precio no puede ser negativo.", "@sage/xtrem-master-data/nodes__item-price__price-overlap": "Los precios se superponen: {{err<PERSON><PERSON>}}", "@sage/xtrem-master-data/nodes__item-price__priority-cannot-be-negative": "La prioridad no puede ser negativa.", "@sage/xtrem-master-data/nodes__item-price__quantity-cannot-be-negative": "La cantidad no puede ser negativa.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory": "Selecciona una planta de stock.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-purchase-site": "Selecciona una planta de stock o compra.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-or-purchase-site": "Selecciona una planta de stock, venta o compra.", "@sage/xtrem-master-data/nodes__item-site__site-must-be-is-inventory-or-sales-site": "Selecciona una planta de stock o venta.", "@sage/xtrem-master-data/nodes__item-site__valuation-method-must-be-standard-cost": "Los artículos sin gestión de stock solo se pueden valorar con el coste estándar.", "@sage/xtrem-master-data/nodes__item-site-cost__calculated-cost-only-for-manufactured-item": "Un coste calculado solo es compatible con un artículo fabricado.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_deletion_impossible_if_before_today": "No se puede eliminar. La fecha de inicio del coste de artículo-planta es anterior a la fecha actual.", "@sage/xtrem-master-data/nodes__item-site-cost__failed_update_impossible": "La fecha de inicio del coste de artículo-planta es anterior o igual a la fecha actual.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-from-date-already-set": "Ya existe un coste con esta fecha de inicio.", "@sage/xtrem-master-data/nodes__item-site-cost__failed-updating-previous-cost": "Ha habido un error al actualizar el coste anterior. {{errors}}", "@sage/xtrem-master-data/nodes__item-site-cost__incorrect-key-changes": "El artículo-planta y la categoría de un coste de artículo-planta no se pueden actualizar.", "@sage/xtrem-master-data/nodes__item-site-cost__labor-cost-only-for-manufactured-item": "Un coste de mano de obra solo es compatible con un artículo fabricado.", "@sage/xtrem-master-data/nodes__item-site-cost__material-cost-only-for-manufactured-item": "Un coste de máquina solo es compatible con un artículo fabricado.", "@sage/xtrem-master-data/nodes__item-site-cost__tool-cost-only-for-manufactured-item": "Un coste de máquina solo es compatible con un artículo fabricado.", "@sage/xtrem-master-data/nodes__item-supplier-price__fromQuantity_improper_range": "El rango de cantidades no se puede superponer a otro rango de cantidades.", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__labor_capability__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__labor_capability__node_name": "Aptitud de mano de obra", "@sage/xtrem-master-data/nodes__labor_capability__property__labor": "<PERSON>o de obra", "@sage/xtrem-master-data/nodes__labor_capability__property__machine": "Máquina", "@sage/xtrem-master-data/nodes__labor_capability__property__service": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__labor_capability__property__tool": "Herramienta", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__labor_resource__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__labor_resource__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__labor_resource__node_name": "<PERSON>o de obra", "@sage/xtrem-master-data/nodes__labor_resource__property__capabilities": "Aptitudes", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__license_plate_number__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers": "<PERSON><PERSON>r númer<PERSON> de contenedor interno en masa", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__containerId": "<PERSON>d. de contenedor", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleItem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__isSingleLot": "Monolote", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__locationId": "Id. de ubicación", "@sage/xtrem-master-data/nodes__license_plate_number__mutation__createBulkLicensePlateNumbers__parameter__numberToCreate": "Número por crear", "@sage/xtrem-master-data/nodes__license_plate_number__node_name": "Número de contenedor interno", "@sage/xtrem-master-data/nodes__license_plate_number__property__consumedCapacity": "Capacidad consumida", "@sage/xtrem-master-data/nodes__license_plate_number__property__container": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleItem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__license_plate_number__property__isSingleLot": "Monolote", "@sage/xtrem-master-data/nodes__license_plate_number__property__location": "Ubicación", "@sage/xtrem-master-data/nodes__license_plate_number__property__number": "Número", "@sage/xtrem-master-data/nodes__license_plate_number__property__owner": "Propietario", "@sage/xtrem-master-data/nodes__license-plate-number__location-required-for-automatic-number-generation": "El número de contenedor interno no se puede generar automáticamente para el contenedor {{containerId}} sin una ubicación.", "@sage/xtrem-master-data/nodes__license-plate-number__no_sequence_counter": "No hay ningún número de secuencia con el id. {{sequenceId}}.", "@sage/xtrem-master-data/nodes__license-plate-number__no-default-sequence": "El número de contenedor interno no se puede generar. Introduce un número de secuencia para el contenedor {{containerId}}.", "@sage/xtrem-master-data/nodes__license-plate-number__owner-not-required": "El propietario no es obligatorio en el número de contenedor interno.", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__location__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__location__location_category_virtual_not_allowed": "Las ubicaciones virtuales no son válidas. Selecciona otro valor.", "@sage/xtrem-master-data/nodes__location__location_zone_virtual_not_allowed": "Las áreas virtuales no son válidas. Selecciona otro valor.", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations": "Crear ubicaciones en masa", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locations": "Ubicaciones", "@sage/xtrem-master-data/nodes__location__mutation__createBulkLocations__parameter__locationSequence": "Secuencia de ubicación", "@sage/xtrem-master-data/nodes__location__mutation__getLocations": "Obtener ubicaciones", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__locationSequence": "Secuencia de ubicación", "@sage/xtrem-master-data/nodes__location__mutation__getLocations__parameter__requiredCombinations": "Combinaciones necesarias", "@sage/xtrem-master-data/nodes__location__node_name": "Ubicación", "@sage/xtrem-master-data/nodes__location__property__dangerousGoodAllowed": "Sustancias peligrosas permitidas", "@sage/xtrem-master-data/nodes__location__property__id": "Id.", "@sage/xtrem-master-data/nodes__location__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__location__property__isVirtualAllowed": "Virtual permitida", "@sage/xtrem-master-data/nodes__location__property__locationType": "Tipo de ubicación", "@sage/xtrem-master-data/nodes__location__property__locationZone": "Área de almacenamiento", "@sage/xtrem-master-data/nodes__location__property__name": "Nombre", "@sage/xtrem-master-data/nodes__location__property__site": "Planta", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__location_sequence__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__location_sequence__node_name": "Número de secuencia de ubicación", "@sage/xtrem-master-data/nodes__location_sequence__property__components": "Componentes", "@sage/xtrem-master-data/nodes__location_sequence__property__lastSequenceUsed": "Última secuencia utilizada", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsRemaining": "Número de ubicaciones restantes", "@sage/xtrem-master-data/nodes__location_sequence__property__numberLocationsUsed": "Número de ubicaciones utilizadas", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__location_sequence_component__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__location_sequence_component__node_name": "Componente de número de secuencia de ubicación", "@sage/xtrem-master-data/nodes__location_sequence_component__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__location_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__location_type__node_name": "Tipo de ubicación", "@sage/xtrem-master-data/nodes__location_type__property__description": "Descripción", "@sage/xtrem-master-data/nodes__location_type__property__id": "Id.", "@sage/xtrem-master-data/nodes__location_type__property__isVirtualAllowed": "Virtual permitida", "@sage/xtrem-master-data/nodes__location_type__property__locationCategory": "Categoría de ubicación", "@sage/xtrem-master-data/nodes__location_type__property__name": "Nombre", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__location_zone__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__location_zone__node_name": "Área de almacenamiento", "@sage/xtrem-master-data/nodes__location_zone__property__id": "Id.", "@sage/xtrem-master-data/nodes__location_zone__property__isVirtualAllowed": "Virtual permitida", "@sage/xtrem-master-data/nodes__location_zone__property__locations": "Ubicaciones", "@sage/xtrem-master-data/nodes__location_zone__property__name": "Nombre", "@sage/xtrem-master-data/nodes__location_zone__property__site": "Planta", "@sage/xtrem-master-data/nodes__location_zone__property__zoneType": "Tipo de área", "@sage/xtrem-master-data/nodes__location-type__location_category_virtual_not_allowed": "Las ubicaciones virtuales no están permitidas.", "@sage/xtrem-master-data/nodes__location-zone__site_modify": "La planta no se puede actualizar. Las ubicaciones están vinculadas a esta área de almacenamiento.", "@sage/xtrem-master-data/nodes__location-zone__zone_type_virtual_not_allowed": "Las áreas virtuales no están permitidas.", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__machine_resource__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__machine_resource__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__machine_resource__node_name": "Máquina", "@sage/xtrem-master-data/nodes__machine_resource__property__contractId": "Id. de contrato", "@sage/xtrem-master-data/nodes__machine_resource__property__contractName": "Nombre de contrato", "@sage/xtrem-master-data/nodes__machine_resource__property__minCapabilityLevel": "<PERSON><PERSON> m<PERSON> de a<PERSON>", "@sage/xtrem-master-data/nodes__machine_resource__property__model": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__machine_resource__property__serialNumber": "Número de serie", "@sage/xtrem-master-data/nodes__machine_resource__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__payment_term__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__payment_term__node_name": "Condiciones de pago", "@sage/xtrem-master-data/nodes__payment_term__property__businessEntityType": "Tipo de entidad empresarial", "@sage/xtrem-master-data/nodes__payment_term__property__days": "Días", "@sage/xtrem-master-data/nodes__payment_term__property__description": "Descripción", "@sage/xtrem-master-data/nodes__payment_term__property__discountAmount": "Importe de descuento", "@sage/xtrem-master-data/nodes__payment_term__property__discountDate": "Fecha de descuento", "@sage/xtrem-master-data/nodes__payment_term__property__discountFrom": "Descuento desde", "@sage/xtrem-master-data/nodes__payment_term__property__discountType": "Tipo de descuento", "@sage/xtrem-master-data/nodes__payment_term__property__dueDateType": "Tipo de fecha de vencimiento", "@sage/xtrem-master-data/nodes__payment_term__property__id": "Id.", "@sage/xtrem-master-data/nodes__payment_term__property__isActive": "Activas", "@sage/xtrem-master-data/nodes__payment_term__property__name": "Nombre", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyAmount": "Importe de recargo", "@sage/xtrem-master-data/nodes__payment_term__property__penaltyType": "Tipo de recargo", "@sage/xtrem-master-data/nodes__payment_term_discount_amount_percentage_error": "El importe de descuento debe ser inferior al 100 %.", "@sage/xtrem-master-data/nodes__payment_term_discount_date_should_be_before_due_date": "", "@sage/xtrem-master-data/nodes__payment_term_discount_from_needs_to_match_due_date_type": "", "@sage/xtrem-master-data/nodes__payment_term_discount_mandatory": "<PERSON> introduces una fecha, el tipo y el importe de descuento son obligatorios.", "@sage/xtrem-master-data/nodes__payment_term_penalty_amount_percentage_error": "El importe de recargo debe ser inferior al 100 %.", "@sage/xtrem-master-data/nodes__range_sequence_component__node_name": "Componente de secuencia de ubicación", "@sage/xtrem-master-data/nodes__range_sequence_component__property__endValue": "Valor de fin", "@sage/xtrem-master-data/nodes__range_sequence_component__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__range_sequence_component__property__startValue": "Valor de inicio", "@sage/xtrem-master-data/nodes__range_sequence_number__last_sequence_used_not_found": "The BOM revision number format is incorrect. Enter a new BOM revision number.", "@sage/xtrem-master-data/nodes__range_sequence_number__no_combinations_found": "", "@sage/xtrem-master-data/nodes__range_sequence_number__node_name": "Range sequence number", "@sage/xtrem-master-data/nodes__range_sequence_number__property__components": "Componentes", "@sage/xtrem-master-data/nodes__range_sequence_number__property__definitionLevel": "Nivel de definición", "@sage/xtrem-master-data/nodes__range_sequence_number__property__isChronological": "Cronológico", "@sage/xtrem-master-data/nodes__range_sequence_number__property__legislation": "Legislación", "@sage/xtrem-master-data/nodes__range_sequence_number__property__numberOfCombinations": "Número de ubicaciones", "@sage/xtrem-master-data/nodes__range_sequence_number__property__rtzLevel": "Rtz level", "@sage/xtrem-master-data/nodes__range_sequence_number__property__type": "Tipo", "@sage/xtrem-master-data/nodes__range_sequence_number__required_combinations_must_be_greater_than_zero": "You need to enter a number greater than zero.", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__reason_code__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__reason_code__node_name": "Código de motivo", "@sage/xtrem-master-data/nodes__reason_code__property__id": "Id.", "@sage/xtrem-master-data/nodes__reason_code__property__isActive": "Activo", "@sage/xtrem-master-data/nodes__reason_code__property__name": "Nombre", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__resource_cost_category__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__resource_cost_category__node_name": "Categoría de coste de recurso", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costCategory": "Categoría de coste", "@sage/xtrem-master-data/nodes__resource_cost_category__property__costUnit": "Unidad de coste", "@sage/xtrem-master-data/nodes__resource_cost_category__property__indirectCostSection": "Sección de coste indirecto", "@sage/xtrem-master-data/nodes__resource_cost_category__property__resource": "Recurso", "@sage/xtrem-master-data/nodes__resource_cost_category__property__runCost": "Coste operacional", "@sage/xtrem-master-data/nodes__resource_cost_category__property__setupCost": "Coste de a<PERSON>ste", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__resource_group_replacement__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__resource_group_replacement__node_name": "Reemplazo de grupo de recursos", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__replacement": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__resource_group_replacement__property__resourceGroup": "Grupo de recursos", "@sage/xtrem-master-data/nodes__resource-cost-category__the-cost-category-is-mandatory": "Introduce la categoría de coste {{costCategory}}.", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number__node_name": "Número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number__property__components": "Componentes", "@sage/xtrem-master-data/nodes__sequence_number__property__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number__property__values": "Valores", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number_assignment__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_company_mandatory": "Introduce un número de secuencia para la sociedad {{companyId}}.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__enter_sequence_number_site_mandatory": "Introduce un número de secuencia para la planta {{siteId}}.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__node_name": "Asignación de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment__only_allowed_for_sales_invoice_credit_memo_fr_legislation": "En la legislación francesa, los números de secuencia contabilizados solo se pueden asignar a facturas y facturas rectificativas de venta.", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__company": "Sociedad", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__currentLegislationId": "Id. de legislación actual", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isAssignOnPosting": "Asignar en contabilización", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isDefaultAssignment": "Asignación por defecto", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__isUsed": "En uso", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__legislation": "Legislación", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__sequenceNumberAssignmentDocumentType": "Tipo de documento de asignación de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__setupId": "Id. de parametrización", "@sage/xtrem-master-data/nodes__sequence_number_assignment__property__site": "Planta", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__node_name": "Tipo de documento de asignación de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__displayOrder": "Orden de visualización", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeFactory": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__nodeValues": "Valores de nodo", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignmentModule": "Módulo de asignación de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__sequenceNumberAssignments": "Asignaciones de números de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment_document_type__property__setupId": "Id. de parametrización", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__node_name": "Módulo de asignación de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__name": "Nombre", "@sage/xtrem-master-data/nodes__sequence_number_assignment_module__property__nodes": "Nodos", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__node_name": "Configuración de asignación de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_assignment_setup__property__modules": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number_component__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_component__node_name": "Componente de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_component__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__sequence_number_value__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__sequence_number_value__mutation__updateSequenceNumberValues": "Actualizar valores de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_value__mutation__updateSequenceNumberValues__parameter__sequenceNumberValues": "Valores de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_value__node_name": "Valor de número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_value__property__additionalInfo": "Información adicional", "@sage/xtrem-master-data/nodes__sequence_number_value__property__company": "Sociedad", "@sage/xtrem-master-data/nodes__sequence_number_value__property__period": "Periodo", "@sage/xtrem-master-data/nodes__sequence_number_value__property__periodDate": "<PERSON><PERSON> de periodo", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceNumber": "Número de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_value__property__sequenceValue": "Valor de secuencia", "@sage/xtrem-master-data/nodes__sequence_number_value__property__site": "Planta", "@sage/xtrem-master-data/nodes__sequence-number__length_exceed": "El número de secuencia es demasiado largo. Introduce uno con menos dígitos.", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__shift_detail__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__shift_detail__node_name": "Detalles de turno", "@sage/xtrem-master-data/nodes__shift_detail__property__duration": "Duración", "@sage/xtrem-master-data/nodes__shift_detail__property__formattedDuration": "Duración formateada", "@sage/xtrem-master-data/nodes__shift_detail__property__id": "Id.", "@sage/xtrem-master-data/nodes__shift_detail__property__name": "Nombre", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftEnd": "Fin de turno", "@sage/xtrem-master-data/nodes__shift_detail__property__shiftStart": "Inicio de turno", "@sage/xtrem-master-data/nodes__site__incorrect_format_siret": "{{siret}} no es un número SIRET válido. El formato esperado es {{format}}.", "@sage/xtrem-master-data/nodes__site__not-a-valid-tax-id": "{{taxIdNumber}} no es un NIF-IVA válido. El formato esperado es {{format}}.", "@sage/xtrem-master-data/nodes__site_extension__corporation_and_site": "La entidad jurídica no se puede definir como {{legalEntity}} porque la entidad empresarial está definida como planta.", "@sage/xtrem-master-data/nodes__site_extension__current_site_is_financial_site": "Esta planta ya es financiera.", "@sage/xtrem-master-data/nodes__site_extension__financial_site_mandatory": "La planta no es financiera. Introduce una planta financiera.", "@sage/xtrem-master-data/nodes__site_extension__invalid_timezone": "La zona horaria {{value}} no es válida.", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__standard__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__standard__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__standard__property__code": "Código", "@sage/xtrem-master-data/nodes__standard__property__id": "Id.", "@sage/xtrem-master-data/nodes__standard__property__industrySector": "Sector industrial", "@sage/xtrem-master-data/nodes__standard__property__name": "Nombre", "@sage/xtrem-master-data/nodes__standard__property__sdo": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__standard__property__version": "Versión", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__standard_industrial_classification__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__standard_industrial_classification__node_name": "Código de clasificación nacional de actividades económicas", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__legislation": "Legislación", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicCode": "Código CNAE", "@sage/xtrem-master-data/nodes__standard_industrial_classification__property__sicDescription": "Descripción de código CNAE", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__supplier__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__supplier__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__supplier__incorrect_format_siret": "{{siret}} no es un número SIRET válido. El formato esperado es {{format}}.", "@sage/xtrem-master-data/nodes__supplier__node_name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__billByAddress": "Dirección de facturador", "@sage/xtrem-master-data/nodes__supplier__property__billBySupplier": "Proveedor <PERSON>dor", "@sage/xtrem-master-data/nodes__supplier__property__category": "Categoría", "@sage/xtrem-master-data/nodes__supplier__property__certificates": "Certificados", "@sage/xtrem-master-data/nodes__supplier__property__deliveryMode": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/nodes__supplier__property__incoterm": "Incoterms", "@sage/xtrem-master-data/nodes__supplier__property__itemPrices": "Precios de artículos", "@sage/xtrem-master-data/nodes__supplier__property__items": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__parent": "Proveedor primario", "@sage/xtrem-master-data/nodes__supplier__property__paymentMethod": "Forma de pago", "@sage/xtrem-master-data/nodes__supplier__property__paymentTerm": "Condiciones de pago", "@sage/xtrem-master-data/nodes__supplier__property__payToAddress": "Dirección de pago", "@sage/xtrem-master-data/nodes__supplier__property__payToSupplier": "<PERSON><PERSON><PERSON><PERSON> pagado", "@sage/xtrem-master-data/nodes__supplier__property__prices": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__property__returnToAddress": "Dirección de devolución", "@sage/xtrem-master-data/nodes__supplier__property__returnToSupplier": "Proveedor receptor de devolución", "@sage/xtrem-master-data/nodes__supplier__property__standardIndustrialClassification": "Código de clasificación nacional de actividades económicas", "@sage/xtrem-master-data/nodes__supplier__property__supplierType": "<PERSON><PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier": "Obtener proveedor por defecto", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__supplier__query__getDefaultSupplier__parameter__site": "Planta", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__supplier_certificate__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate": "<PERSON><PERSON> certificado", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__certificate": "Certificado", "@sage/xtrem-master-data/nodes__supplier_certificate__mutation__renewCertificate__parameter__newCertificateDate": "Fecha de certificado nueva", "@sage/xtrem-master-data/nodes__supplier_certificate__node_name": "Certificado de proveedor", "@sage/xtrem-master-data/nodes__supplier_certificate__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__team__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__team__node_name": "Equipo", "@sage/xtrem-master-data/nodes__team__property__description": "Descripción", "@sage/xtrem-master-data/nodes__team__property__id": "Id.", "@sage/xtrem-master-data/nodes__team__property__name": "Nombre", "@sage/xtrem-master-data/nodes__team__property__site": "Planta", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__tool_resource__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__tool_resource__bulkMutation__bulkDelete": "Eliminar en masa", "@sage/xtrem-master-data/nodes__tool_resource__node_name": "Herramienta", "@sage/xtrem-master-data/nodes__tool_resource__property__consumptionMode": "Modo de consumo", "@sage/xtrem-master-data/nodes__tool_resource__property__hoursTracked": "Horas realizadas", "@sage/xtrem-master-data/nodes__tool_resource__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__tool_resource__property__quantity": "Cantidad", "@sage/xtrem-master-data/nodes__tool_resource__property__unitProduced": "Unidades producidas", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__item": "El cliente debe tener un artículo para la conversión de unidades.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__customer__type": "Introduce un flujo de venta para poder introducir el cliente.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__flow_without_item": "El coeficiente de conversión de unidades debe estar asignado a un flujo con artículos.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_delete": "No puedes eliminar el coeficiente de conversión de unidades. El valor de la propiedad \"isStandard\" es \"true\".", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_property_update": "No puedes actualizar la propiedad \"isStandard\".", "@sage/xtrem-master-data/nodes__unit__conversion__factor__is_standard_update": "Para actualizar, eliminar o añadir un coeficiente de conversión de unidades, la propiedad \"isStandard\" debe tener un valor distinto a \"true\".", "@sage/xtrem-master-data/nodes__unit__conversion__factor__item__type": "Introduce un artículo para poder introducir un tipo de flujo.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__purchase__customer": "No puedes utilizar un flujo de compra con el cliente.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__sales__supplier": "No puedes utilizar un flujo de venta con el proveedor.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__customer": "<PERSON><PERSON><PERSON> el proveedor o el cliente, no ambos.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__item": "El proveedor debe tener un artículo para la conversión de unidades.", "@sage/xtrem-master-data/nodes__unit__conversion__factor__supplier__type": "Introduce un flujo de compra para poder introducir el proveedor.", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__unit_conversion_factor__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__unit_conversion_factor__node_name": "Coeficiente de conversión de unidades", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__coefficient": "Coeficiente", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__customer": "Cliente", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__fromUnit": "Desde unidad", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__isStandard": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__toUnit": "Hasta unidad", "@sage/xtrem-master-data/nodes__unit_conversion_factor__property__type": "Tipo", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__unit_of_measure__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__unit_of_measure__decrease_not_possible_unit_of_measure_is_used": "No puedes disminuir los decimales de una unidad de medida en uso.", "@sage/xtrem-master-data/nodes__unit_of_measure__node_name": "Unidad de medida", "@sage/xtrem-master-data/nodes__unit_of_measure__property__conversionFactor": "Coeficiente de conversión", "@sage/xtrem-master-data/nodes__unit_of_measure__property__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__property__description": "Descripción", "@sage/xtrem-master-data/nodes__unit_of_measure__property__id": "Id.", "@sage/xtrem-master-data/nodes__unit_of_measure__property__isActive": "Activa", "@sage/xtrem-master-data/nodes__unit_of_measure__property__name": "Nombre", "@sage/xtrem-master-data/nodes__unit_of_measure__property__symbol": "Símbolo", "@sage/xtrem-master-data/nodes__unit_of_measure__property__type": "Tipo", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo": "Convertir de unidad a unidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__conversionFactor": "Coeficiente de conversión", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__customer": "Cliente", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__formatToUnitDecimalDigits": "Formatear a decimales de unidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__fromUnit": "Desde unidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__quantity": "Cantidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__toUnit": "Hasta unidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__convertFromTo__parameter__type": "Tipo", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit": "Obtener unidad de compra", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getPurchaseUnit__parameter__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor": "Obtener coeficiente de conversión de unidades", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__customer": "Cliente", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__fromUnit": "Desde unidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__supplier": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__toUnit": "Hasta unidad", "@sage/xtrem-master-data/nodes__unit_of_measure__query__getUnitConversionFactor__parameter__type": "Tipo", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__version_information__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__version_information__node_name": "Información de versión", "@sage/xtrem-master-data/nodes__version_information__property__text": "Texto", "@sage/xtrem-master-data/nodes__version_information__property__version": "Versión", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport": "Exportar", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__filter": "Filtro", "@sage/xtrem-master-data/nodes__weekly_shift__asyncMutation__asyncExport__parameter__id": "Id.", "@sage/xtrem-master-data/nodes__weekly_shift__no_daily_shift_on_full_week_shift": "Un turno de semana completa no puede tener turnos diarios.", "@sage/xtrem-master-data/nodes__weekly_shift__node_name": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__capacity": "Capacidad", "@sage/xtrem-master-data/nodes__weekly_shift__property__formattedCapacity": "Capacidad formateada", "@sage/xtrem-master-data/nodes__weekly_shift__property__fridayShift": "<PERSON><PERSON> de v<PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__id": "Id.", "@sage/xtrem-master-data/nodes__weekly_shift__property__isFullWeek": "Semana completa", "@sage/xtrem-master-data/nodes__weekly_shift__property__mondayShift": "<PERSON><PERSON> <PERSON> lunes", "@sage/xtrem-master-data/nodes__weekly_shift__property__name": "Nombre", "@sage/xtrem-master-data/nodes__weekly_shift__property__saturdayShift": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__sundayShift": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__thursdayShift": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__tuesdayShift": "<PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/nodes__weekly_shift__property__wednesdayShift": "<PERSON><PERSON> <PERSON> mi<PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__node_name": "Trabajo en curso", "@sage/xtrem-master-data/nodes__work_in_progress__property__actualQuantity": "Cantidad real", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentId": "Id. de documento", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentLine": "Línea de documento", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentNumber": "Número de documento", "@sage/xtrem-master-data/nodes__work_in_progress__property__documentType": "Tipo de documento", "@sage/xtrem-master-data/nodes__work_in_progress__property__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/nodes__work_in_progress__property__expectedQuantity": "Cantidad prevista", "@sage/xtrem-master-data/nodes__work_in_progress__property__item": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentLine": "Línea de documento de origen", "@sage/xtrem-master-data/nodes__work_in_progress__property__originDocumentType": "Tipo de documento de origen", "@sage/xtrem-master-data/nodes__work_in_progress__property__outstandingQuantity": "Cantidad pendiente", "@sage/xtrem-master-data/nodes__work_in_progress__property__remainingQuantityToAllocate": "Cantidad restante por asignar", "@sage/xtrem-master-data/nodes__work_in_progress__property__site": "Planta", "@sage/xtrem-master-data/nodes__work_in_progress__property__startDate": "Fecha de inicio", "@sage/xtrem-master-data/nodes__work_in_progress__property__status": "Estado", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite": "Obtener cantidad en curso por artículo-planta", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentDate": "<PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentItem": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentSite": "Planta actual", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__currentStatus": "Estado actual", "@sage/xtrem-master-data/nodes__work_in_progress__query__getWorkInProgressQuantityPerItemSite__parameter__originDocumentType": "Tipo de documento de origen", "@sage/xtrem-master-data/nodes_sequence_number_assignment_company_leg": "La legislación {{legislation}} debe ser la misma que la de la sociedad: {{companyLegislation}}.", "@sage/xtrem-master-data/nodes_sequence_number_assignment_site_error": "La planta debe pertenecer a la sociedad {{company}}.", "@sage/xtrem-master-data/nodes-resource-group-cannot-be-replaced-by-itself": "El grupo de recursos no se puede reemplazar a sí mismo.", "@sage/xtrem-master-data/nodes-resource-group-replacement-resource-group-should-be-replaced-by-another-one-with-same-site": "El grupo de recursos se debe reemplazar por otro de la misma planta.", "@sage/xtrem-master-data/or": "O", "@sage/xtrem-master-data/package__name": "<PERSON><PERSON> maest<PERSON>", "@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available": "No hay listas de precios disponibles.", "@sage/xtrem-master-data/page__item_customer_price_panel__price_is_zero": "El precio es nulo.", "@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available": "No hay listas de precios disponibles.", "@sage/xtrem-master-data/page-extensions__user_extension____navigationPanel__listItem__line11__title": "Cuadro de mando seleccionado", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__firstName": "Propietario", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__owner__lastName": "Propietario", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____columns__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> cuadro de mando", "@sage/xtrem-master-data/page-extensions__user_extension__selectedDashboard____title": "Cuadro de mando seleccionado", "@sage/xtrem-master-data/pages__address__country____columns__title__continent": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__address__deleteAddress____title": "Eliminar", "@sage/xtrem-master-data/pages__address_functions__businessentity_primary_active_address_mandatory": "Asigna una dirección activa principal a la entidad empresarial.", "@sage/xtrem-master-data/pages__address_functions__company_primary_active_address_mandatory": "Asigna una dirección activa principal a la sociedad.", "@sage/xtrem-master-data/pages__address_functions__customer_primary_active_address_mandatory": "Asigna una dirección activa principal al cliente.", "@sage/xtrem-master-data/pages__address_functions__primary_active_address_mandatory": "Asigna una dirección activa principal a {{addressEntityTypeLocalized}}.", "@sage/xtrem-master-data/pages__address_functions__site_primary_active_address_mandatory": "Asigna una dirección activa principal a la planta.", "@sage/xtrem-master-data/pages__address_functions__supplier_primary_active_address_mandatory": "Asigna una dirección activa principal al proveedor.", "@sage/xtrem-master-data/pages__address_functions_add_new____title": "Nueva dirección {{addressEntityTypeLocalized}}", "@sage/xtrem-master-data/pages__address_functions_address_contacts____title": "Contactos para {{addressEntityTypeLocalized}} dirección {{addressName}}.", "@sage/xtrem-master-data/pages__address_functions_businessentity_add_new____title": "Nueva dirección de entidad empresarial", "@sage/xtrem-master-data/pages__address_functions_businessentity_address_contacts____title": "Contactos para la dirección {{addressName}}", "@sage/xtrem-master-data/pages__address_functions_businessentity_edit____title": "Editar dirección de entidad empresarial", "@sage/xtrem-master-data/pages__address_functions_edit____title": "Editar <PERSON>cci<PERSON> {{addressEntityTypeLocalized}}", "@sage/xtrem-master-data/pages__address_panel__edit____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__address_panel__isPrimary_must_be_active": "Una dirección principal debe estar activa.", "@sage/xtrem-master-data/pages__address_panel__isPrimaryForAnotherEntity____title": "Principal", "@sage/xtrem-master-data/pages__address_panel__new____title": "Nueva dirección", "@sage/xtrem-master-data/pages__address-contacts_panel_add_new____title": "Nuevo contacto", "@sage/xtrem-master-data/pages__address-contacts_panel_edit____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_active": "Activo", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_inactive": "Inactiva", "@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__allergen____navigationPanel__listItem__pictogram__title": "Pictograma", "@sage/xtrem-master-data/pages__allergen____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__allergen____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__allergen____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__allergen____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__allergen___id____title": "Id.", "@sage/xtrem-master-data/pages__allergen__allergenImageBlock____title": "Pictograma", "@sage/xtrem-master-data/pages__allergen__allergenInformationBlock____title": "Información", "@sage/xtrem-master-data/pages__allergen__generalSection____title": "General", "@sage/xtrem-master-data/pages__allergen__id____title": "Id.", "@sage/xtrem-master-data/pages__allergen__idBlock____title": "Id.", "@sage/xtrem-master-data/pages__allergen__isActive____title": "Activo", "@sage/xtrem-master-data/pages__allergen__name____title": "Nombre", "@sage/xtrem-master-data/pages__already_used_message": "El número de secuencia anterior ya se ha utilizado para generar un número de documento.", "@sage/xtrem-master-data/pages__already_used_title": "Número de secuencia utilizado", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__componentLength__title": "Longitud de secuencia", "@sage/xtrem-master-data/pages__bom_revision_sequence____navigationPanel__listItem__isDefault__title": "Por defecto", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypePlural": "BOM revision sequence", "@sage/xtrem-master-data/pages__bom_revision_sequence____objectTypeSingular": "BOM revision sequence", "@sage/xtrem-master-data/pages__bom_revision_sequence____title": "BOM revision sequence", "@sage/xtrem-master-data/pages__bom_revision_sequence__addComponent____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__bom_revision_sequence__capital_letters_only": "El componente alfabético de un número de secuencia solo puede tener mayúsculas.", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentLength____title": "Longitud de secuencia", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__endValue": "Valor de fin", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__startValue": "Valor de inicio", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__bom_revision_sequence__components____title": "Componentes", "@sage/xtrem-master-data/pages__bom_revision_sequence__componentsBlock____title": "Componentes", "@sage/xtrem-master-data/pages__bom_revision_sequence__default": "Por defecto", "@sage/xtrem-master-data/pages__bom_revision_sequence__digits_only": "A numeric sequence number component can only contain numbers.", "@sage/xtrem-master-data/pages__bom_revision_sequence__id____title": "Id", "@sage/xtrem-master-data/pages__bom_revision_sequence__invalid_range": "El valor de inicio no puede ser superior al valor de fin.", "@sage/xtrem-master-data/pages__bom_revision_sequence__isDefault____title": "Por defecto", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainBlock____title": "Detalles", "@sage/xtrem-master-data/pages__bom_revision_sequence__mainSection____title": "General", "@sage/xtrem-master-data/pages__bom_revision_sequence__name____title": "Nombre", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__listItem__line6__title": "NIF-IVA", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__business_entity____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__business_entity____objectTypePlural": "Entidades empresariales", "@sage/xtrem-master-data/pages__business_entity____objectTypeSingular": "Entidad empresarial", "@sage/xtrem-master-data/pages__business_entity____title": "Entidad empresarial", "@sage/xtrem-master-data/pages__business_entity___id____title": "Id.", "@sage/xtrem-master-data/pages__business_entity__address_active": "Activa", "@sage/xtrem-master-data/pages__business_entity__address_inactive": "Inactiva", "@sage/xtrem-master-data/pages__business_entity__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__contacts": "Contactos", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__isPrimary": "Dirección principal", "@sage/xtrem-master-data/pages__business_entity__addresses____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__addresses____dropdownActions__title__4": "Eliminar dirección y contactos", "@sage/xtrem-master-data/pages__business_entity__addresses____title": "Direcciones", "@sage/xtrem-master-data/pages__business_entity__addressSection____title": "Direcciones", "@sage/xtrem-master-data/pages__business_entity__contact_active": "Activo", "@sage/xtrem-master-data/pages__business_entity__contact_inactive": "Inactivo", "@sage/xtrem-master-data/pages__business_entity__contacts____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__isPrimary": "Principal", "@sage/xtrem-master-data/pages__business_entity__contacts____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__business_entity__contacts____dropdownActions__title__3": "Eliminar", "@sage/xtrem-master-data/pages__business_entity__contacts____headerLabel__title": "Activo", "@sage/xtrem-master-data/pages__business_entity__contactSection____title": "Contactos", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__regionLabel": "Etiqueta de región", "@sage/xtrem-master-data/pages__business_entity__country____columns__title__zipLabel": "Etiqueta de código postal", "@sage/xtrem-master-data/pages__business_entity__country____lookupDialogTitle": "Seleccionar país", "@sage/xtrem-master-data/pages__business_entity__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__business_entity__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__business_entity__currency____title": "Divisa", "@sage/xtrem-master-data/pages__business_entity__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__business_entity__displayAddresses____columns__title": "Contactos", "@sage/xtrem-master-data/pages__business_entity__id____title": "Id.", "@sage/xtrem-master-data/pages__business_entity__idBlock____title": "Id.", "@sage/xtrem-master-data/pages__business_entity__imageBlock____title": "Imagen", "@sage/xtrem-master-data/pages__business_entity__isCustomer____title": "Cliente", "@sage/xtrem-master-data/pages__business_entity__isNaturalPerson____title": "Persona física", "@sage/xtrem-master-data/pages__business_entity__isSite____title": "Planta", "@sage/xtrem-master-data/pages__business_entity__isSupplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity__legalEntity____title": "Entidad jurídica", "@sage/xtrem-master-data/pages__business_entity__mainBlock____title": "General", "@sage/xtrem-master-data/pages__business_entity__mainSection____title": "General", "@sage/xtrem-master-data/pages__business_entity__name____title": "Nombre", "@sage/xtrem-master-data/pages__business_entity__parent____lookupDialogTitle": "Seleccionar entidad empresarial primaria", "@sage/xtrem-master-data/pages__business_entity__parent____title": "Entidad empresarial primaria", "@sage/xtrem-master-data/pages__business_entity__roleBlock____title": "Tipos", "@sage/xtrem-master-data/pages__business_entity__save____title": "Guardar", "@sage/xtrem-master-data/pages__business_entity__siret____title": "SIRET", "@sage/xtrem-master-data/pages__business_entity__taxIdNumber____title": "NIF-IVA", "@sage/xtrem-master-data/pages__business_entity__website____title": "Sitio web", "@sage/xtrem-master-data/pages__business_entity_address_panel____title": "Dirección de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine1____title": "Línea de dirección 1", "@sage/xtrem-master-data/pages__business_entity_address_panel__addressLine2____title": "Línea de dirección 2", "@sage/xtrem-master-data/pages__business_entity_address_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__city____title": "Ciudad", "@sage/xtrem-master-data/pages__business_entity_address_panel__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__business_entity_address_panel__country____lookupDialogTitle": "Seleccionar país", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryDetail____title": "Detalles de entrega", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____postfix": "día(s)", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryLeadTime____title": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____lookupDialogTitle": "Seleccionar modo de entrega", "@sage/xtrem-master-data/pages__business_entity_address_panel__deliveryMode____title": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____lookupDialogTitle": "Seleccionar Incoterms", "@sage/xtrem-master-data/pages__business_entity_address_panel__incoterm____title": "Incoterms®", "@sage/xtrem-master-data/pages__business_entity_address_panel__informationBlock____title": "Información de expedición", "@sage/xtrem-master-data/pages__business_entity_address_panel__isActiveShippingAddress____title": "Activa", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimary____title": "Dirección principal", "@sage/xtrem-master-data/pages__business_entity_address_panel__isPrimaryShippingAddress____title": "Dirección de expedición principal", "@sage/xtrem-master-data/pages__business_entity_address_panel__isShippingAddress____title": "Dirección de expedición", "@sage/xtrem-master-data/pages__business_entity_address_panel__locationPhoneNumber____title": "Número de teléfono", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainBlock____title": "Información de dirección", "@sage/xtrem-master-data/pages__business_entity_address_panel__mainSection____title": "Dirección", "@sage/xtrem-master-data/pages__business_entity_address_panel__name____title": "Nombre", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__business_entity_address_panel__shipmentSite____title": "Planta de expedición", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingBlock____title": "Información de expedición", "@sage/xtrem-master-data/pages__business_entity_address_panel__shippingSection____title": "Información", "@sage/xtrem-master-data/pages__business_entity_address_panel__workDaysSelection____title": "Días laborables", "@sage/xtrem-master-data/pages__business_entity_contact_panel____title": "Contacto de dirección de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____lookupDialogTitle": "Seleccionar <PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__address____title": "Dirección", "@sage/xtrem-master-data/pages__business_entity_contact_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__business_entity_contact_panel__email____title": "E-mail", "@sage/xtrem-master-data/pages__business_entity_contact_panel__firstName____title": "Nombre", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isActive____title": "Activo", "@sage/xtrem-master-data/pages__business_entity_contact_panel__isPrimary____title": "Principal", "@sage/xtrem-master-data/pages__business_entity_contact_panel__lastName____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_contact_panel__locationPhoneNumber____title": "Número de teléfono", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainBlock____title": "Información de contacto", "@sage/xtrem-master-data/pages__business_entity_contact_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_contact_panel__position____title": "Puesto", "@sage/xtrem-master-data/pages__business_entity_contact_panel__preferredName____title": "Nombre preferente", "@sage/xtrem-master-data/pages__business_entity_contact_panel__role____title": "Tipo", "@sage/xtrem-master-data/pages__business_entity_contact_panel__title____title": "Tratamiento", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____subtitle": "Elige una entidad empresarial que cumpla con tus necesidades.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer____title": "Crear cliente a partir de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressBlock____title": "Definición de dirección de expedición", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title": "Días laborables", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail": "Dirección de expedición", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__incoterm__name": "Incoterms®", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isActive": "Estado de dirección", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__isPrimary": "Dirección de expedición principal", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__leadTime": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__mode__name": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Planta de expedición", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__isPrimary": "Dirección principal", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__3": "Definir como dirección de expedición principal", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____dropdownActions__title__4": "Eliminar dirección y contactos", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addresses____title": "Direcciones", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__addressSection____title": "Dirección", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____lookupDialogTitle": "Seleccionar entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity____title": "Crear a partir de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__businessEntity_already_a_customer": "Esta entidad empresarial ya está definida como cliente.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialBlock____title": "Contabilidad", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__financialSection____title": "Contabilidad", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalBlock____title": "Selecciona la entidad empresarial a partir de la que se va a crear el cliente.", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__generalSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__paymentTerm____lookupDialogTitle": "Seleccionar condiciones de pago", "@sage/xtrem-master-data/pages__business_entity_selection_for_customer__primary_ship_to_address_mandatory": "El cliente debe tener al menos una dirección de expedición principal activa.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____subtitle": "Elige una entidad empresarial que cumpla con tus necesidades.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site____title": "Crear planta a partir de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____lookupDialogTitle": "Seleccionar entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity____title": "Crear a partir de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__businessEntity_already_a_site": "Esta entidad empresarial ya está definida como planta.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__financialSite____lookupDialogTitle": "Seleccionar planta financiera", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalBlock____title": "Selecciona la entidad empresarial a partir de la que se va a crear la planta.", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__generalSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__isFinance____title": "Contabilidad", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____columns__title__isActive": "Activa", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__legalCompany____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementBlock____title": "Gestión", "@sage/xtrem-master-data/pages__business_entity_selection_for_site__managementSection____title": "Gestión", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____subtitle": "Elige una entidad empresarial que cumpla con tus necesidades.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier____title": "<PERSON><PERSON><PERSON> proveedor a partir de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____lookupDialogTitle": "Seleccionar entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity____title": "Crear a partir de entidad empresarial", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__businessEntity_already_a_supplier": "Esta entidad empresarial ya está definida como proveedor.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialBlock____title": "Contabilidad", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__financialSection____title": "Contabilidad", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalBlock____title": "Selecciona la entidad empresarial a partir de la que se va a crear el proveedor.", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__generalSection____title": "General", "@sage/xtrem-master-data/pages__business_entity_selection_for_supplier__paymentTerm____lookupDialogTitle": "Seleccionar condiciones de pago", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__listItem__level__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_level____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__capability_level____objectTypePlural": "<PERSON><PERSON><PERSON> aptitud", "@sage/xtrem-master-data/pages__capability_level____objectTypeSingular": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__capability_level____title": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__capability_level__description____title": "Descripción", "@sage/xtrem-master-data/pages__capability_level__id____title": "Id.", "@sage/xtrem-master-data/pages__capability_level__level____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_level__mainBlock____title": "General", "@sage/xtrem-master-data/pages__capability_level__mainSection____title": "General", "@sage/xtrem-master-data/pages__capability_level__name____title": "Nombre", "@sage/xtrem-master-data/pages__capability_panel____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__description": "Descripción", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__level": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> nivel de aptitud", "@sage/xtrem-master-data/pages__capability_panel__capabilityLevel____title": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__capability_panel__create____title": "Aceptar", "@sage/xtrem-master-data/pages__capability_panel__createAction____title": "Aceptar", "@sage/xtrem-master-data/pages__capability_panel__dateEndValid____title": "Activa hasta", "@sage/xtrem-master-data/pages__capability_panel__dateStartValid____title": "Activa desde", "@sage/xtrem-master-data/pages__capability_panel__id____title": "Id.", "@sage/xtrem-master-data/pages__capability_panel__labor____columns__title___id": "Id.", "@sage/xtrem-master-data/pages__capability_panel__labor____lookupDialogTitle": "Se<PERSON>cci<PERSON>r mano de obra", "@sage/xtrem-master-data/pages__capability_panel__labor____title": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__description": "Descripción", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__minCapabilityLevel__level": "<PERSON>vel mín. de aptitud", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__model": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__capability_panel__machine____columns__title__serialNumber": "Número de serie", "@sage/xtrem-master-data/pages__capability_panel__machine____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__machine____title": "Máquina", "@sage/xtrem-master-data/pages__capability_panel__name____title": "Nombre", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__description": "Descripción", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__capability_panel__service____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__capability_panel__service____lookupDialogTitle": "Sele<PERSON><PERSON><PERSON> servicio", "@sage/xtrem-master-data/pages__capability_panel__service____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__description": "Descripción", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__quantity": "Cantidad", "@sage/xtrem-master-data/pages__capability_panel__tool____columns__title__resourceGroup__name": "Grupo de recursos", "@sage/xtrem-master-data/pages__capability_panel__tool____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__capability_panel__tool____title": "Herramienta", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_business_entity_address_title": "Editar dirección de entidad empresarial", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_company_address_title": "Editar dirección de sociedad", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_customer_address_title": "Editar dirección de cliente", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_site_address_title": "Editar dirección de planta", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_supplier_address_title": "Editar dirección de proveedor", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_business_entity_address_title": "Nueva dirección de entidad empresarial", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_company_address_title": "Nueva dirección de sociedad", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_customer_address_title": "Nueva dirección de cliente", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_site_address_title": "Nueva dirección de planta", "@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_supplier_address_title": "Nueva dirección de proveedor", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__edit_contact_title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__client_functions__business_entity_contact__new_contact_title": "Nuevo contacto", "@sage/xtrem-master-data/pages__company____add_site": "<PERSON><PERSON><PERSON>a", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line10__title": "Forma jurídica", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line11__title": "Valor de número de secuencia", "@sage/xtrem-master-data/pages__company____navigationPanel__listItem__line8__title": "NAF (APE)", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__company____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__company____objectTypePlural": "Sociedades", "@sage/xtrem-master-data/pages__company____objectTypeSingular": "Sociedad", "@sage/xtrem-master-data/pages__company____title": "Sociedad", "@sage/xtrem-master-data/pages__company__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__addresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__company__addresses____columns__title__contacts": "Contactos", "@sage/xtrem-master-data/pages__company__addresses____columns__title__isPrimary": "Dirección principal", "@sage/xtrem-master-data/pages__company__addresses____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__addresses____dropdownActions__title__4": "Eliminar dirección y contactos", "@sage/xtrem-master-data/pages__company__addresses____title": "Direcciones", "@sage/xtrem-master-data/pages__company__addressSection____title": "Direcciones", "@sage/xtrem-master-data/pages__company__contacts____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__contacts____columns__title__isPrimary": "Principal", "@sage/xtrem-master-data/pages__company__contacts____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__company__contacts____columns__title__role": "Tipo", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__company__contacts____dropdownActions__title__3": "Eliminar", "@sage/xtrem-master-data/pages__company__contacts____headerLabel__title": "Activo", "@sage/xtrem-master-data/pages__company__contactSection____title": "Contactos", "@sage/xtrem-master-data/pages__company__country____columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__company__country____lookupDialogTitle": "Seleccionar país", "@sage/xtrem-master-data/pages__company__creditLimitBlock____title": "Límite de crédito", "@sage/xtrem-master-data/pages__company__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__company__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__company__currency____placeholder": "Seleccionar...", "@sage/xtrem-master-data/pages__company__currency____title": "Divisa", "@sage/xtrem-master-data/pages__company__customerOnHoldCheck____title": "Comprobación de cliente bloqueado", "@sage/xtrem-master-data/pages__company__description____title": "Descripción", "@sage/xtrem-master-data/pages__company__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__company__id____title": "Id.", "@sage/xtrem-master-data/pages__company__isSequenceNumberIdUsed____title": "En uso", "@sage/xtrem-master-data/pages__company__legalForm____title": "Forma jurídica", "@sage/xtrem-master-data/pages__company__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-master-data/pages__company__legislation____placeholder": "Seleccionar...", "@sage/xtrem-master-data/pages__company__mainBlock____title": "Información", "@sage/xtrem-master-data/pages__company__mainSection____title": "General", "@sage/xtrem-master-data/pages__company__managementSection____title": "Gestión", "@sage/xtrem-master-data/pages__company__naf____title": "NAF (APE)", "@sage/xtrem-master-data/pages__company__name____title": "Nombre", "@sage/xtrem-master-data/pages__company__paymentTrackingBlock____title": "Seguimiento de pagos", "@sage/xtrem-master-data/pages__company__rcs____title": "RCS", "@sage/xtrem-master-data/pages__company__save____title": "Guardar", "@sage/xtrem-master-data/pages__company__sequenceNumberId____title": "Valor de número de secuencia", "@sage/xtrem-master-data/pages__company__siren____title": "SIREN", "@sage/xtrem-master-data/pages__company__siteBlock____title": "Organización", "@sage/xtrem-master-data/pages__compare__number_remaining_to__required": "Las combinaciones necesarias no pueden ser superiores a las combinaciones restantes.", "@sage/xtrem-master-data/pages__contact_panel__isPrimary_must_be_active": "Un contacto principal debe estar activo.", "@sage/xtrem-master-data/pages__contact_selection_panel____title": "Panel de selección de contacto", "@sage/xtrem-master-data/pages__contact_selection_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__email": "E-mail", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__firstName": "Nombre", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____columns__title__title": "Tratamiento", "@sage/xtrem-master-data/pages__contact_selection_panel__contacts____title": "Contactos", "@sage/xtrem-master-data/pages__contact_selection_panel__contactSelectionBlock____title": "Se<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__ok____title": "Aceptar", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__firstName": "Nombre", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____columns__title__title": "Tratamiento", "@sage/xtrem-master-data/pages__contact_selection_panel__selectedContact____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__consumedLocationCapacity__title": "Capacidad de almacenamiento necesaria", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isInternal__title": "Interno", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleItem__title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__isSingleLot__title": "Monolote", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__labelFormat__title": "Formato de etiqueta", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__locationCategory__title": "Tipo", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__sequenceNumber__title": "Número de secuencia", "@sage/xtrem-master-data/pages__container____navigationPanel__listItem__storageCapacity__title": "Capacidad de almacenamiento", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__container____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__container____objectTypePlural": "Contenedores", "@sage/xtrem-master-data/pages__container____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container__consumedLocationCapacity____title": "Capacidad de almacenamiento necesaria", "@sage/xtrem-master-data/pages__container__id____title": "Id.", "@sage/xtrem-master-data/pages__container__isActive____title": "Activo", "@sage/xtrem-master-data/pages__container__isInternal____title": "Interno", "@sage/xtrem-master-data/pages__container__isSingleItem____title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__container__isSingleLot____title": "Monolote", "@sage/xtrem-master-data/pages__container__labelFormat____title": "Formato de etiqueta", "@sage/xtrem-master-data/pages__container__mainSection____title": "General", "@sage/xtrem-master-data/pages__container__name____title": "Nombre", "@sage/xtrem-master-data/pages__container__sequenceNumber____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__container__sequenceNumber____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__container__sequenceNumber____lookupDialogTitle": "Seleccionar número de secuencia", "@sage/xtrem-master-data/pages__container__sequenceNumber____title": "Número de secuencia", "@sage/xtrem-master-data/pages__container__storageCapacity____title": "Capacidad de almacenamiento", "@sage/xtrem-master-data/pages__container__type____title": "<PERSON><PERSON><PERSON> de contenedor", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line2__title": "Id.", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__listItem__line3__title": "Tipo", "@sage/xtrem-master-data/pages__cost_category____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__cost_category____objectTypePlural": "Categorías de coste", "@sage/xtrem-master-data/pages__cost_category____objectTypeSingular": "Categoría de coste", "@sage/xtrem-master-data/pages__cost_category____title": "Categoría de coste", "@sage/xtrem-master-data/pages__cost_category__costCategoryType____title": "Tipo", "@sage/xtrem-master-data/pages__cost_category__id____title": "Id.", "@sage/xtrem-master-data/pages__cost_category__isMandatory____title": "Obligatoria", "@sage/xtrem-master-data/pages__cost_category__mainSection____title": "General", "@sage/xtrem-master-data/pages__cost_category__name____title": "Nombre", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line_5__title": "Código ISO 3166-1 alfa-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line2__title": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line3__title": "Divisa", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line5__title": "Código ISO 3166-1 alfa-3", "@sage/xtrem-master-data/pages__country____navigationPanel__listItem__line6__title": "Estado miembro de la UE", "@sage/xtrem-master-data/pages__country____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__country____objectTypePlural": "Países", "@sage/xtrem-master-data/pages__country____objectTypeSingular": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__country____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__country__continent____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__country__countryFlagBlock____title": "Bandera", "@sage/xtrem-master-data/pages__country__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__country__currency____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__country__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-master-data/pages__country__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__country__currency____title": "Divisa", "@sage/xtrem-master-data/pages__country__generalSection____title": "General", "@sage/xtrem-master-data/pages__country__id____title": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__country__isEuMember____title": "Estado miembro de la UE", "@sage/xtrem-master-data/pages__country__iso31661Alpha3____title": "Código ISO 3166-1 alfa-3", "@sage/xtrem-master-data/pages__country__legislation____placeholder": "Seleccionar legislación", "@sage/xtrem-master-data/pages__country__legislation____title": "Legislación", "@sage/xtrem-master-data/pages__country__mainBlock____title": "Información", "@sage/xtrem-master-data/pages__country__name____title": "Nombre", "@sage/xtrem-master-data/pages__country__regionLabel____title": "Etiqueta para región", "@sage/xtrem-master-data/pages__country__zipLabel____title": "Etiqueta para código postal", "@sage/xtrem-master-data/pages__country_invalid_id": "El identificador debe contener dos caracteres.", "@sage/xtrem-master-data/pages__country_invalid_iso_code": "El código debe contener tres caracteres.", "@sage/xtrem-master-data/pages__create_test_data____title": "Create test data", "@sage/xtrem-master-data/pages__create_test_data__instructions____content": "", "@sage/xtrem-master-data/pages__create_test_data__linkField____title": "Confluence page", "@sage/xtrem-master-data/pages__create_test_data__mainBlock____title": "Información", "@sage/xtrem-master-data/pages__create_test_data__mainSection____title": "General", "@sage/xtrem-master-data/pages__create-test-data__instuctions": "This page gives access to development tools to create large data sets for testing purposes.\n            \nPlease refer to the Confluence page for detailed instructions on the values for each property.", "@sage/xtrem-master-data/pages__create-test-data__link_instuctions": "Detailed instructions on how to create test data.", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line_4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line3__title": "Código ISO 4217", "@sage/xtrem-master-data/pages__currency____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency____objectTypePlural": "Divisas", "@sage/xtrem-master-data/pages__currency____objectTypeSingular": "Divisa", "@sage/xtrem-master-data/pages__currency____title": "Divisa", "@sage/xtrem-master-data/pages__currency___id____title": "Id.", "@sage/xtrem-master-data/pages__currency__addExchangeRate____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__addRateSection____title": "Añadir tipo de cambio", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__icon": "Bandera", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__currency__converterFromCurrency____title": "De", "@sage/xtrem-master-data/pages__currency__converterResult____placeholder": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterResult____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__converterSection____title": "Conversor", "@sage/xtrem-master-data/pages__currency__converterToAmount____placeholder": "Importe por convertir", "@sage/xtrem-master-data/pages__currency__converterToAmount____title": "Importe por convertir", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__icon": "Bandera", "@sage/xtrem-master-data/pages__currency__converterToCurrency____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__currency__converterToCurrency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__currency__converterToCurrency____placeholder": "Seleccionar divisa", "@sage/xtrem-master-data/pages__currency__converterToCurrency____title": "A", "@sage/xtrem-master-data/pages__currency__currencyRate____placeholder": "Tipo de cambio", "@sage/xtrem-master-data/pages__currency__currencyRate____title": "Tipo de cambio", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__dateRate": "Fecha de tipo de cambio", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__destination__symbol": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__rate": "Tipo de cambio", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____columns__title__shortDescription": "Descripción corta", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__image__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line2Right__title": "Descripción corta", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__line3Right__title": "Fecha de tipo de cambio", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__subtitleRight__title": "Descripción corta", "@sage/xtrem-master-data/pages__currency__currentExchangeRates____mobileCard__title__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__currentExchangeRatesSection____title": "Detalles", "@sage/xtrem-master-data/pages__currency__decimalDigits____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__icon": "Bandera", "@sage/xtrem-master-data/pages__currency__destinationCurrency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__currency__destinationCurrency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__currency__destinationCurrency____placeholder": "Seleccionar divisa", "@sage/xtrem-master-data/pages__currency__destinationCurrency____title": "Divisa de destino", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderBlock____title": "Tipos de cambio actuales", "@sage/xtrem-master-data/pages__currency__detailPanelHeaderSection____title": "Tipos de cambio actuales", "@sage/xtrem-master-data/pages__currency__divisor____title": "Divisa de origen", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__dateRate": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__id": "Divisa", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__destination__name": "Nombre", "@sage/xtrem-master-data/pages__currency__exchangeRates____columns__title__rate": "Tipo de cambio", "@sage/xtrem-master-data/pages__currency__exchangeRates____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__currency__exchangeRates____title": "Tipos de cambio", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____chart__xAxis__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__currency__exchangeRatesGraph____title": "Tipos de cambio", "@sage/xtrem-master-data/pages__currency__exchangeRatesSection____title": "Tipos de cambio", "@sage/xtrem-master-data/pages__currency__icon____title": "Bandera", "@sage/xtrem-master-data/pages__currency__id____title": "Código ISO 4217", "@sage/xtrem-master-data/pages__currency__invalid_id": "El identificador debe contener tres caracteres.", "@sage/xtrem-master-data/pages__currency__mainSection____title": "General", "@sage/xtrem-master-data/pages__currency__name____title": "Nombre", "@sage/xtrem-master-data/pages__currency__rateDate____placeholder": "Seleccionar...", "@sage/xtrem-master-data/pages__currency__rateDate____title": "Fecha de tipo de cambio", "@sage/xtrem-master-data/pages__currency__rounding____title": "Redondeo", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__icon": "Bandera", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__currency__selectedExchangeRateCurrency____title": "Divisa", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_message": "Estas divisas y esta fecha ya están vinculadas a un tipo de cambio. ¿Quieres mantener el tipo de cambio actual o aplicar el nuevo?", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_title": "Este tipo de cambio ya existe.", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_title": "Añadir tipo de cambio", "@sage/xtrem-master-data/pages__currency__side_panel_add_currency_reverse_rate_confirmation_message": "La fecha indicada ya está vinculada a un tipo de cambio inverso. ¿Quieres mantener el tipo de cambio actual o aplicar uno nuevo?", "@sage/xtrem-master-data/pages__currency__side_panel_add_inverse_currency_rate_confirmation_title": "Tipo de cambio inverso existente", "@sage/xtrem-master-data/pages__currency__symbol____title": "Símbolo", "@sage/xtrem-master-data/pages__customer____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title": "Bloquear", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__2": "Desb<PERSON>que<PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_4__title": "<PERSON><PERSON> principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line_5__title": "Ciudad", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line10__title": "E-mail", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line12__title": "NIF-IVA", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line13__title": "Estado", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line18__title": "Importe mínimo de pedido", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line19__title": "Condiciones de pago", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line20__title": "Bloqueado", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line21__title": "Categoría de cliente", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line22__title": "Límite de crédito", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line6__title": "Código postal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line7__title": "N.º teléfono principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line8__title": "Contacto principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__line9__title": "N.º teléfono de contacto principal", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__columns__title__id": "Id.", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__customer____navigationPanel__listItem__titleLine__title": "Nombre", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__2": "Todos", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__3": "Activos", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__4": "Inactivos", "@sage/xtrem-master-data/pages__customer____navigationPanel__optionsMenu__title__5": "Bloqueados", "@sage/xtrem-master-data/pages__customer____objectTypePlural": "Clientes", "@sage/xtrem-master-data/pages__customer____objectTypeSingular": "Cliente", "@sage/xtrem-master-data/pages__customer____title": "Cliente", "@sage/xtrem-master-data/pages__customer__addItem____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addItemPriceLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__address_assigned_be_primary": "No puedes eliminar la dirección principal de una entidad empresarial.", "@sage/xtrem-master-data/pages__customer__address_assigned_primary": "No puedes eliminar una dirección principal.", "@sage/xtrem-master-data/pages__customer__address_assigned_to_billToAddress": "Esta dirección se ha asignado a la dirección de facturación principal.", "@sage/xtrem-master-data/pages__customer__address_assigned_to_deliveryAddress": "Esta dirección se ha definido como dirección de expedición.", "@sage/xtrem-master-data/pages__customer__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____columns__title": "Días laborables", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail": "Dirección de expedición", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__incoterm__name": "Incoterms®", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isActive": "Estado de dirección", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__isPrimary": "Dirección de expedición principal", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__leadTime": "<PERSON><PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__mode__name": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__deliveryDetail__shipmentSite__name": "Planta de expedición", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__isPrimary": "Dirección principal", "@sage/xtrem-master-data/pages__customer__addresses____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__3": "Definir como dirección de expedición principal", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__addresses____dropdownActions__title__5": "Eliminar dirección y contactos", "@sage/xtrem-master-data/pages__customer__addresses____title": "Direcciones", "@sage/xtrem-master-data/pages__customer__addressSection____title": "Dirección", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_id": "Ya existe un cliente con este identificador.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_name": "Ya existe un cliente con este nombre.", "@sage/xtrem-master-data/pages__customer__already_exists_with_same_taxIdNumber": "Ya existe un cliente con este NIF-IVA.", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine1": "Línea 1", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__addressLine2": "Línea 2", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-master-data/pages__customer__billToAddress____columns__title__postcode": "Código postal", "@sage/xtrem-master-data/pages__customer__billToAddress____title": "Dirección de facturación principal", "@sage/xtrem-master-data/pages__customer__billToCustomer____title": "Cliente facturado", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____columns__title__concatenatedAddress": "Dirección de facturación principal", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__billToLinkedAddress____title": "Dirección de facturación principal", "@sage/xtrem-master-data/pages__customer__businessEntity____columns__title__isNaturalPerson": "Persona física", "@sage/xtrem-master-data/pages__customer__category____columns__title__sequenceNumber__name": "Número de secuencia", "@sage/xtrem-master-data/pages__customer__category____lookupDialogTitle": "Seleccionar categoría", "@sage/xtrem-master-data/pages__customer__commercialBlock____title": "Comercial", "@sage/xtrem-master-data/pages__customer__commercialSection____title": "Comercial", "@sage/xtrem-master-data/pages__customer__contact_assigned_primary": "No puedes eliminar un contacto principal.", "@sage/xtrem-master-data/pages__customer__contacts____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__isPrimary": "Principal", "@sage/xtrem-master-data/pages__customer__contacts____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__customer__contacts____dropdownActions__title__3": "Eliminar", "@sage/xtrem-master-data/pages__customer__contacts____headerLabel__title": "Activo", "@sage/xtrem-master-data/pages__customer__contactSection____title": "Contactos", "@sage/xtrem-master-data/pages__customer__country____columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__customer__country____lookupDialogTitle": "Seleccionar país", "@sage/xtrem-master-data/pages__customer__createCustomer____title": "Nuevo", "@sage/xtrem-master-data/pages__customer__createFromBusinessEntity____title": "Crear a partir de entidad empresarial", "@sage/xtrem-master-data/pages__customer__creditLimit____title": "Límite de crédito", "@sage/xtrem-master-data/pages__customer__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____addButtonText": "Añadir dirección de expedición", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title___id": "Id.", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__regionLabel": "Etiqueta de región", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__columns__title__zipLabel": "Etiqueta de código postal", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__3": "Línea 2", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__4": "Ciudad", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__5": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__6": "Número de teléfono", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__columns__shipToAddress__name__title__7": "Id.", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____columns__title__workDaysSelection": "Días laborables", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title": "Editar dirección de expedición", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__3": "Definir como principal", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____dropdownActions__title__4": "Eliminar", "@sage/xtrem-master-data/pages__customer__deliveryAddresses____headerLabel__title": "Activa", "@sage/xtrem-master-data/pages__customer__display_address_active": "Activa", "@sage/xtrem-master-data/pages__customer__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__2": "Dirección principal", "@sage/xtrem-master-data/pages__customer__displayAddresses____columns__title__3": "Dirección de expedición", "@sage/xtrem-master-data/pages__customer__displayStatus____title": "Estado", "@sage/xtrem-master-data/pages__customer__financialBlock____title": "Contabilidad", "@sage/xtrem-master-data/pages__customer__financialSection____title": "Contabilidad", "@sage/xtrem-master-data/pages__customer__imageBlock____title": "Imagen", "@sage/xtrem-master-data/pages__customer__internalNote____helperText": "Estas notas se muestran en los documentos internos.", "@sage/xtrem-master-data/pages__customer__internalNote____title": "Notas internas", "@sage/xtrem-master-data/pages__customer__isNaturalPerson____title": "Persona física", "@sage/xtrem-master-data/pages__customer__isOnHold____title": "Bloqueado", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__currency__id": "Divisa", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__customer__businessEntity__name": "Cliente", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__price": "Precio", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__priceReason__name": "Motivo de precio", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__salesSite__id": "Planta de venta", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__startDate": "Fecha de inicio", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__stockSite__id": "Planta de stock", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__customer__itemPrices____columns__title__unit__id": "Unidad", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__customer__itemPrices____dropdownActions__title__2": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemPricesSection____title": "Precios de artículos", "@sage/xtrem-master-data/pages__customer__items____columns__columns__item__name__title": "Categoría", "@sage/xtrem-master-data/pages__customer__items____columns__title__id": "Id. de artículo-cliente", "@sage/xtrem-master-data/pages__customer__items____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__customer__items____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____columns__title__maximumSalesQuantity": "Cantidad máxima de venta", "@sage/xtrem-master-data/pages__customer__items____columns__title__minimumSalesQuantity": "Cantidad mínima de venta", "@sage/xtrem-master-data/pages__customer__items____columns__title__name": "Artículo-cliente", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnit__name": "Unidad de venta", "@sage/xtrem-master-data/pages__customer__items____columns__title__salesUnitToStockUnitConversion": "Coeficiente de conversión de unidad de stock", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__items____dropdownActions__title__2": "Eliminar", "@sage/xtrem-master-data/pages__customer__items____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__itemSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__mainBlock____title": "General", "@sage/xtrem-master-data/pages__customer__mainSection____title": "General", "@sage/xtrem-master-data/pages__customer__minimumOrderAmount____title": "Importe mínimo de pedido", "@sage/xtrem-master-data/pages__customer__noteBlock____title": "Notas", "@sage/xtrem-master-data/pages__customer__notesSection____title": "Notas", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine1": "Línea 1", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__addressLine2": "Línea 2", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-master-data/pages__customer__payByAddress____columns__title__postcode": "Código postal", "@sage/xtrem-master-data/pages__customer__payByAddress____title": "Dirección de pago principal", "@sage/xtrem-master-data/pages__customer__payByCustomer____title": "Cliente facturado", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____columns__title__concatenatedAddress": "Dirección de pago principal", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer__payByLinkedAddress____title": "Dirección de pago principal", "@sage/xtrem-master-data/pages__customer__paymentTerm____lookupDialogTitle": "Seleccionar condiciones de pago", "@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory": "Asigna un contacto principal y activo a la dirección.", "@sage/xtrem-master-data/pages__customer__primary_active_address_mandatory": "Asigna una dirección principal y activa al cliente.", "@sage/xtrem-master-data/pages__customer__primary_ship_to_address_mandatory": "El cliente debe tener al menos una dirección de expedición principal activa.", "@sage/xtrem-master-data/pages__customer__put_on_hold": "Customer put on hold", "@sage/xtrem-master-data/pages__customer__putOnHold____title": "Bloquear", "@sage/xtrem-master-data/pages__customer__remove_on_hold": "Customer hold removed", "@sage/xtrem-master-data/pages__customer__removeOnHold____title": "Desb<PERSON>que<PERSON>", "@sage/xtrem-master-data/pages__customer__save____title": "Guardar", "@sage/xtrem-master-data/pages__customer__ShippingSection____title": "Expedición", "@sage/xtrem-master-data/pages__customer__website____title": "Sitio web", "@sage/xtrem-master-data/pages__customer_address_add_new____title": "Nueva dirección de cliente", "@sage/xtrem-master-data/pages__customer_address_contacts____title": "Contactos asociados a {{value}}", "@sage/xtrem-master-data/pages__customer_address_edit____title": "Editar dirección de cliente", "@sage/xtrem-master-data/pages__customer_address_panel_new__isPrimaryShippingAddress_must_be_active": "Habilita la dirección de expedición principal.", "@sage/xtrem-master-data/pages__customer_contact_list__primary_address": "Dirección principal", "@sage/xtrem-master-data/pages__customer_contact_list__primary_contact": "Contacto principal", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__customer_price_reason____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypePlural": "Motivos de precio para cliente", "@sage/xtrem-master-data/pages__customer_price_reason____objectTypeSingular": "Motivo de precio para cliente", "@sage/xtrem-master-data/pages__customer_price_reason____title": "Motivo de precio para cliente", "@sage/xtrem-master-data/pages__customer_price_reason__description____title": "Descripción", "@sage/xtrem-master-data/pages__customer_price_reason__mainSection____title": "General", "@sage/xtrem-master-data/pages__customer_price_reason__name____title": "Nombre", "@sage/xtrem-master-data/pages__customer_price_reason__priority____title": "Prioridad", "@sage/xtrem-master-data/pages__customer_price_reason__priority_already_exists": "La prioridad {{priority}} ya existe.", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Gestión de números de secuencia", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line_4__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line3__title": "Cliente", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__listItem__sequenceNumber__title": "Número de secuencia", "@sage/xtrem-master-data/pages__customer_supplier_category____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypePlural": "Categorías de proveedor y de cliente", "@sage/xtrem-master-data/pages__customer_supplier_category____objectTypeSingular": "Categoría de proveedor y de cliente", "@sage/xtrem-master-data/pages__customer_supplier_category____title": "Categoría de proveedor y de cliente", "@sage/xtrem-master-data/pages__customer_supplier_category__id____title": "Id.", "@sage/xtrem-master-data/pages__customer_supplier_category__isCustomer____title": "Cliente", "@sage/xtrem-master-data/pages__customer_supplier_category__isSequenceNumberManagement____title": "Gestión de números de secuencia", "@sage/xtrem-master-data/pages__customer_supplier_category__isSupplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__customer_supplier_category__mainSection____title": "General", "@sage/xtrem-master-data/pages__customer_supplier_category__name____title": "Nombre", "@sage/xtrem-master-data/pages__customer_supplier_category__sequenceNumber____title": "Número de secuencia", "@sage/xtrem-master-data/pages__customer-supplier__category_dialog_content": "La categoría seleccionada está vinculada a un número de secuencia. ¿Quieres generar otro id. o mantener el actual?", "@sage/xtrem-master-data/pages__customer-supplier__generate_ID": "Generar id.", "@sage/xtrem-master-data/pages__customer-supplier__keep_current_id-": "Mantener id. actual", "@sage/xtrem-master-data/pages__customer-supplier__select_id_number_title": "Seleccionar id.", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-customer": "Seleccionar id. de número de secuencia de cliente", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier": "Seleccionar id. de número de secuencia de proveedor", "@sage/xtrem-master-data/pages__customer-supplier-category__lookup-supplier-customer": "Seleccionar id. de número de secuencia de cliente y proveedor", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__formattedCapacity__title": "Capacidad", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__listItem__isFullDay__title": "<PERSON>ía completo", "@sage/xtrem-master-data/pages__daily_shift____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__daily_shift____objectTypePlural": "Turnos diarios", "@sage/xtrem-master-data/pages__daily_shift____objectTypeSingular": "Turno diario", "@sage/xtrem-master-data/pages__daily_shift____title": "Turno diario", "@sage/xtrem-master-data/pages__daily_shift___id____title": "Id.", "@sage/xtrem-master-data/pages__daily_shift__addShiftDetail____title": "<PERSON><PERSON><PERSON> de turno", "@sage/xtrem-master-data/pages__daily_shift__detailsBlock____title": "Detalles", "@sage/xtrem-master-data/pages__daily_shift__formattedCapacity____title": "Capacidad", "@sage/xtrem-master-data/pages__daily_shift__isFullDay____title": "<PERSON>ía completo", "@sage/xtrem-master-data/pages__daily_shift__mainBlock____title": "General", "@sage/xtrem-master-data/pages__daily_shift__mainSection____title": "General", "@sage/xtrem-master-data/pages__daily_shift__name____title": "Nombre", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__formattedDuration": "Duración", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__id": "Id.", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__name": "Nombre", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftEnd": "Fin de turno", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____columns__title__shiftDetail__shiftStart": "Inicio de turno", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__daily_shift__shiftDetails____title": "Detalles de turno", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__formattedDuration": "Duración", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftEnd": "Fin de turno", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____columns__title__shiftStart": "Inicio de turno", "@sage/xtrem-master-data/pages__daily_shift__shiftDetailsSelect____lookupDialogTitle": "Seleccionar de<PERSON><PERSON> de turno", "@sage/xtrem-master-data/pages__delete_page_dialog_content": "¿Quieres eliminar este registro?", "@sage/xtrem-master-data/pages__delete_page_dialog_title": "Confirmar eliminación", "@sage/xtrem-master-data/pages__delete_page_Item_delete_supplier_price_dialog_content": "¿Quieres eliminar esta línea? Esta acción no se puede revertir una vez guardado el documento.", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__inlineActions__title__duplicate": "Duplicar", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__delivery_mode____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__delivery_mode____objectTypePlural": "Modos de entrega", "@sage/xtrem-master-data/pages__delivery_mode____objectTypeSingular": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/pages__delivery_mode____title": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/pages__delivery_mode__description____title": "Descripción", "@sage/xtrem-master-data/pages__delivery_mode__id____title": "Id.", "@sage/xtrem-master-data/pages__delivery_mode__name____title": "Nombre", "@sage/xtrem-master-data/pages__delivery_mode__section____title": "General", "@sage/xtrem-master-data/pages__email_exception": "El e-mail no se ha enviado: {{exception}}.", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__image__title": "Imagen", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__employee____navigationPanel__listItem__line2Right__title": "Recurso", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__employee____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__employee____objectTypePlural": "Trabajadores", "@sage/xtrem-master-data/pages__employee____objectTypeSingular": "Trabajador", "@sage/xtrem-master-data/pages__employee____title": "Trabajador", "@sage/xtrem-master-data/pages__employee__firstName____title": "Nombre", "@sage/xtrem-master-data/pages__employee__id____title": "Id.", "@sage/xtrem-master-data/pages__employee__image____title": "Imagen", "@sage/xtrem-master-data/pages__employee__isActive____title": "Activo", "@sage/xtrem-master-data/pages__employee__lastName____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__employee__mainSection____title": "General", "@sage/xtrem-master-data/pages__employee__resource____columns__title__resourceGroup__type": "Tipo", "@sage/xtrem-master-data/pages__employee__resource____lookupDialogTitle": "Sele<PERSON><PERSON>r recurso", "@sage/xtrem-master-data/pages__employee__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__enter_email_address_and_last_name": "Introduce el e-mail y los apellidos.", "@sage/xtrem-master-data/pages__ghs_classification____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__ghs_classification____objectTypePlural": "Clasificaciones según SGA", "@sage/xtrem-master-data/pages__ghs_classification____objectTypeSingular": "Clasificación según SGA", "@sage/xtrem-master-data/pages__ghs_classification____title": "Clasificación según SGA", "@sage/xtrem-master-data/pages__ghs_classification__generalSection____title": "General", "@sage/xtrem-master-data/pages__ghs_classification__ghsClassificationInformationBlock____title": "Clasificación según SGA", "@sage/xtrem-master-data/pages__ghs_classification__hazard____title": "Peligros", "@sage/xtrem-master-data/pages__ghs_classification__id____title": "Id.", "@sage/xtrem-master-data/pages__ghs_classification__idBlock____title": "Id.", "@sage/xtrem-master-data/pages__ghs_classification__name____title": "Nombre", "@sage/xtrem-master-data/pages__ghs_classification__pictogram____title": "Pictograma", "@sage/xtrem-master-data/pages__ghs_classification__pictogramBlock____title": "Pictograma", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__efficiency__title": "Rendimiento", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__location__title": "Ubicación", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__minCapabilityLevel__title": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__type__title": "Tipo de recurso", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__listItem__weeklyShift__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__group_resource____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__group_resource____objectTypePlural": "Grupos de recursos", "@sage/xtrem-master-data/pages__group_resource____objectTypeSingular": "Grupo de recursos", "@sage/xtrem-master-data/pages__group_resource____title": "Grupo de recursos", "@sage/xtrem-master-data/pages__group_resource__addCostCategory____title": "Añadir categoría de coste", "@sage/xtrem-master-data/pages__group_resource__addReplacementLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__addResource____helperText": "<PERSON><PERSON><PERSON> recurso", "@sage/xtrem-master-data/pages__group_resource__addResource____title": "<PERSON><PERSON><PERSON> recurso", "@sage/xtrem-master-data/pages__group_resource__blockDetails____title": "Configuración", "@sage/xtrem-master-data/pages__group_resource__blockReplacements____title": "Grupos de recursos de reemplazo", "@sage/xtrem-master-data/pages__group_resource__blockResources____title": "Recursos", "@sage/xtrem-master-data/pages__group_resource__blockWeekly____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__group_resource__costBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__costSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__description____title": "Descripción", "@sage/xtrem-master-data/pages__group_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__group_resource__efficiency____title": "Rendimiento", "@sage/xtrem-master-data/pages__group_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__group_resource__id____title": "Id.", "@sage/xtrem-master-data/pages__group_resource__location____columns__title__locationType__id": "Tipo", "@sage/xtrem-master-data/pages__group_resource__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-master-data/pages__group_resource__location____title": "Ubicación", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> nivel de aptitud", "@sage/xtrem-master-data/pages__group_resource__minCapabilityLevel____title": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__group_resource__name____title": "Nombre", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title___sortValue": "Prioridad", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__id": "Id.", "@sage/xtrem-master-data/pages__group_resource__replacements____columns__title__replacement__name": "Nombre", "@sage/xtrem-master-data/pages__group_resource__replacements____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__group_resource__replacements____title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCapacity____title": "Capacidad semanal", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Tipo de categoría de coste", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoria", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costCategory__name": "Categoría de coste", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__costUnit__name": "Unidad de coste", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coste indirecto", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__runCost": "Operacional", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__group_resource__resourceCostCategories____title": "Categorías de costes de recursos", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title": "Tipo", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__group_resource__resources____columns__title__weeklyShift__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__2": "Excluir", "@sage/xtrem-master-data/pages__group_resource__resources____dropdownActions__title__3": "Eliminar", "@sage/xtrem-master-data/pages__group_resource__resources____title": "Recursos", "@sage/xtrem-master-data/pages__group_resource__section____title": "General", "@sage/xtrem-master-data/pages__group_resource__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__group_resource__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__group_resource__transferInResource____helperText": "Incluir", "@sage/xtrem-master-data/pages__group_resource__transferInResource____title": "Incluir", "@sage/xtrem-master-data/pages__group_resource__type____title": "Tipo de recurso", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____columns__title__dailyShift": "Turno diario", "@sage/xtrem-master-data/pages__group_resource__weeklyDetails____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____columns__title__formattedCapacity": "Capacidad", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____lookupDialogTitle": "Seleccionar turno semanal", "@sage/xtrem-master-data/pages__group_resource__weeklyShift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__incoterm____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__incoterm____objectTypePlural": "Incoterms®", "@sage/xtrem-master-data/pages__incoterm____objectTypeSingular": "Incoterms®", "@sage/xtrem-master-data/pages__incoterm____title": "Incoterms®", "@sage/xtrem-master-data/pages__incoterm__description____title": "Descripción", "@sage/xtrem-master-data/pages__incoterm__id____title": "Id.", "@sage/xtrem-master-data/pages__incoterm__name____title": "Nombre", "@sage/xtrem-master-data/pages__incoterm__section____title": "General", "@sage/xtrem-master-data/pages__indirect_cost_origin____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypePlural": "Orígenes de coste indirecto", "@sage/xtrem-master-data/pages__indirect_cost_origin____objectTypeSingular": "Origen de coste indirecto", "@sage/xtrem-master-data/pages__indirect_cost_origin____title": "Origen de coste indirecto", "@sage/xtrem-master-data/pages__indirect_cost_origin__id____title": "Id.", "@sage/xtrem-master-data/pages__indirect_cost_origin__mainSection____title": "General", "@sage/xtrem-master-data/pages__indirect_cost_origin__name____title": "Nombre", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line_4__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__indirect_cost_section____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypePlural": "Secciones de costes indirectos", "@sage/xtrem-master-data/pages__indirect_cost_section____objectTypeSingular": "Sección de coste indirecto", "@sage/xtrem-master-data/pages__indirect_cost_section____title": "Sección de coste indirecto", "@sage/xtrem-master-data/pages__indirect_cost_section__addOrigin____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__indirect_cost_section__calculationMethod____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__indirect_cost_section__id____title": "Id.", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__id": "Origen de coste indirecto", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__indirectCostOrigin__name": "Nombre", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____columns__title__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__indirect_cost_section__lines____title": "Detalles", "@sage/xtrem-master-data/pages__indirect_cost_section__mainSection____title": "General", "@sage/xtrem-master-data/pages__indirect_cost_section__name____title": "Nombre", "@sage/xtrem-master-data/pages__indirect_cost_section__originsBlock____title": "Detalles", "@sage/xtrem-master-data/pages__indirect_cost_section__totalPercentage____title": "Porcentaje total", "@sage/xtrem-master-data/pages__invalid-email": "La dirección de e-mail \"{{email}}\" no es correcta.", "@sage/xtrem-master-data/pages__item____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__item____navigationPanel__dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__item____navigationPanel__emptyStateClickableText": "Crea un artículo.", "@sage/xtrem-master-data/pages__item____navigationPanel__inlineActions__title__duplicate": "Duplicar", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__basePrice__title": "Precio base", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__commodityCode__title": "Código de mercancías", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__currency__title": "Divisa de venta", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__expirationDate__title": "Gestión de fecha de caducidad", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBomRevisionManaged__title": "Revisión de estructura de materiales", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isBought__title": "Comprado", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isManufactured__title": "Fabricado", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isPhantom__title": "Fantasma", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isSold__title": "Vendido", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__isStockManaged__title": "Gestión de stock", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line2Right__title": "GTIN-13", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__line3Right__title": "Categoría", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotManagement__title": "Gestión de lotes", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__lotSequenceNumber__title": "Número de secuencia de lote", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__maximumSalesQuantity__title": "Cantidad máxima de venta", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumPrice__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__minimumSalesQuantity__title": "Cantidad mínima de venta", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__purchaseUnit__title": "Unidad de compra", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__salesUnit__title": "Unidad de venta", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberManagement__title": "Gestión de números de serie", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__serialNumberSequenceNumber__title": "Secuencia de número de serie", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__stockUnit__title": "Unidad de stock", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__volumeUnit__title": "Unidad de volumen", "@sage/xtrem-master-data/pages__item____navigationPanel__listItem__weightUnit__title": "Unidad de peso", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__3": "En desarrollo", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__4": "Sin renovar", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__5": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__6": "No utilizables", "@sage/xtrem-master-data/pages__item____navigationPanel__optionsMenu__title__7": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item___id____title": "Id.", "@sage/xtrem-master-data/pages__item__addingNewAllergen____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__addingNewAllergen____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__addingNewGhsClassification____title": "Seleccionar clasificación según SGA", "@sage/xtrem-master-data/pages__item__addItemSite____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__addNewAllergen____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__addNewAllergenAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__addNewClassification____title": "Seleccionar clasificación según SGA", "@sage/xtrem-master-data/pages__item__addNewClassificationAction____title": "Añadir clasificación", "@sage/xtrem-master-data/pages__item__addSupplier____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__id": "Id.", "@sage/xtrem-master-data/pages__item__allergens____columns__title__allergen__name": "Nombre", "@sage/xtrem-master-data/pages__item__allergens____dropdownActions__title__remove": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__allergens____mobileCard__subtitle__title": "Id.", "@sage/xtrem-master-data/pages__item__allergens____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__allergenSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__basePrice____title": "Precio base", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____helperText": "Select a sequence number or leave blank to enter manual BOM revision numbers.", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____lookupDialogTitle": "Seleccionar número de secuencia de ubicación", "@sage/xtrem-master-data/pages__item__bomRevisionSequenceNumber____title": "BOM revision sequence number", "@sage/xtrem-master-data/pages__item__capacity____title": "Capacidad", "@sage/xtrem-master-data/pages__item__category____columns__title__sequenceNumber__name": "Número de secuencia", "@sage/xtrem-master-data/pages__item__category____lookupDialogTitle": "Seleccionar categoría de artículo", "@sage/xtrem-master-data/pages__item__category____title": "Categoría", "@sage/xtrem-master-data/pages__item__classifications____mobileCard__subtitle__title": "Id.", "@sage/xtrem-master-data/pages__item__commodityCode____title": "Código de mercancías", "@sage/xtrem-master-data/pages__item__commodityCodeEU____title": "Código de mercancías", "@sage/xtrem-master-data/pages__item__communityCodeEU____title": "Código de mercancías de la UE", "@sage/xtrem-master-data/pages__item__customers____columns__columns__salesUnit__name__title__3": "Símbolo", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__businessEntity__id": "Id. de cliente", "@sage/xtrem-master-data/pages__item__customers____columns__title__customer__name": "Cliente", "@sage/xtrem-master-data/pages__item__customers____columns__title__id": "Id. de artículo-cliente", "@sage/xtrem-master-data/pages__item__customers____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__item__customers____columns__title__maximumSalesQuantity": "Cantidad máxima de venta", "@sage/xtrem-master-data/pages__item__customers____columns__title__minimumSalesQuantity": "Cantidad mínima de venta", "@sage/xtrem-master-data/pages__item__customers____columns__title__name": "Artículo-cliente", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnit__name": "Unidad de venta", "@sage/xtrem-master-data/pages__item__customers____columns__title__salesUnitToStockUnitConversion": "Coeficiente de conversión de unidad de stock", "@sage/xtrem-master-data/pages__item__customers____dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__item__customers____inlineActions__title__openLinePanel": "Abrir panel de línea", "@sage/xtrem-master-data/pages__item__customers____mobileCard__title__title": "Cliente", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__item__customers____optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__item__customers____sidebar__headerDropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__item__customers____title": "Clientes", "@sage/xtrem-master-data/pages__item__customerSection____title": "Clientes", "@sage/xtrem-master-data/pages__item__customsUnitBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__density____title": "Densidad", "@sage/xtrem-master-data/pages__item__description____title": "Descripción", "@sage/xtrem-master-data/pages__item__eanNumber____title": "GTIN-13", "@sage/xtrem-master-data/pages__item__financialBlock____title": "Contabilidad", "@sage/xtrem-master-data/pages__item__financialSection____title": "Contabilidad", "@sage/xtrem-master-data/pages__item__generateId": "Generar id.", "@sage/xtrem-master-data/pages__item__generateNewId": "La categoría seleccionada ya está vinculada a un número de secuencia. ¿Quieres mantener el id. actual o generar uno nuevo?", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__id": "Id.", "@sage/xtrem-master-data/pages__item__ghsClassifications____columns__title__classification__name": "Nombre", "@sage/xtrem-master-data/pages__item__ghsClassifications____dropdownActions__title__remove": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__ghsClassifications____title": "Clasificaciones según SGA", "@sage/xtrem-master-data/pages__item__ghsClassificationSection____title": "Clasificación según SGA", "@sage/xtrem-master-data/pages__item__good_stock_block_title": "Stock", "@sage/xtrem-master-data/pages__item__good_stock_unit_title": "Unidad de stock", "@sage/xtrem-master-data/pages__item__headerSection____title": "Sección de cabecera", "@sage/xtrem-master-data/pages__item__image____title": "Imagen", "@sage/xtrem-master-data/pages__item__imageBlock____title": "Imagen", "@sage/xtrem-master-data/pages__item__inventoryBlock____title": "Stock", "@sage/xtrem-master-data/pages__item__isBomRevisionManaged____title": "Revisión de estructura de materiales", "@sage/xtrem-master-data/pages__item__isBought____title": "Comprado", "@sage/xtrem-master-data/pages__item__isExpiryManaged____title": "Gestión de fecha de caducidad", "@sage/xtrem-master-data/pages__item__isManufactured____title": "Fabricado", "@sage/xtrem-master-data/pages__item__isPhantom____title": "Fantasma", "@sage/xtrem-master-data/pages__item__isPotencyManagement____title": "Concentración", "@sage/xtrem-master-data/pages__item__isSold____title": "Vendido", "@sage/xtrem-master-data/pages__item__isStockManaged____title": "Gestión de stock", "@sage/xtrem-master-data/pages__item__isTraceabilityManagement____title": "Trazabilidad", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__indirectCostSection__name__title__3": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__item__name__title": "Nombre", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__item__name__title__2": "Id.", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title___id": "Id.", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__columns__title__symbol": "Símbolo", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__name__title__4": "Divisa financiera", "@sage/xtrem-master-data/pages__item__itemSites____columns__columns__site__title": "Sociedad", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__prodLeadTime": "día(s)", "@sage/xtrem-master-data/pages__item__itemSites____columns__postfix__purchaseLeadTime": "día(s)", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__batchQuantity": "Cantidad de tanda", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__economicOrderQuantity": "Cantidad económica de pedido", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__expectedQuantity": "Cantidad prevista", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__preferredProcess": "Proceso preferente", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__prodLeadTime": "Plazo de producción", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__replenishmentMethod": "Método de reaprovisionamiento", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__requiredQuantity": "Cantidad necesaria", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__safetyStock": "Stock de seguridad", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__stdCostValue": "Coste unitario", "@sage/xtrem-master-data/pages__item__itemSites____columns__title__valuationMethod": "Tipo de coste", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__item__itemSites____dropdownActions__title__edit": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__itemSites____title": "Artículos-plantas", "@sage/xtrem-master-data/pages__item__keepCurrentId": "Mantener id. actual", "@sage/xtrem-master-data/pages__item__lotManagement____title": "Gestión de lotes", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____lookupDialogTitle": "Seleccionar número de secuencia de lote", "@sage/xtrem-master-data/pages__item__lotSequenceNumber____title": "Número de secuencia de lote", "@sage/xtrem-master-data/pages__item__mainBlock____title": "Gestión", "@sage/xtrem-master-data/pages__item__mainSection____title": "Información", "@sage/xtrem-master-data/pages__item__managementSection____title": "Gestión", "@sage/xtrem-master-data/pages__item__manufacturingBlock____title": "Producción", "@sage/xtrem-master-data/pages__item__maximumSalesQuantity____title": "Cantidad máxima", "@sage/xtrem-master-data/pages__item__minimumPrice____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__minimumSalesQuantity____title": "Cantidad mínima", "@sage/xtrem-master-data/pages__item__name____title": "Nombre", "@sage/xtrem-master-data/pages__item__positionField1____title": "Campo de posición 1", "@sage/xtrem-master-data/pages__item__positionField2____title": "Campo de posición 2", "@sage/xtrem-master-data/pages__item__positionField3____title": "Campo de posición 3", "@sage/xtrem-master-data/pages__item__priceSection____title": "<PERSON>cios de proveedores", "@sage/xtrem-master-data/pages__item__purchase_unit_not_0_decimal_places": "La unidad de compra {{unitOfMeasure}} no puede tener decimales en los artículos que se gestionan por número de serie.", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__isActive": "Activa", "@sage/xtrem-master-data/pages__item__purchaseUnit____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__item__purchaseUnit____lookupDialogTitle": "Seleccionar unidad de compra", "@sage/xtrem-master-data/pages__item__purchaseUnitBlock____title": "Compras", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversion____title": "Coeficiente de conversión de unidad de stock", "@sage/xtrem-master-data/pages__item__purchaseUnitToStockUnitConversionDedicated____title": "Coeficiente específico", "@sage/xtrem-master-data/pages__item__sales_unit_not_0_decimal_places": "La unidad de venta {{unitOfMeasure}} no puede tener decimales en los artículos que se gestionan por número de serie.", "@sage/xtrem-master-data/pages__item__salesBlock____title": "Ventas", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__item__salesCurrency____columns__title__symbol": "Símbolo", "@sage/xtrem-master-data/pages__item__salesCurrency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__item__salesCurrency____title": "Divisa", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__currency__name__title": "Código ISO 4217", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title": "Nombre", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__customer__title__2": "Id.", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title": "Nombre", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__2": "Id.", "@sage/xtrem-master-data/pages__item__salesPrices____columns__columns__stockSite__title__3": "Sociedad", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__currency__name": "Seleccionar divisa", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__customer": "Seleccionar cliente", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__priceReason__name": "Seleccionar motivo de precio", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__salesSite": "Seleccionar planta", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__stockSite": "Seleccionar planta", "@sage/xtrem-master-data/pages__item__salesPrices____columns__lookupDialogTitle__unit__name": "Seleccionar unidad", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__charge": "Gasto", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__currency__name": "Divisa", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__businessEntity__id": "Id. de cliente", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__id": "Cliente", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__customer__name": "Cliente", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__discount": "Descuento", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__endDate": "<PERSON> validez", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__price": "Precio", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__priceReason__name": "Motivo de precio", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite": "Planta de venta", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__salesSite__name": "Planta de venta", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__startDate": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite": "Planta de stock", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__stockSite__id": "Planta de stock", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__id": "Unidad", "@sage/xtrem-master-data/pages__item__salesPrices____columns__title__unit__name": "Unidad", "@sage/xtrem-master-data/pages__item__salesPrices____dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__item__salesPrices____inlineActions__title__openLinePanel": "Abrir panel de línea", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__title__title": "Cliente", "@sage/xtrem-master-data/pages__item__salesPrices____mobileCard__titleRight__title": "Motivo de precio", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__item__salesPrices____optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__item__salesPrices____sidebar__headerDropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__item__salesPrices____title": "Precios para clientes", "@sage/xtrem-master-data/pages__item__salesPricesSection____title": "Precios de venta", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item__salesUnit____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__item__salesUnit____lookupDialogTitle": "Seleccionar unidad de venta", "@sage/xtrem-master-data/pages__item__salesUnitBlock____title": "Ventas", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversion____title": "Coeficiente de conversión de unidad de stock", "@sage/xtrem-master-data/pages__item__salesUnitToStockUnitConversionDedicated____title": "Coeficiente específico", "@sage/xtrem-master-data/pages__item__save____title": "", "@sage/xtrem-master-data/pages__item__saveItem____title": "Guardar", "@sage/xtrem-master-data/pages__item__selectId": "Seleccionar id.", "@sage/xtrem-master-data/pages__item__serialNumberManagement____title": "Gestión de números de serie", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____lookupDialogTitle": "Seleccionar número de serie de número de secuencia", "@sage/xtrem-master-data/pages__item__serialNumberSequenceNumber____title": "Secuencia de número de serie", "@sage/xtrem-master-data/pages__item__serialNumberUsage____title": "Uso de número de serie", "@sage/xtrem-master-data/pages__item__service_stock_block_title": "Unidad", "@sage/xtrem-master-data/pages__item__service_stock_unit_title": "Unidad base", "@sage/xtrem-master-data/pages__item__siteSection____title": "Plantas", "@sage/xtrem-master-data/pages__item__status____title": "Estado", "@sage/xtrem-master-data/pages__item__stock_unit_not_0_decimal_places": "La unidad de stock {{unitOfMeasure}} no puede tener decimales en los artículos que se gestionan por número de serie.", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__isActive": "Activa", "@sage/xtrem-master-data/pages__item__stockUnit____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__item__stockUnit____lookupDialogTitle": "Seleccionar unidad de stock", "@sage/xtrem-master-data/pages__item__stockUnit____title": "Unidad de stock", "@sage/xtrem-master-data/pages__item__storageBlock____title": "Características", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__currency__name__title": "Código ISO 4217", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title": "Nombre", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__2": "Id.", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__site__title__3": "Sociedad", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title": "Nombre", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__2": "Id.", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__3": "NIF-IVA", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__columns__supplier__title__4": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__currency__name": "Seleccionar divisa", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__site": "Seleccionar planta", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__supplier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__lookupDialogTitle__unit__name": "Seleccionar unidad", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__currency__name": "Divisa", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValid": "Validez", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidFrom": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__dateValidTo": "<PERSON> validez", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__price": "Precio", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__priority": "Prioridad", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__site__businessEntity__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__businessEntity__id": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__supplier__name": "Nombre", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__id": "Unidad", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__name": "Unidad de medida", "@sage/xtrem-master-data/pages__item__supplierPrices____columns__title__unit__symbol": "Símbolo", "@sage/xtrem-master-data/pages__item__supplierPrices____dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__item__supplierPrices____inlineActions__title__openLinePanel": "Abrir panel de línea", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__supplierPrices____mobileCard__titleRight__title": "Tipo", "@sage/xtrem-master-data/pages__item__supplierPrices____sidebar__headerDropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__item__supplierPrices____title": "<PERSON>cios de proveedores", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title": "Nombre", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__2": "Id.", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__3": "NIF-IVA", "@sage/xtrem-master-data/pages__item__suppliers____columns__columns__supplier__title__4": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__purchaseUnitOfMeasure": "Seleccionar unidad", "@sage/xtrem-master-data/pages__item__suppliers____columns__lookupDialogTitle__supplier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__isDefaultItemSupplier": "Proveedor por defecto", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__minimumPurchaseQuantity": "Cantidad mínima de compra", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__purchaseUnitOfMeasure": "Unidad de compra", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier___id": "Id.", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__businessEntity__id": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplier__name": "Nombre", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemCode": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____columns__title__supplierItemName": "Art<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____dropdownActions__title__delete": "Eliminar", "@sage/xtrem-master-data/pages__item__suppliers____inlineActions__title__openLinePanel": "Abrir panel de línea", "@sage/xtrem-master-data/pages__item__suppliers____mobileCard__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__item__suppliers____optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__item__suppliers____sidebar__headerDropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__item__suppliers____title": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/pages__item__supplierSection____title": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/pages__item__type____title": "Tipo", "@sage/xtrem-master-data/pages__item__typeBlock____title": "Información de artículo", "@sage/xtrem-master-data/pages__item__unitBlock____title": "Stock", "@sage/xtrem-master-data/pages__item__unitSection____title": "Unidades", "@sage/xtrem-master-data/pages__item__volume____title": "Volumen", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item__volumeUnit____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__item__volumeUnit____lookupDialogTitle": "Seleccionar unidad de volumen", "@sage/xtrem-master-data/pages__item__weight____title": "Peso", "@sage/xtrem-master-data/pages__item__weightUnit____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item__weightUnit____lookupDialogTitle": "Seleccionar unidad de peso", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__isSequenceNumberManagement__title": "Gestión de números de secuencia", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__sequenceNumber__title": "Número de secuencia de id. de artículo", "@sage/xtrem-master-data/pages__item_category____navigationPanel__listItem__type__title": "Tipo", "@sage/xtrem-master-data/pages__item_category____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_category____objectTypePlural": "Categorías de artículo", "@sage/xtrem-master-data/pages__item_category____objectTypeSingular": "Categoría de artículo", "@sage/xtrem-master-data/pages__item_category____title": "Categoría de artículo", "@sage/xtrem-master-data/pages__item_category__declarationsBlock____title": "Declaraciones", "@sage/xtrem-master-data/pages__item_category__generalBlock____title": "General", "@sage/xtrem-master-data/pages__item_category__generalSection____title": "General", "@sage/xtrem-master-data/pages__item_category__id____title": "Id.", "@sage/xtrem-master-data/pages__item_category__isSequenceNumberManagement____title": "Gestión de números de secuencia", "@sage/xtrem-master-data/pages__item_category__name____title": "Nombre", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____lookupDialogTitle": "Seleccionar número de secuencia de id. de artículo", "@sage/xtrem-master-data/pages__item_category__sequenceNumber____title": "Número de secuencia de id. de artículo", "@sage/xtrem-master-data/pages__item_category__type____title": "Declaraciones", "@sage/xtrem-master-data/pages__item_customer__edit____title": "Editar <PERSON>-cliente", "@sage/xtrem-master-data/pages__item_customer_panel____title": "<PERSON><PERSON><PERSON>-cliente", "@sage/xtrem-master-data/pages__item_customer_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_panel__id____title": "Id. de artículo-cliente", "@sage/xtrem-master-data/pages__item_customer_panel__item____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__item_customer_panel__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_panel__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_panel__item_is_inactive": "El artículo no está activo. Su estado es \"{{status}}\".", "@sage/xtrem-master-data/pages__item_customer_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_customer_panel__maximumSalesQuantity____title": "Cantidad máxima de venta", "@sage/xtrem-master-data/pages__item_customer_panel__minimumSalesQuantity____title": "Cantidad mínima de venta", "@sage/xtrem-master-data/pages__item_customer_panel__name____title": "Nombre de artículo-cliente", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____lookupDialogTitle": "Seleccionar unidad de venta", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnit____title": "Unidad de venta", "@sage/xtrem-master-data/pages__item_customer_panel__salesUnitToStockUnitConversion____title": "Coeficiente de conversión de unidad de stock", "@sage/xtrem-master-data/pages__item_customer_panel__save____title": "Aceptar", "@sage/xtrem-master-data/pages__item_customer_price_panel____title": "Precio para cliente", "@sage/xtrem-master-data/pages__item_customer_price_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__charge____title": "Gasto", "@sage/xtrem-master-data/pages__item_customer_price_panel__confirm____title": "Guardar", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__item_customer_price_panel__currency____title": "Divisa", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__id": "Id.", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____lookupDialogTitle": "Seleccionar cliente", "@sage/xtrem-master-data/pages__item_customer_price_panel__customer____title": "Cliente", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____postfix": "%", "@sage/xtrem-master-data/pages__item_customer_price_panel__discount____title": "Descuento", "@sage/xtrem-master-data/pages__item_customer_price_panel__edit____title": "Editar precio de venta", "@sage/xtrem-master-data/pages__item_customer_price_panel__endDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_customer_price_panel__fromQuantity____title": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__new____title": "Nuevo precio de venta", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_greater_than_100": "Introduce un porcentaje inferior a 100.", "@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_is_negative": "Introduce un porcentaje positivo.", "@sage/xtrem-master-data/pages__item_customer_price_panel__price____title": "Precio", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____lookupDialogTitle": "Seleccionar motivo de precio", "@sage/xtrem-master-data/pages__item_customer_price_panel__priceReason____title": "Motivo de precio", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____lookupDialogTitle": "Seleccionar planta de venta", "@sage/xtrem-master-data/pages__item_customer_price_panel__salesSite____title": "Planta de venta", "@sage/xtrem-master-data/pages__item_customer_price_panel__startDate____title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____lookupDialogTitle": "Seleccionar planta de stock", "@sage/xtrem-master-data/pages__item_customer_price_panel__stockSite____title": "Planta de stock", "@sage/xtrem-master-data/pages__item_customer_price_panel__toQuantity____title": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____lookupDialogTitle": "Seleccionar unidad", "@sage/xtrem-master-data/pages__item_customer_price_panel__unit____title": "Unidad de medida", "@sage/xtrem-master-data/pages__item_customer_price_panel__validUnits____title": "Unidades válidas", "@sage/xtrem-master-data/pages__item_customer_price_view_panel____title": "Consulta de lista de precios", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__idBlock____title": "Id.", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__columns__priceReason__name__title": "Prioridad", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__charge": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__postfix__discount": "%", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title___id": "Id.", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__currency__id": "Divisa", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__customer__id": "Cliente", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__endDate": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__price": "Precio", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__priceReason__name": "Motivo de precio", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__id": "Planta de venta", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__salesSite__name": "Planta de venta", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__startDate": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__id": "Planta de stock", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__stockSite__name": "Planta de stock", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__items____columns__title__unit__id": "Unidad", "@sage/xtrem-master-data/pages__item_customer_price_view_panel__mainSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel____title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__item_price_panel___id____title": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__currency____columns__title__symbol": "Símbolo", "@sage/xtrem-master-data/pages__item_price_panel__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__item_price_panel__currency____title": "Divisa", "@sage/xtrem-master-data/pages__item_price_panel__dateValid____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__edit____title": "<PERSON><PERSON> precio de <PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__fromDate____title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_price_panel__fromQuantity____title": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title": "Descripción", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__2": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__columns__stockUnit__description__title__3": "Símbolo", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__item____columns__title__stockUnit__description": "Descripción", "@sage/xtrem-master-data/pages__item_price_panel__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__new____title": "Nuevo precio de proveedor", "@sage/xtrem-master-data/pages__item_price_panel__price____title": "Precio", "@sage/xtrem-master-data/pages__item_price_panel__priority____title": "Prioridad", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__2": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__columns__legalCompany__name__title__3": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__item_price_panel__site____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__item_price_panel__site____title": "Planta", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__2": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__country__name__title__3": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__2": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__3": "Símbolo", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__columns__currency__name__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__currency__name": "Divisa", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__supplier____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/pages__item_price_panel__supplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__toDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_price_panel__toQuantity____title": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__type____title": "Tipo", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__item_price_panel__unit____columns__title__symbol": "Símbolo", "@sage/xtrem-master-data/pages__item_price_panel__unit____lookupDialogTitle": "Seleccionar unidad", "@sage/xtrem-master-data/pages__item_price_panel__unit____title": "Unidad de medida", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__averageDailyConsumption__title": "Consumo medio diario", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__batchQuantity__title": "Cantidad de tanda", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__economicOrderQuantity__title": "Cantidad económica de pedido", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__expectedQuantity__title": "Prevista", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemCategory__title": "Categoría de artículo", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemDescription__title": "Descripción de artículo", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__itemId__title": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__preferredProcess__title": "Proceso preferente", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__postfix": "días", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__purchaseLeadTime__title": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__reorderPoint__title": "Punto de pedido", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__replenishmentMethod__title": "Método de reaprovisionamiento", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__requiredQuantity__title": "Obligatoria", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__safetyStock__title": "Stock de seguridad", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__stdCostValue__title": "<PERSON>ste <PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__title__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____navigationPanel__listItem__valuationMethod__title": "Método de valoración", "@sage/xtrem-master-data/pages__item_site____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site____objectTypePlural": "Artículos-plantas", "@sage/xtrem-master-data/pages__item_site____objectTypeSingular": "Artículo-planta", "@sage/xtrem-master-data/pages__item_site____title": "Artículo-planta", "@sage/xtrem-master-data/pages__item_site___id____title": "Id.", "@sage/xtrem-master-data/pages__item_site___valuationMethod____title": "Método de valoración", "@sage/xtrem-master-data/pages__item_site__addItemSiteCost____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__addSupplier____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__averageDailyConsumption____title": "Consumo medio diario", "@sage/xtrem-master-data/pages__item_site__batchQuantity____title": "Cantidad de tanda", "@sage/xtrem-master-data/pages__item_site__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____lookupDialogTitle": "Seleccionar ubicación por defecto", "@sage/xtrem-master-data/pages__item_site__completedProductDefaultLocation____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__item_site__costBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__costCategoryType": "Tipo", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_site__costCategory____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__item_site__costCategory____title": "Categoría de coste", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title": "Tipo", "@sage/xtrem-master-data/pages__item_site__costs____columns__columns__costCategory__name__title__2": "Obligatoria", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__costCategory__name": "Categoría de coste", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__forQuantity": "Cantidad", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__fromDate": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__isCalculated": "Calculado", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__totalCost": "Coste total", "@sage/xtrem-master-data/pages__item_site__costs____columns__title__unitCost": "Coste unitario", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__2": "Eliminar", "@sage/xtrem-master-data/pages__item_site__costs____dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costs____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costsBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__costSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__counting-in-progress": "Inventario en curso", "@sage/xtrem-master-data/pages__item_site__countingInProgress____title": "Inventario en curso", "@sage/xtrem-master-data/pages__item_site__countingInProgressMention____title": "Inventario en curso", "@sage/xtrem-master-data/pages__item_site__economicOrderQuantity____title": "Cantidad económica de pedido", "@sage/xtrem-master-data/pages__item_site__edit____title": "Editar artículo-planta", "@sage/xtrem-master-data/pages__item_site__expectedQuantity____title": "Cantidad prevista", "@sage/xtrem-master-data/pages__item_site__forQuantity____title": "Cantidad", "@sage/xtrem-master-data/pages__item_site__fromDate____title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_site__id____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____lookupDialogTitle": "Seleccionar ubicación por defecto", "@sage/xtrem-master-data/pages__item_site__inboundDefaultLocation____title": "Entrada", "@sage/xtrem-master-data/pages__item_site__indirectCost____title": "Indirecto", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____lookupDialogTitle": "Selecciona sección de costes indirectos", "@sage/xtrem-master-data/pages__item_site__indirectCostSection____title": "Sección de coste indirecto", "@sage/xtrem-master-data/pages__item_site__isCalculated____title": "Calculado", "@sage/xtrem-master-data/pages__item_site__isOrderToOrder____title": "Pedido por pedido", "@sage/xtrem-master-data/pages__item_site__item____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__item_site__item____columns__title__description": "Descripción", "@sage/xtrem-master-data/pages__item_site__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__laborCost____title": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__item_site__locationBlock____title": "Ubicación por defecto", "@sage/xtrem-master-data/pages__item_site__machineCost____title": "Máquina", "@sage/xtrem-master-data/pages__item_site__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_site__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site__new____title": "Nuevo artículo-planta", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____lookupDialogTitle": "Seleccionar ubicación por defecto", "@sage/xtrem-master-data/pages__item_site__outboundDefaultLocation____title": "Salida", "@sage/xtrem-master-data/pages__item_site__preferredProcess____title": "Proceso preferente", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____postfix": "día(s)", "@sage/xtrem-master-data/pages__item_site__prodLeadTime____title": "Plazo de producción", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____postfix": "día(s)", "@sage/xtrem-master-data/pages__item_site__purchaseLeadTime____title": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__item_site__qualityControlBlock____title": "Estado de control de calidad por defecto", "@sage/xtrem-master-data/pages__item_site__reorderPoint____title": "Punto de pedido", "@sage/xtrem-master-data/pages__item_site__replenishmentBlock____title": "Reaprovisionamiento", "@sage/xtrem-master-data/pages__item_site__replenishmentMethod____title": "Método de reaprovisionamiento", "@sage/xtrem-master-data/pages__item_site__replenishmentSection____title": "Reaprovisionamiento", "@sage/xtrem-master-data/pages__item_site__requiredQuantity____title": "Cantidad necesaria", "@sage/xtrem-master-data/pages__item_site__safetyStock____title": "Stock de seguridad", "@sage/xtrem-master-data/pages__item_site__saveItemSite____title": "Guardar", "@sage/xtrem-master-data/pages__item_site__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__item_site__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__item_site__stdCostBlock____title": "<PERSON>ste <PERSON>", "@sage/xtrem-master-data/pages__item_site__stdCostValue____title": "<PERSON>ste <PERSON>", "@sage/xtrem-master-data/pages__item_site__stockBlock____title": "Stock", "@sage/xtrem-master-data/pages__item_site__stockRulesSection____title": "Reglas de stock", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title___id": "Id.", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__isDefaultItemSupplier": "Por defecto", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__minimumPurchaseOrderQuantity": "Cantidad mínima de compra", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__purchaseUnit__name": "Unidad", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__id": "<PERSON><PERSON>. de <PERSON>edor", "@sage/xtrem-master-data/pages__item_site__suppliers____columns__title__supplier__businessEntity__name": "Nombre de proveedor", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliers____dropdownActions__title__2": "Eliminar", "@sage/xtrem-master-data/pages__item_site__suppliers____title": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliersBlock____title": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/pages__item_site__suppliersSection____title": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/pages__item_site__toDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_site__toolCost____title": "Herramienta", "@sage/xtrem-master-data/pages__item_site__totalCost____title": "Total coste", "@sage/xtrem-master-data/pages__item_site__unitCost____title": "Coste unitario", "@sage/xtrem-master-data/pages__item_site__version____title": "Versión", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__listItem__line3__title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_site_cost____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__item_site_cost____objectTypePlural": "Costes de artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost____objectTypeSingular": "Coste de artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost____title": "Coste de artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost___id____title": "Id.", "@sage/xtrem-master-data/pages__item_site_cost__calculate____title": "Calcular", "@sage/xtrem-master-data/pages__item_site_cost__calculateAction____title": "Calcular", "@sage/xtrem-master-data/pages__item_site_cost__chartBlock____title": "Gráfico de costes", "@sage/xtrem-master-data/pages__item_site_cost__costBlock____title": "Detalles de costes", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__costCategoryType": "Tipo", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____columns__title__isMandatory": "Obligatoria", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____lookupDialogTitle": "Seleccionar categoría de coste", "@sage/xtrem-master-data/pages__item_site_cost__costCategory____title": "Categoría de coste", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriod____title": "Nuevo periodo", "@sage/xtrem-master-data/pages__item_site_cost__createNewPeriodAction____title": "Nuevo periodo", "@sage/xtrem-master-data/pages__item_site_cost__forQuantity____title": "Cantidad", "@sage/xtrem-master-data/pages__item_site_cost__fromDate____title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_site_cost__isCalculated____title": "Calculado", "@sage/xtrem-master-data/pages__item_site_cost__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__name": "Nombre de artículo", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__status": "Estado de artículo", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__item__type": "Tipo de artículo", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site": "Nombre de planta", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____lookupDialogTitle": "Seleccionar artí<PERSON>-planta", "@sage/xtrem-master-data/pages__item_site_cost__itemSite____title": "Artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost__laborCost____title": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__item_site_cost__machineCost____title": "Máquina", "@sage/xtrem-master-data/pages__item_site_cost__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_site_cost__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site_cost__site____title": "Planta", "@sage/xtrem-master-data/pages__item_site_cost__toDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_site_cost__toolCost____title": "Herramienta", "@sage/xtrem-master-data/pages__item_site_cost__totalCost____title": "Coste total", "@sage/xtrem-master-data/pages__item_site_cost__unitCost____title": "Coste unitario", "@sage/xtrem-master-data/pages__item_site_cost__version____title": "Versión", "@sage/xtrem-master-data/pages__item_site_cost_panel____subtitle": "Panel de costes de artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost_panel____title": "Panel de costes de artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost_panel___id____title": "Id.", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_cost_panel__chartBlock____title": "Gráfico de costes", "@sage/xtrem-master-data/pages__item_site_cost_panel__costBlock____title": "Detalles de costes", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__costCategoryType": "Tipo", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____columns__title__isMandatory": "Obligatoria", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____lookupDialogTitle": "Seleccionar categoría de coste", "@sage/xtrem-master-data/pages__item_site_cost_panel__costCategory____title": "Categoría de coste", "@sage/xtrem-master-data/pages__item_site_cost_panel__costChart____title": "Gráfico de costes", "@sage/xtrem-master-data/pages__item_site_cost_panel__deleteAction____title": "Eliminar", "@sage/xtrem-master-data/pages__item_site_cost_panel__forQuantity____title": "Cantidad", "@sage/xtrem-master-data/pages__item_site_cost_panel__fromDate____title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_site_cost_panel__invalid-from-date": "La fecha de inicio no puede ser anterior a la fecha actual.", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__item__id__columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__item__id__columns__title__symbol": "Símbolo", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__columns__site__id__title": "Símbolo", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__batchQuantity": "Cantidad de tanda", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__item__name": "Nombre de artículo", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____columns__title__site__name": "Nombre de planta", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____lookupDialogTitle": "Seleccionar artí<PERSON>-planta", "@sage/xtrem-master-data/pages__item_site_cost_panel__itemSite____title": "Artículo-planta", "@sage/xtrem-master-data/pages__item_site_cost_panel__laborCost____title": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__item_site_cost_panel__machineCost____title": "Máquina", "@sage/xtrem-master-data/pages__item_site_cost_panel__materialCost____title": "Material", "@sage/xtrem-master-data/pages__item_site_cost_panel__save____title": "Guardar", "@sage/xtrem-master-data/pages__item_site_cost_panel__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__item_site_cost_panel__toDate____title": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_site_cost_panel__toolCost____title": "Herramienta", "@sage/xtrem-master-data/pages__item_site_cost_panel__totalCost____title": "Coste total", "@sage/xtrem-master-data/pages__item_site_cost_panel__unitCost____title": "Coste unitario", "@sage/xtrem-master-data/pages__item_site_cost_panel__version____title": "Versión", "@sage/xtrem-master-data/pages__item_site_supplier____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypePlural": "Artículos-plantas-proveedores", "@sage/xtrem-master-data/pages__item_site_supplier____objectTypeSingular": "Artículo-planta-proveedor", "@sage/xtrem-master-data/pages__item_site_supplier____title": "Artículo-planta-proveedor", "@sage/xtrem-master-data/pages__item_site_supplier___id____title": "Id.", "@sage/xtrem-master-data/pages__item_site_supplier__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__item_site_supplier__createItemSiteSupplier____title": "Nuevo", "@sage/xtrem-master-data/pages__item_site_supplier__deleteItemSiteSupplier____title": "Eliminar", "@sage/xtrem-master-data/pages__item_site_supplier__identificationBlock____title": "Identificación", "@sage/xtrem-master-data/pages__item_site_supplier__isDefaultItemSupplier____title": "Artículo-planta-proveedor por defecto", "@sage/xtrem-master-data/pages__item_site_supplier__itemDetailsBlock____title": "Detalles de artículo", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__name": "Nombre de artículo", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__status": "Estado de artículo", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__item__type": "Tipo de artículo", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site": "Nombre de planta", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____lookupDialogTitle": "Seleccionar artí<PERSON>-planta", "@sage/xtrem-master-data/pages__item_site_supplier__itemSite____title": "Artículo-planta", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__id": "Id.", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__name": "Nombre", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____columns__title__supplier__businessEntity__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__itemSupplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__mainSection____title": "General", "@sage/xtrem-master-data/pages__item_site_supplier__minimumPurchaseOrderQuantity____title": "Cantidad mínima de compra", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____postfix": "días", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseLeadTime____title": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__item_site_supplier__purchaseUnit____lookupDialogTitle": "Seleccionar unidad de compra", "@sage/xtrem-master-data/pages__item_site_supplier__saveItemSiteSupplier____title": "Guardar", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__id": "Id.", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__businessEntity__name__2": "Id.", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____columns__title__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/pages__item_site_supplier__supplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier__item_cost_edit____title": "Editar coste de artículo-planta", "@sage/xtrem-master-data/pages__item_supplier__item_cost_new____title": "Nuevo coste de artículo-planta", "@sage/xtrem-master-data/pages__item_supplier__item_edit____title": "Editar art<PERSON>-planta-<PERSON>edor", "@sage/xtrem-master-data/pages__item_supplier__item_new____title": "Nuevo artículo-planta-proveedor", "@sage/xtrem-master-data/pages__item_supplier__purchaseUnitOfMeasure____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__item_supplier_price_panel____title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____columns__title__id": "Código ISO 4217", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__item_supplier_price_panel__currency____title": "Divisa", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValid____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidFrom____title": "Fecha de inicio", "@sage/xtrem-master-data/pages__item_supplier_price_panel__dateValidTo____title": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__item_supplier_price_panel__fromQuantity____title": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__price____title": "Precio", "@sage/xtrem-master-data/pages__item_supplier_price_panel__priority____title": "Prioridad", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__item_supplier_price_panel__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__id": "Id.", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____columns__title__businessEntity__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/pages__item_supplier_price_panel__supplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__toQuantity____title": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_panel__type____title": "Tipo", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____lookupDialogTitle": "Seleccionar unidad", "@sage/xtrem-master-data/pages__item_supplier_price_panel__unit____title": "Unidad de medida", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel____title": "Consulta de lista de precios", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel___id____title": "Id.", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__idBlock____title": "Id.", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__currency__id": "Divisa", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidFrom": "Fecha de inicio de validez", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__dateValidTo": "Fecha de fin de validez", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__site__id": "Planta", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__items____columns__title__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__item_supplier_price_view_panel__mainSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__item-customer_panel__conversion_negative_value": "La conversión de venta a stock no debe ser inferior o igual a 0.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_less_than_minimum_value": "La cantidad máxima de venta no debe ser inferior a la cantidad mínima de venta.", "@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_negative_value": "La cantidad máxima de venta no debe ser inferior a 0.", "@sage/xtrem-master-data/pages__item-customer_panel__minimum_quantity_negative_value": "La cantidad mínima de venta no debe ser inferior a 0.", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_buy_to_order": "Compra por pedido", "@sage/xtrem-master-data/pages__item-site__order_to_order_title_make_to_order": "Fabricación por pedido", "@sage/xtrem-master-data/pages__item-site__preferred_process_cannot_be": "Un artículo de stock debe haberse fabricado, comprado o ambas cosas.", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeFrom__title": "Activa desde", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__activeTo__title": "Activa hasta", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__image__title": "Imagen", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__resourceGroup__title": "Grupo de recursos", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__listItem__weeklyShift__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__labor_resource____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__labor_resource____objectTypePlural": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__labor_resource____objectTypeSingular": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__labor_resource____title": "<PERSON>o de obra", "@sage/xtrem-master-data/pages__labor_resource___id____title": "Id.", "@sage/xtrem-master-data/pages__labor_resource__activeFrom____title": "Activa desde", "@sage/xtrem-master-data/pages__labor_resource__activeTo____title": "Activa hasta", "@sage/xtrem-master-data/pages__labor_resource__addCapability____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__addCostCategory____title": "Añadir categoría de coste", "@sage/xtrem-master-data/pages__labor_resource__blockCapabilities____title": "Aptitudes", "@sage/xtrem-master-data/pages__labor_resource__blockDetails____title": "Configuración", "@sage/xtrem-master-data/pages__labor_resource__blockWeekly____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__labor_resource__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__cancelSidePanel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__capabilityLevel__name": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateEndValid": "<PERSON><PERSON> de fin", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__dateStartValid": "Fecha de inicio", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__machine__name": "Máquina", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__service__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__capabilities____columns__title__tool__name": "Herramienta", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__labor_resource__capabilities____dropdownActions__title__2": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__capabilities____title": "Aptitudes", "@sage/xtrem-master-data/pages__labor_resource__capabilitiesSection____title": "Aptitudes", "@sage/xtrem-master-data/pages__labor_resource__costBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__costSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__description____title": "Descripción", "@sage/xtrem-master-data/pages__labor_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__labor_resource__efficiency____title": "Rendimiento", "@sage/xtrem-master-data/pages__labor_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__labor_resource__id____title": "Id.", "@sage/xtrem-master-data/pages__labor_resource__isActive____title": "Activa", "@sage/xtrem-master-data/pages__labor_resource__location____columns__title__locationType__id": "Tipo", "@sage/xtrem-master-data/pages__labor_resource__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-master-data/pages__labor_resource__location____title": "Ubicación", "@sage/xtrem-master-data/pages__labor_resource__name____title": "Nombre", "@sage/xtrem-master-data/pages__labor_resource__resourceCapacity____title": "Capacidad semanal", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Tipo de categoría de coste", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoria", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__columns__costUnit__name__title__3": "Símbolo", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costCategory__name": "Categoría de coste", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__costUnit__name": "Unidad de coste", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coste indirecto", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__runCost": "Operacional", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__labor_resource__resourceCostCategories____title": "Categorías de costes de recursos", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____lookupDialogTitle": "Seleccionar grupo de recursos", "@sage/xtrem-master-data/pages__labor_resource__resourceGroup____title": "Grupo de recursos", "@sage/xtrem-master-data/pages__labor_resource__resourceImage____title": "Imagen", "@sage/xtrem-master-data/pages__labor_resource__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__labor_resource__saveSidePanel____title": "Guardar", "@sage/xtrem-master-data/pages__labor_resource__section____title": "General", "@sage/xtrem-master-data/pages__labor_resource__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__labor_resource__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__capacity": "Capacidad", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__dailyShift": "Turno diario", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__day": "Día", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift1": "Turno 1", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift2": "Turno 2", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift3": "Turno 3", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift4": "Turno 4", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____columns__title__shift5": "Turno 5", "@sage/xtrem-master-data/pages__labor_resource__weeklyDetails____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____columns__title__formattedCapacity": "Capacidad", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____lookupDialogTitle": "Seleccionar turno semanal", "@sage/xtrem-master-data/pages__labor_resource__weeklyShift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__postfix": "kg", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__consumedCapacity__title": "Capacidad utilizada", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isInternalIcon__title": "Interno", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__isSingleLot__title": "Monolote", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__line2__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__location__title": "Ubicación", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__sSingleItem__title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__listItem__titleRight__title": "Tipo", "@sage/xtrem-master-data/pages__license_plate_number____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__license_plate_number____objectTypePlural": "Números de contenedor interno", "@sage/xtrem-master-data/pages__license_plate_number____objectTypeSingular": "Número de contenedor interno", "@sage/xtrem-master-data/pages__license_plate_number____title": "Número de contenedor interno", "@sage/xtrem-master-data/pages__license_plate_number___id____title": "Id.", "@sage/xtrem-master-data/pages__license_plate_number__consumedCapacity____title": "Capacidad utilizada", "@sage/xtrem-master-data/pages__license_plate_number__container____columns__title__isInternal": "Interno", "@sage/xtrem-master-data/pages__license_plate_number__container____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> contenedor", "@sage/xtrem-master-data/pages__license_plate_number__container____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__containerType____title": "<PERSON><PERSON><PERSON> de contenedor", "@sage/xtrem-master-data/pages__license_plate_number__isInternal____title": "Interno", "@sage/xtrem-master-data/pages__license_plate_number__isSingleItem____title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number__isSingleLot____title": "Monolote", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__locationType__id__title": "Nombre", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__locationType__id__title__2": "Id.", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__site__name__title": "Nombre", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__columns__site__name__title__2": "Id.", "@sage/xtrem-master-data/pages__license_plate_number__location____columns__title__locationType__id": "Tipo", "@sage/xtrem-master-data/pages__license_plate_number__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-master-data/pages__license_plate_number__location____title": "Ubicación", "@sage/xtrem-master-data/pages__license_plate_number__locationSite____title": "Planta", "@sage/xtrem-master-data/pages__license_plate_number__locationType____title": "Tipo de ubicación", "@sage/xtrem-master-data/pages__license_plate_number__mainSection____title": "General", "@sage/xtrem-master-data/pages__license_plate_number__mass_update__success": "Números de contenedor interno actualizados", "@sage/xtrem-master-data/pages__license_plate_number__number____title": "Número", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__id": "Id.", "@sage/xtrem-master-data/pages__license_plate_number__owner____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-master-data/pages__license_plate_number__owner____lookupDialogTitle": "Seleccionar propietario", "@sage/xtrem-master-data/pages__license_plate_number__owner____title": "Propietario", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation____title": "Creación masiva de números de contenedor interno", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__cancelLicensePlateNumbers____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleItem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__isSingleLot": "Monolote", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____columns__title__sequenceNumber__id": "Número de secuencia", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> contenedor", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__container____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleItem____title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__isSingleLot____title": "Monolote", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__columns__title__id": "Id.", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__columns__location__name__title": "Tipo", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleItem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__isSingleLot": "Monolote", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__locationType__name": "Tipo", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__name": "Ubicación", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____columns__title__location__site__name": "Planta", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbers____title": "Números de contenedor interno", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__licensePlateNumbersBlock____title": "Números de contenedor interno", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____columns__title__locationType__name": "Tipo", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__location____title": "Ubicación", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationSite____title": "Planta", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__locationType____title": "Tipo", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__mainSection____title": "General", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__numberToCreate____title": "Número por generar", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__saveLicensePlateNumbers____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__license_plate_number_mass_creation__searchButton____title": "Generar", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__dangerousGoodAllowed__title": "Sustancias peligrosas permitidas", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationType__title": "Tipo", "@sage/xtrem-master-data/pages__location____navigationPanel__listItem__locationZone__title": "Á<PERSON>", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__location____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__location____objectTypePlural": "Ubicaciones", "@sage/xtrem-master-data/pages__location____objectTypeSingular": "Ubicación", "@sage/xtrem-master-data/pages__location____title": "Ubicación", "@sage/xtrem-master-data/pages__location__dangerousGoodAllowed____title": "Sustancias peligrosas permitidas", "@sage/xtrem-master-data/pages__location__id____title": "Id.", "@sage/xtrem-master-data/pages__location__locationType____columns__title__locationCategory": "Categoría", "@sage/xtrem-master-data/pages__location__locationType____lookupDialogTitle": "Seleccionar tipo de ubicación", "@sage/xtrem-master-data/pages__location__locationType____title": "Tipo", "@sage/xtrem-master-data/pages__location__locationZone____columns__title__zoneType": "Tipo", "@sage/xtrem-master-data/pages__location__locationZone____lookupDialogTitle": "Seleccionar área de almacenamiento", "@sage/xtrem-master-data/pages__location__locationZone____title": "Á<PERSON>", "@sage/xtrem-master-data/pages__location__name____title": "Nombre", "@sage/xtrem-master-data/pages__location__section____title": "General", "@sage/xtrem-master-data/pages__location__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__location__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__location_mass_creation____title": "Creación masiva de ubicaciones", "@sage/xtrem-master-data/pages__location_mass_creation__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__createLocations____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationType__name__title": "Categoría", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__columns__locationZone__name__title": "Tipo", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationType__name": "Tipo", "@sage/xtrem-master-data/pages__location_mass_creation__locations____columns__title__locationZone__name": "Á<PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__locations____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__location_mass_creation__locationsBlock____title": "Ubicaciones", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__lastSequenceUsed": "Última secuencia utilizada", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberLocationsRemaining": "Asignaciones restantes", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____columns__title__numberOfCombinations": "Total asignaciones", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____lookupDialogTitle": "Seleccionar número de secuencia de ubicación", "@sage/xtrem-master-data/pages__location_mass_creation__locationSequence____title": "Número de secuencia de ubicación", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____columns__title__locationCategory": "Categoría", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____lookupDialogTitle": "Seleccionar tipo de ubicación", "@sage/xtrem-master-data/pages__location_mass_creation__locationType____title": "Tipo", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____columns__title__zoneType": "Tipo", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____lookupDialogTitle": "Seleccionar área de almacenamiento", "@sage/xtrem-master-data/pages__location_mass_creation__locationZone____title": "Á<PERSON>", "@sage/xtrem-master-data/pages__location_mass_creation__mainSection____title": "General", "@sage/xtrem-master-data/pages__location_mass_creation__requiredCombinations____title": "Combinaciones necesarias", "@sage/xtrem-master-data/pages__location_mass_creation__searchButton____title": "Buscar", "@sage/xtrem-master-data/pages__location_mass_creation__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__location_mass_creation__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__location_mass_creation__site____placeholder": "Seleccionar...", "@sage/xtrem-master-data/pages__location_mass_creation__site____title": "Planta", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__componentLength__title": "Longitud de secuencia", "@sage/xtrem-master-data/pages__location_sequence____navigationPanel__listItem__numberOfCombinations__title": "Número de ubicaciones", "@sage/xtrem-master-data/pages__location_sequence____objectTypePlural": "Número de secuencia de ubicación", "@sage/xtrem-master-data/pages__location_sequence____objectTypeSingular": "Número de secuencia de ubicación", "@sage/xtrem-master-data/pages__location_sequence____title": "Número de secuencia de ubicación", "@sage/xtrem-master-data/pages__location_sequence__addComponent____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__capital_letters_only": "El componente alfabético de un número de secuencia solo puede tener mayúsculas.", "@sage/xtrem-master-data/pages__location_sequence__componentLength____title": "Longitud de secuencia", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__endValue": "Valor de fin", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__startValue": "Valor de inicio", "@sage/xtrem-master-data/pages__location_sequence__components____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__location_sequence__components____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__location_sequence__components____title": "Componentes", "@sage/xtrem-master-data/pages__location_sequence__componentsBlock____title": "Componentes", "@sage/xtrem-master-data/pages__location_sequence__constant_invalid_length": "Check the length of the constant value.", "@sage/xtrem-master-data/pages__location_sequence__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_sequence__deleteAction____title": "Eliminar", "@sage/xtrem-master-data/pages__location_sequence__digits_only": "A numeric sequence number component can only contain numbers.", "@sage/xtrem-master-data/pages__location_sequence__id____title": "Id.", "@sage/xtrem-master-data/pages__location_sequence__invalid_range": "El valor de inicio no puede ser superior al valor de fin.", "@sage/xtrem-master-data/pages__location_sequence__mainBlock____title": "Detalles", "@sage/xtrem-master-data/pages__location_sequence__mainSection____title": "General", "@sage/xtrem-master-data/pages__location_sequence__name____title": "Nombre", "@sage/xtrem-master-data/pages__location_sequence__numberOfCombinations____title": "Número de ubicaciones", "@sage/xtrem-master-data/pages__location_sequence__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__location_type____navigationPanel__listItem__locationCategory__title": "Categoría", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__2": "Interna", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__3": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__4": "Cliente", "@sage/xtrem-master-data/pages__location_type____navigationPanel__optionsMenu__title__5": "Subcontratación", "@sage/xtrem-master-data/pages__location_type____objectTypePlural": "Tipos de ubicación", "@sage/xtrem-master-data/pages__location_type____objectTypeSingular": "Tipo de ubicación", "@sage/xtrem-master-data/pages__location_type____title": "Tipo de ubicación", "@sage/xtrem-master-data/pages__location_type__description____title": "Descripción", "@sage/xtrem-master-data/pages__location_type__id____title": "Id.", "@sage/xtrem-master-data/pages__location_type__locationCategory____title": "Categoría", "@sage/xtrem-master-data/pages__location_type__mainBlock____title": "Detalles", "@sage/xtrem-master-data/pages__location_type__mainSection____title": "General", "@sage/xtrem-master-data/pages__location_type__name____title": "Nombre", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__listItem__zoneType__title": "Tipo", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__2": "Congelación", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__3": "Sensible", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__4": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__5": "Restringida", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__6": "P<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__7": "Química", "@sage/xtrem-master-data/pages__location_zone____navigationPanel__optionsMenu__title__8": "Magnética", "@sage/xtrem-master-data/pages__location_zone____objectTypePlural": "Áreas de almacenamiento", "@sage/xtrem-master-data/pages__location_zone____objectTypeSingular": "Área de almacenamiento", "@sage/xtrem-master-data/pages__location_zone____title": "Área de almacenamiento", "@sage/xtrem-master-data/pages__location_zone__id____title": "Id.", "@sage/xtrem-master-data/pages__location_zone__name____title": "Nombre", "@sage/xtrem-master-data/pages__location_zone__section____title": "General", "@sage/xtrem-master-data/pages__location_zone__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__location_zone__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__location_zone__zoneType____title": "Tipo", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeFrom__title": "Activa desde", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__activeTo__title": "Activa hasta", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__efficiency__title": "Rendimiento", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__image__title": "Imagen", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__location__title": "Ubicación", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__minCapabilityLevel__title": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__model__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__resourceGroup__title": "Grupo de recursos", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__serialNumber__title": "Número de serie de máquina", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__listItem__weeklyShift__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__machine_resource____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__machine_resource____objectTypePlural": "Máquinas", "@sage/xtrem-master-data/pages__machine_resource____objectTypeSingular": "Máquina", "@sage/xtrem-master-data/pages__machine_resource____title": "Máquina", "@sage/xtrem-master-data/pages__machine_resource___id____title": "Id.", "@sage/xtrem-master-data/pages__machine_resource__activeFrom____title": "Activa desde", "@sage/xtrem-master-data/pages__machine_resource__activeTo____title": "Activa hasta", "@sage/xtrem-master-data/pages__machine_resource__addCostCategory____title": "Añadir categoría de coste", "@sage/xtrem-master-data/pages__machine_resource__blockContract____title": "Contrato", "@sage/xtrem-master-data/pages__machine_resource__blockDetails____title": "Configuración", "@sage/xtrem-master-data/pages__machine_resource__blockWeekly____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__machine_resource__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__cancelSidePanel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__contractId____title": "Id.", "@sage/xtrem-master-data/pages__machine_resource__contractName____title": "Nombre", "@sage/xtrem-master-data/pages__machine_resource__contractSection____title": "Contrato", "@sage/xtrem-master-data/pages__machine_resource__costBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__costSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__description____title": "Descripción", "@sage/xtrem-master-data/pages__machine_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__machine_resource__efficiency____title": "Rendimiento", "@sage/xtrem-master-data/pages__machine_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__machine_resource__id____title": "Id.", "@sage/xtrem-master-data/pages__machine_resource__isActive____title": "Activa", "@sage/xtrem-master-data/pages__machine_resource__location____columns__title__locationType__id": "Tipo", "@sage/xtrem-master-data/pages__machine_resource__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-master-data/pages__machine_resource__location____title": "Ubicación", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____columns__title__description": "Descripción", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> nivel de aptitud", "@sage/xtrem-master-data/pages__machine_resource__minCapabilityLevel____title": "<PERSON><PERSON> de aptitud", "@sage/xtrem-master-data/pages__machine_resource__model____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__name____title": "Nombre", "@sage/xtrem-master-data/pages__machine_resource__resourceCapacity____title": "Capacidad semanal", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Tipo de categoría de coste", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoria", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costCategory__name": "Categoría de coste", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__costUnit__name": "Unidad de coste", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coste indirecto", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__runCost": "Operacional", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__machine_resource__resourceCostCategories____title": "Categorías de costes de recursos", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____lookupDialogTitle": "Seleccionar grupo de recursos", "@sage/xtrem-master-data/pages__machine_resource__resourceGroup____title": "Grupo de recursos", "@sage/xtrem-master-data/pages__machine_resource__resourceImage____title": "Imagen", "@sage/xtrem-master-data/pages__machine_resource__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__machine_resource__section____title": "General", "@sage/xtrem-master-data/pages__machine_resource__serialNumber____title": "Número de serie de máquina", "@sage/xtrem-master-data/pages__machine_resource__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__machine_resource__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__country": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__id": "Id.", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__name": "Nombre", "@sage/xtrem-master-data/pages__machine_resource__supplier____columns__title__businessEntity__taxIdNumber": "NIF-IVA", "@sage/xtrem-master-data/pages__machine_resource__supplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__machine_resource__weeklyDetails____columns__title__dailyShift": "Turno diario", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____columns__title__formattedCapacity": "Capacidad", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____lookupDialogTitle": "Seleccionar turno semanal", "@sage/xtrem-master-data/pages__machine_resource__weeklyShift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__master_data__cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__master_data__confirm": "Confirmar", "@sage/xtrem-master-data/pages__master_data__update": "Actualizar", "@sage/xtrem-master-data/pages__master_data__warning-dialog-content": "¿Quieres actualizar el valor del número de secuencia?", "@sage/xtrem-master-data/pages__multiple__location__creation__success_multi": "{{num}} ubicac<PERSON>s creadas", "@sage/xtrem-master-data/pages__multiple_location_creation__success": "{{num}} ubicación creada", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__listItem__line3__title": "Tipo de entidad empresarial", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__payment_term____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__payment_term____objectTypePlural": "Condiciones de pago", "@sage/xtrem-master-data/pages__payment_term____objectTypeSingular": "Condiciones de pago", "@sage/xtrem-master-data/pages__payment_term____title": "Condiciones de pago", "@sage/xtrem-master-data/pages__payment_term___id____title": "Id.", "@sage/xtrem-master-data/pages__payment_term__amount": "Importe", "@sage/xtrem-master-data/pages__payment_term__blockDiscount____title": "Descuento", "@sage/xtrem-master-data/pages__payment_term__blockDue____title": "<PERSON><PERSON>nc<PERSON>o", "@sage/xtrem-master-data/pages__payment_term__blockPenalty____title": "Recargo", "@sage/xtrem-master-data/pages__payment_term__businessEntityType____title": "Tipo de entidad empresarial", "@sage/xtrem-master-data/pages__payment_term__days____title": "Número de días", "@sage/xtrem-master-data/pages__payment_term__description____title": "Descripción", "@sage/xtrem-master-data/pages__payment_term__discountDate____title": "Día", "@sage/xtrem-master-data/pages__payment_term__discountFrom____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__payment_term__discountType____title": "Tipo", "@sage/xtrem-master-data/pages__payment_term__dueDateType____title": "Tipo", "@sage/xtrem-master-data/pages__payment_term__id____title": "Id.", "@sage/xtrem-master-data/pages__payment_term__name____title": "Nombre", "@sage/xtrem-master-data/pages__payment_term__penaltyType____title": "Tipo", "@sage/xtrem-master-data/pages__payment_term__percentage": "Po<PERSON>entaj<PERSON>", "@sage/xtrem-master-data/pages__payment_term__section____title": "General", "@sage/xtrem-master-data/pages__reason_code____objectTypePlural": "Códigos de motivo", "@sage/xtrem-master-data/pages__reason_code____objectTypeSingular": "Código de motivo", "@sage/xtrem-master-data/pages__reason_code____title": "Código de motivo", "@sage/xtrem-master-data/pages__reason_code__id____title": "Id.", "@sage/xtrem-master-data/pages__reason_code__isActive____title": "Activo", "@sage/xtrem-master-data/pages__reason_code__mainSection____title": "General", "@sage/xtrem-master-data/pages__reason_code__name____title": "Nombre", "@sage/xtrem-master-data/pages__request_approval_dialog____title": "Solicitud de aprobación", "@sage/xtrem-master-data/pages__request_approval_dialog__approverSelectionBlock____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__default_approver": "Principal", "@sage/xtrem-master-data/pages__request_approval_dialog__email_cannot_be_sent": "La solicitud de aprobación no se puede enviar.", "@sage/xtrem-master-data/pages__request_approval_dialog__email_exception_request": "La solicitud no se ha enviado: {{exception}}", "@sage/xtrem-master-data/pages__request_approval_dialog__email_sent_to_approval": "El e-mail se ha enviado a {{value}} para aprobación.", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____helperText": "Se enviará una solicitud de aprobación a esta dirección.", "@sage/xtrem-master-data/pages__request_approval_dialog__emailAddressApproval____title": "Para", "@sage/xtrem-master-data/pages__request_approval_dialog__invalid-email": "La dirección de e-mail {{value}} no es válida.", "@sage/xtrem-master-data/pages__request_approval_dialog__requestApprovalSection____title": "Solicitud de aprobación", "@sage/xtrem-master-data/pages__request_approval_dialog__selectApprover____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__email": "E-mail", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__firstName": "Nombre", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____lookupDialogTitle": "Seleccionar usuario seleccionado", "@sage/xtrem-master-data/pages__request_approval_dialog__selectedUser____title": "<PERSON><PERSON><PERSON> se<PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_content": "¿Quieres enviar la solicitud de aprobación?", "@sage/xtrem-master-data/pages__request_approval_dialog__send_approval_request_dialog_title": "Confirmar env<PERSON> de solicitud de aprobación", "@sage/xtrem-master-data/pages__request_approval_dialog__sendApprovalRequestButton____title": "Enviar", "@sage/xtrem-master-data/pages__request_approval_dialog__substitute_approver": "Sustituto", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__email": "E-mail", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__firstName": "Nombre", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__lastName": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__request_approval_dialog__users____columns__title__type": "Aprobador", "@sage/xtrem-master-data/pages__request_approval_dialog__users____title": "Usuarios", "@sage/xtrem-master-data/pages__resource_functions__duration_in_hours_and_minutes": "{{hours}} h {{minutes}} min", "@sage/xtrem-master-data/pages__resource_group_transfer____title": "Transferencia de grupo de recursos", "@sage/xtrem-master-data/pages__resource_group_transfer__block____title": "Id.", "@sage/xtrem-master-data/pages__resource_group_transfer__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__resource_group_transfer__confirm____title": "Guardar", "@sage/xtrem-master-data/pages__resource_group_transfer__mainSection____title": "General", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____columns__title__weeklyShift__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____lookupDialogTitle": "Sele<PERSON><PERSON>r recurso", "@sage/xtrem-master-data/pages__resource_group_transfer__resource____title": "Recurso", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____columns__title__weeklyShift__id": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____lookupDialogTitle": "Seleccionar grupo de recursos", "@sage/xtrem-master-data/pages__resource_group_transfer__resourceGroup____title": "Grupo de recursos", "@sage/xtrem-master-data/pages__resource_group_transfer__type____title": "Tipo de grupo de recursos", "@sage/xtrem-master-data/pages__select_sold_to_contact_button_text": "Seleccionar contacto de cliente solicitante", "@sage/xtrem-master-data/pages__send_button_text": "Enviar", "@sage/xtrem-master-data/pages__send_email_panel____title": "Enviar presupuesto de venta", "@sage/xtrem-master-data/pages__send_email_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____helperText": "Se enviará un e-mail a esta dirección.", "@sage/xtrem-master-data/pages__send_email_panel__emailAddress____title": "E-mail", "@sage/xtrem-master-data/pages__send_email_panel__emailFirstName____title": "Nombre", "@sage/xtrem-master-data/pages__send_email_panel__emailLastName____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__send_email_panel__emailTitles____title": "Tratamiento", "@sage/xtrem-master-data/pages__send_email_panel__selectSoldToContact____title": "Seleccionar contacto de cliente solicitante", "@sage/xtrem-master-data/pages__send_email_panel__sendEmailBlock____title": "Para", "@sage/xtrem-master-data/pages__send_email_panel__sendSalesOrderButton____title": "Enviar pedido", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_4__title": "Nivel de definición", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line_5__title": "Frecuencia de restablecimiento", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line4__title": "Nivel de definición", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line5__title": "Frecuencia de restablecimiento", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line6__title": "Tipo", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__listItem__line7__title": "Control cronológico", "@sage/xtrem-master-data/pages__sequence_number____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__sequence_number____objectTypePlural": "Números de secuencia", "@sage/xtrem-master-data/pages__sequence_number____objectTypeSingular": "Número de secuencia", "@sage/xtrem-master-data/pages__sequence_number____title": "Número de secuencia", "@sage/xtrem-master-data/pages__sequence_number__addComponent____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__chronologicalControl____title": "Control de tiempo", "@sage/xtrem-master-data/pages__sequence_number__componentLength____title": "Longitud de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__constant": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__length": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number__components____columns__title__type": "Tipo", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__sequence_number__components____dropdownActions__title__2": "Componente", "@sage/xtrem-master-data/pages__sequence_number__components____title": "Componentes", "@sage/xtrem-master-data/pages__sequence_number__componentsBlock____title": "Componentes", "@sage/xtrem-master-data/pages__sequence_number__counterLength____title": "Longitud de secuencia", "@sage/xtrem-master-data/pages__sequence_number__createAction____title": "Nuevo", "@sage/xtrem-master-data/pages__sequence_number__createValue____title": "Crear valor de inicio", "@sage/xtrem-master-data/pages__sequence_number__definitionLevel____title": "Nivel de definición", "@sage/xtrem-master-data/pages__sequence_number__delete____title": "Eliminar", "@sage/xtrem-master-data/pages__sequence_number__id____title": "Id.", "@sage/xtrem-master-data/pages__sequence_number__isChronological____title": "Control cronológico", "@sage/xtrem-master-data/pages__sequence_number__isClearedByReset____title": "Forzar restablecimiento con instancia", "@sage/xtrem-master-data/pages__sequence_number__isUsed____title": "En uso", "@sage/xtrem-master-data/pages__sequence_number__mainSection____title": "General", "@sage/xtrem-master-data/pages__sequence_number__name____title": "Nombre", "@sage/xtrem-master-data/pages__sequence_number__propertiesBlock____title": "Detalles de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number__resetBlock____title": "Detalles de restablecimiento", "@sage/xtrem-master-data/pages__sequence_number__resetToZero____title": "Restablecer a cero", "@sage/xtrem-master-data/pages__sequence_number__rtzLevel____title": "Frecuencia de restablecimiento", "@sage/xtrem-master-data/pages__sequence_number__save____title": "Guardar", "@sage/xtrem-master-data/pages__sequence_number__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__sequence_number__sequence____title": "Tipo de secuencia", "@sage/xtrem-master-data/pages__sequence_number__sequenceNumberType____title": "Tipo", "@sage/xtrem-master-data/pages__sequence_number__type____title": "Tipo", "@sage/xtrem-master-data/pages__sequence_number__updateValue____title": "Actualizar valor", "@sage/xtrem-master-data/pages__sequence_number_assignment_filter": "Filtrar", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup____title": "Asignación de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__addEditAssignmentLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__company____title": "Sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__isDefaultAssignment____title": "Incluir valores por defecto", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__legislation____title": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__lineBlock____title": "Criterios", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__resultsBlock____title": "Resul<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__company__name": "Sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__isDefaultAssignment": "Por defecto", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__legislation__name": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__name": "Menú", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__name": "Documento", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__nodeFactory__title": "Documento", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__sequenceNumber__name": "Número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__columns__title__site__name": "Planta", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____levels__dropdownActions__title__2": "Eliminar", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__sequenceNumbersAssignments____title": "Resul<PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____placeholder": "Seleccionar planta", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup__site____title": "Planta", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel____title": "Panel de asignación de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title__2": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__columns__legislation__id__title__3": "Nombre", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____columns__title__legislation__id": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____placeholder": "Seleccionar sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__company____title": "Sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__edit____title": "Editar asignación de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isAssignOnPosting____title": "Asignar en contabilización", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__isUsed____title": "En uso", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____lookupDialogTitle": "Seleccionar legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____placeholder": "Seleccionar legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__legislation____title": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__new____title": "Nueva asignación de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__save____title": "Guardar", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title__2": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__columns__legislation__id__title__3": "Nombre", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__legislation__id": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____lookupDialogTitle": "Seleccionar número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumber____title": "Número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__columns__sequenceNumberAssignmentModule__id__title": "Menú", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__node": "Nodo", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__nodeFactory__name": "Documento", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__sequenceNumberAssignmentModule__id": "Menú", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____columns__title__setupId": "Id. de parametrización", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____lookupDialogTitle": "Seleccionar tipo de documento", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____placeholder": "Seleccionar número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__sequenceNumberAssignmentDocumentType____title": "Tipo de documento", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title___id": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title__id": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__2": "Id.", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__3": "Nombre", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__columns__legalCompany__id__title__4": "Legislación", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____columns__title__legalCompany__id": "Sociedad", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__sequence_number_assignment_setup_panel__site____placeholder": "Seleccionar planta", "@sage/xtrem-master-data/pages__sequence_number_dialog____title": "Actualizar valor de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_dialog__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_dialog__definitionLevel____title": "Nivel de definición", "@sage/xtrem-master-data/pages__sequence_number_dialog__length____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_dialog__rtzLevel____title": "Frecuencia de restablecimiento", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumber____title": "Número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title___updateStamp": "Última actualización", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__company__id": "Id. de sociedad", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__company__name": "Sociedad", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__newNextValue": "Siguiente valor actualizado", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__period": "Periodo", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__sequenceValue": "Siguiente valor", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-master-data/pages__sequence_number_dialog__sequenceNumberValues____columns__title__site__name": "Planta", "@sage/xtrem-master-data/pages__sequence_number_dialog__update____title": "Actualizar", "@sage/xtrem-master-data/pages__sequence_number_dialog_records_updated": "El registro se ha actualizado.", "@sage/xtrem-master-data/pages__sequence_number_value____title": "Crear valor de número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_value__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value__confirm_action_dialog_content": "¿Quieres crear el valor del número de secuencia?", "@sage/xtrem-master-data/pages__sequence_number_value__confirm-continue": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value__definitionLevel____title": "Nivel de definición", "@sage/xtrem-master-data/pages__sequence_number_value__id____title": "Número de secuencia", "@sage/xtrem-master-data/pages__sequence_number_value__minimumLength____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence_number_value__rtzLevel____title": "Frecuencia de restablecimiento", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title___updateStamp": "Última actualización", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__id": "Id. de sociedad", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__company__name": "Sociedad", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__newNextValue": "Siguiente valor actualizado", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__periodDate": "<PERSON><PERSON> de periodo", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__sequenceValue": "Siguiente valor", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__id": "<PERSON><PERSON><PERSON> de planta", "@sage/xtrem-master-data/pages__sequence_number_value__values____columns__title__site__name": "Planta", "@sage/xtrem-master-data/pages__sequence_number_value_confirm_action_dialog_title": "Confirmar", "@sage/xtrem-master-data/pages__sequence-number_value_add_new____title": "Crear valor de número de secuencia", "@sage/xtrem-master-data/pages__sequence-number_value_create____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__sequence-number_value_edit____title": "Actualizar valor", "@sage/xtrem-master-data/pages__sequence-number_value_update____title": "Actualizar", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftEnd__title": "Hora de fin", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__listItem__shiftStart__title": "Hora de inicio", "@sage/xtrem-master-data/pages__shift_detail____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__shift_detail____objectTypePlural": "Detalles de turno", "@sage/xtrem-master-data/pages__shift_detail____objectTypeSingular": "Detalles de turno", "@sage/xtrem-master-data/pages__shift_detail____title": "Detalles de turno", "@sage/xtrem-master-data/pages__shift_detail__formattedDuration____title": "Duración", "@sage/xtrem-master-data/pages__shift_detail__id____title": "Id.", "@sage/xtrem-master-data/pages__shift_detail__mainBlock____title": "Detalles", "@sage/xtrem-master-data/pages__shift_detail__mainSection____title": "General", "@sage/xtrem-master-data/pages__shift_detail__name____title": "Nombre", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftEnd____title": "Hora de fin", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____placeholder": "hh:mm", "@sage/xtrem-master-data/pages__shift_detail__shiftStart____title": "Hora de inicio", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line_4__title": "Sociedad", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__line10__title": "Planta financiera", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__sequenceNumber__title": "Valor de número de secuencia", "@sage/xtrem-master-data/pages__site____navigationPanel__listItem__taxId__title": "NIF-IVA", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__site____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__site____objectTypePlural": "Plantas", "@sage/xtrem-master-data/pages__site____objectTypeSingular": "Planta", "@sage/xtrem-master-data/pages__site____title": "Planta", "@sage/xtrem-master-data/pages__site__addressAndContactBlock____title": "Dirección", "@sage/xtrem-master-data/pages__site__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__site__addresses____columns__title__isPrimary": "Dirección principal", "@sage/xtrem-master-data/pages__site__addresses____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__addresses____dropdownActions__title__4": "Eliminar dirección y contactos", "@sage/xtrem-master-data/pages__site__addresses____title": "Direcciones", "@sage/xtrem-master-data/pages__site__addressSection____title": "Dirección", "@sage/xtrem-master-data/pages__site__already_exists_with_same_id": "Ya existe una planta con este identificador.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_name": "Ya existe una planta con este nombre.", "@sage/xtrem-master-data/pages__site__already_exists_with_same_taxIdNumber": "Ya existe una planta con este NIF-IVA.", "@sage/xtrem-master-data/pages__site__businessEntity____columns__title__isNaturalPerson": "Persona física", "@sage/xtrem-master-data/pages__site__contacts____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__contacts____columns__title__isPrimary": "Principal", "@sage/xtrem-master-data/pages__site__contacts____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__site__contacts____dropdownActions__title__3": "Eliminar", "@sage/xtrem-master-data/pages__site__contacts____headerLabel__title": "Activo", "@sage/xtrem-master-data/pages__site__contactSection____title": "Contactos", "@sage/xtrem-master-data/pages__site__country____columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__site__country____columns__title__regionLabel": "Etiqueta de región", "@sage/xtrem-master-data/pages__site__country____columns__title__zipLabel": "Etiqueta de código postal", "@sage/xtrem-master-data/pages__site__country____lookupDialogTitle": "Seleccionar país", "@sage/xtrem-master-data/pages__site__createFromBusinessEntity____title": "Crear a partir de entidad empresarial", "@sage/xtrem-master-data/pages__site__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__site__defaultLocation____lookupDialogTitle": "Seleccionar ubicación por defecto", "@sage/xtrem-master-data/pages__site__defaultLocation____title": "Ubicación por defecto", "@sage/xtrem-master-data/pages__site__description____title": "Descripción", "@sage/xtrem-master-data/pages__site__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title": "Contactos", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__2": "Dirección principal", "@sage/xtrem-master-data/pages__site__displayAddresses____columns__title__isPrimaryForAnotherEntity": "Principal para otra entidad", "@sage/xtrem-master-data/pages__site__financialSite____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__site__financialSite____lookupDialogTitle": "Seleccionar planta financiera", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createdBy": "<PERSON><PERSON>o por", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__createStamp": "Creación", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updatedBy": "Actualizado por", "@sage/xtrem-master-data/pages__site__groupRoleSites____columns__title__updateStamp": "Actualización", "@sage/xtrem-master-data/pages__site__groupRoleSites____title": "Grupos de autorización", "@sage/xtrem-master-data/pages__site__hierarchyChartContent____title": "Organización", "@sage/xtrem-master-data/pages__site__imageBlock____title": "Imagen", "@sage/xtrem-master-data/pages__site__isFinance____title": "Contabilidad", "@sage/xtrem-master-data/pages__site__isInventory____title": "Stock", "@sage/xtrem-master-data/pages__site__isLocationManaged____title": "Gestión de ubicación", "@sage/xtrem-master-data/pages__site__isManufacturing____title": "Producción", "@sage/xtrem-master-data/pages__site__isPurchase____title": "Compras", "@sage/xtrem-master-data/pages__site__isSales____title": "Ventas", "@sage/xtrem-master-data/pages__site__isSequenceNumberIdUsed____title": "Número de secuencia utilizado", "@sage/xtrem-master-data/pages__site__legalCompany____columns__title__isActive": "Activa", "@sage/xtrem-master-data/pages__site__legalCompany____lookupDialogTitle": "Seleccionar sociedad", "@sage/xtrem-master-data/pages__site__mainBlock____title": "General", "@sage/xtrem-master-data/pages__site__mainSection____title": "General", "@sage/xtrem-master-data/pages__site__managementSection____title": "Gestión", "@sage/xtrem-master-data/pages__site__save____title": "Guardar", "@sage/xtrem-master-data/pages__site__sequenceNumberId____title": "Valor de número de secuencia", "@sage/xtrem-master-data/pages__site__siteGroupBlock____title": "Grupos de plantas", "@sage/xtrem-master-data/pages__site__siteGroups____columns__title__isLegalCompany": "Sociedad", "@sage/xtrem-master-data/pages__site__siteGroups____title": "Grupos de plantas", "@sage/xtrem-master-data/pages__site__siteGroupSection____title": "Grupos de plantas", "@sage/xtrem-master-data/pages__site__timeZone____title": "Zona horaria", "@sage/xtrem-master-data/pages__site__userGroupBlock____title": "Grupos de autorización", "@sage/xtrem-master-data/pages__site__userGroupSection____title": "Grupos de usuarios", "@sage/xtrem-master-data/pages__site__website____title": "Sitio web", "@sage/xtrem-master-data/pages__site_page_taxIdNumber_required_if_no_business_entity": "Hay que indicar un NIF-IVA si no se ha seleccionado ninguna entidad empresarial.", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line3__title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard____navigationPanel__listItem__line6__title": "Sector", "@sage/xtrem-master-data/pages__standard____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__standard____objectTypePlural": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard____title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard__code____title": "Código", "@sage/xtrem-master-data/pages__standard__createStandard____title": "Nuevo", "@sage/xtrem-master-data/pages__standard__deleteStandard____title": "Eliminar", "@sage/xtrem-master-data/pages__standard__id____title": "Id.", "@sage/xtrem-master-data/pages__standard__idBlock____title": "Id.", "@sage/xtrem-master-data/pages__standard__industrySector____title": "Sector", "@sage/xtrem-master-data/pages__standard__mainBlock____title": "General", "@sage/xtrem-master-data/pages__standard__mainSection____title": "General", "@sage/xtrem-master-data/pages__standard__name____title": "Nombre", "@sage/xtrem-master-data/pages__standard__saveStandard____title": "Guardar", "@sage/xtrem-master-data/pages__standard__sdo____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__standard__version____title": "Versión", "@sage/xtrem-master-data/pages__supplier____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__isActive__title": "Activo", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line10__title": "Categoría de cliente", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line7__title": "Importe mínimo de pedido", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__line8__title": "Condiciones de pago", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__columns__title__id": "Id.", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__supplier____navigationPanel__listItem__titleLine__title": "Nombre", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__2": "Activos", "@sage/xtrem-master-data/pages__supplier____navigationPanel__optionsMenu__title__3": "Inactivos", "@sage/xtrem-master-data/pages__supplier____objectTypePlural": "<PERSON>veed<PERSON>", "@sage/xtrem-master-data/pages__supplier____objectTypeSingular": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier___id____title": "Id.", "@sage/xtrem-master-data/pages__supplier__addCertificate____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addItem____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addPriceLine____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addressAndContactBlock____title": "Dirección", "@sage/xtrem-master-data/pages__supplier__addresses____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__concatenatedAddressWithoutName": "Dirección sin nombre", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__isPrimary": "Dirección principal", "@sage/xtrem-master-data/pages__supplier__addresses____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__3": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__addresses____dropdownActions__title__4": "Eliminar dirección y contactos", "@sage/xtrem-master-data/pages__supplier__addresses____title": "Direcciones", "@sage/xtrem-master-data/pages__supplier__addressSection____title": "Dirección", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_id": "Ya existe un proveedor con este identificador.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_name": "Ya existe un proveedor con este nombre.", "@sage/xtrem-master-data/pages__supplier__already_exists_with_same_taxIdNumber": "Ya existe un proveedor con este NIF-IVA.", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__concatenatedAddress": "Dirección de facturador principal", "@sage/xtrem-master-data/pages__supplier__billByAddress____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__billByAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__billByAddress____title": "Dirección de facturador principal", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine1": "Línea 1", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__addressLine2": "Línea 2", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__billByAddressLookup____columns__title__postcode": "Código postal", "@sage/xtrem-master-data/pages__supplier__billBySupplier____title": "Proveedor <PERSON>dor", "@sage/xtrem-master-data/pages__supplier__businessEntity____columns__title__isNaturalPerson": "Persona física", "@sage/xtrem-master-data/pages__supplier__category____columns__title__sequenceNumber__name": "Número de secuencia", "@sage/xtrem-master-data/pages__supplier__category____lookupDialogTitle": "Seleccionar categoría", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__certificationBody": "Organismo de certificación", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfCertification": "Fecha de certificación", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__dateOfOriginalCertification": "Fecha de certificación original", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__id": "Referencia de certificado", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__standard__id": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__certificates____columns__title__validUntil": "<PERSON><PERSON><PERSON><PERSON> hasta", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__certificates____dropdownActions__title__2": "Eliminar", "@sage/xtrem-master-data/pages__supplier__certificates____title": "Certificados", "@sage/xtrem-master-data/pages__supplier__certificateSection____title": "Certificados", "@sage/xtrem-master-data/pages__supplier__commercialBlock____title": "Comercial", "@sage/xtrem-master-data/pages__supplier__commercialSection____title": "Comercial", "@sage/xtrem-master-data/pages__supplier__contacts____addButtonText": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__isPrimary": "Principal", "@sage/xtrem-master-data/pages__supplier__contacts____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__2": "Definir como principal", "@sage/xtrem-master-data/pages__supplier__contacts____dropdownActions__title__3": "Eliminar", "@sage/xtrem-master-data/pages__supplier__contacts____headerLabel__title": "Activo", "@sage/xtrem-master-data/pages__supplier__contactSection____title": "Contactos", "@sage/xtrem-master-data/pages__supplier__country____columns__title__id": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__supplier__country____lookupDialogTitle": "Seleccionar país", "@sage/xtrem-master-data/pages__supplier__createFromBusinessEntity____title": "Crear a partir de entidad empresarial", "@sage/xtrem-master-data/pages__supplier__currency____lookupDialogTitle": "Seleccionar divisa", "@sage/xtrem-master-data/pages__supplier__deliveryMode____lookupDialogTitle": "Seleccionar modo de entrega", "@sage/xtrem-master-data/pages__supplier__deliveryMode____title": "Modo de en<PERSON>ga", "@sage/xtrem-master-data/pages__supplier__display_primary_address": "Principal", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title": "Contactos", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__2": "Dirección principal", "@sage/xtrem-master-data/pages__supplier__displayAddresses____columns__title__isPrimaryForAnotherEntity": "Principal para otra entidad", "@sage/xtrem-master-data/pages__supplier__euVatNumber____title": "NIF-IVA", "@sage/xtrem-master-data/pages__supplier__financialBlock____title": "Contabilidad", "@sage/xtrem-master-data/pages__supplier__financialSection____title": "Contabilidad", "@sage/xtrem-master-data/pages__supplier__imageBlock____title": "Imagen", "@sage/xtrem-master-data/pages__supplier__incoterm____lookupDialogTitle": "Seleccionar Incoterms®", "@sage/xtrem-master-data/pages__supplier__incoterm____title": "Incoterms®", "@sage/xtrem-master-data/pages__supplier__industrySpecificBlock____title": "Certificados", "@sage/xtrem-master-data/pages__supplier__internalNote____title": "Notas internas", "@sage/xtrem-master-data/pages__supplier__isNaturalPerson____title": "Persona física", "@sage/xtrem-master-data/pages__supplier__itemBlock____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isActive": "Activo", "@sage/xtrem-master-data/pages__supplier__items____columns__title__isDefaultItemSupplier": "Proveedor por defecto", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__supplier__items____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____columns__title__minimumPurchaseQuantity": "Cantidad mínima de compra", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseLeadTime": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__id": "Unidad de compra", "@sage/xtrem-master-data/pages__supplier__items____columns__title__purchaseUnitOfMeasure__name": "Unidad de compra", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemCode": "<PERSON>ó<PERSON> artículo-<PERSON>edor", "@sage/xtrem-master-data/pages__supplier__items____columns__title__supplierItemName": "Art<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__items____dropdownActions__title__2": "Eliminar", "@sage/xtrem-master-data/pages__supplier__items____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__itemSection____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__mainBlock____title": "General", "@sage/xtrem-master-data/pages__supplier__mainSection____title": "General", "@sage/xtrem-master-data/pages__supplier__minimumOrderAmount____title": "Importe mínimo de pedido", "@sage/xtrem-master-data/pages__supplier__noteBlock____title": "Notas", "@sage/xtrem-master-data/pages__supplier__noteSection____title": "Notas", "@sage/xtrem-master-data/pages__supplier__parent____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>or primario", "@sage/xtrem-master-data/pages__supplier__parent____title": "Proveedor primario", "@sage/xtrem-master-data/pages__supplier__paymentMethod____title": "Forma de pago", "@sage/xtrem-master-data/pages__supplier__paymentTerm____lookupDialogTitle": "Seleccionar condiciones de pago", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__concatenatedAddress": "Dirección de pago principal", "@sage/xtrem-master-data/pages__supplier__payToAddress____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__payToAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__payToAddress____title": "Dirección de pago principal", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine1": "Línea 1", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__addressLine2": "Línea 2", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__payToAddressLookup____columns__title__postcode": "Código postal", "@sage/xtrem-master-data/pages__supplier__payToSupplier____title": "Proveedor <PERSON>dor", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title": "Nombre", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__2": "Código ISO 3166-1 alfa-2", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__3": "Id.", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__4": "Etiqueta de región", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__columns__country__name__title__5": "Etiqueta de código postal", "@sage/xtrem-master-data/pages__supplier__primaryAddress____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__concatenatedAddress": "Dirección de devolución principal", "@sage/xtrem-master-data/pages__supplier__returnToAddress____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__returnToAddress____dropdownActions__title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__returnToAddress____title": "Dirección de devolución principal", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine1": "Línea 1", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__addressLine2": "Línea 2", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__businessEntity__name": "Entidad empresarial", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__country__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__locationPhoneNumber": "Número de teléfono", "@sage/xtrem-master-data/pages__supplier__returnToAddressLookup____columns__title__postcode": "Código postal", "@sage/xtrem-master-data/pages__supplier__save____title": "Guardar", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__currency__id": "Divisa", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidFrom": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__dateValidTo": "<PERSON> validez", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__fromQuantity": "Desde can<PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__description": "Descripción de artículo", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__id": "Id<PERSON> <PERSON> artí<PERSON>lo", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__id": "Planta", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__site__name": "Planta", "@sage/xtrem-master-data/pages__supplier__supplierPrices____columns__title__toQuantity": "<PERSON><PERSON> can<PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__supplier__supplierPrices____dropdownActions__title__2": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPrices____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier__supplierPricesBlock____title": "<PERSON>cios de proveedores", "@sage/xtrem-master-data/pages__supplier__website____title": "Sitio web", "@sage/xtrem-master-data/pages__supplier_certificate_panel____title": "Certificado de proveedor", "@sage/xtrem-master-data/pages__supplier_certificate_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_certificate_panel__certificationBody____title": "Organismo de certificación", "@sage/xtrem-master-data/pages__supplier_certificate_panel__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfCertification____title": "Fecha de certificación", "@sage/xtrem-master-data/pages__supplier_certificate_panel__dateOfOriginalCertification____title": "Fecha de certificación original", "@sage/xtrem-master-data/pages__supplier_certificate_panel__edit____title": "Editar certificado de proveedor", "@sage/xtrem-master-data/pages__supplier_certificate_panel__id____title": "Referencia de certificado", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainBlock____title": "Certificado", "@sage/xtrem-master-data/pages__supplier_certificate_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__supplier_certificate_panel__new____title": "Nuevo certificado de proveedor", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__code": "Código", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__industrySector": "Sector", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__sdo": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____columns__title__version": "Versión", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_certificate_panel__standard____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_certificate_panel__validUntil____title": "Fecha de fin de validez", "@sage/xtrem-master-data/pages__supplier_item_panel____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel___id____title": "Id.", "@sage/xtrem-master-data/pages__supplier_item_panel__cancel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__confirm____title": "Aceptar", "@sage/xtrem-master-data/pages__supplier_item_panel__isDefaultItemSupplier____title": "Proveedor por defecto", "@sage/xtrem-master-data/pages__supplier_item_panel__item____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__supplier_item_panel__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__mainBlock____title": "Art<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__mainSection____title": "General", "@sage/xtrem-master-data/pages__supplier_item_panel__minimumPurchaseQuantity____title": "Cantidad mínima de compra", "@sage/xtrem-master-data/pages__supplier_item_panel__new____title": "Nuevo artículo-proveedor", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseLeadTime____title": "<PERSON><PERSON><PERSON> de compra", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____lookupDialogTitle": "Seleccionar unidad", "@sage/xtrem-master-data/pages__supplier_item_panel__purchaseUnitOfMeasure____title": "Unidad", "@sage/xtrem-master-data/pages__supplier_item_panel__supplier____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemCode____title": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__supplier_item_panel__supplierItemName____title": "Nombre de artículo-proveedor", "@sage/xtrem-master-data/pages__team____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__team____objectTypePlural": "Equipos", "@sage/xtrem-master-data/pages__team____objectTypeSingular": "Equipo", "@sage/xtrem-master-data/pages__team____title": "Equipo", "@sage/xtrem-master-data/pages__team__createAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__team__deleteAction____title": "Eliminar", "@sage/xtrem-master-data/pages__team__description____title": "Descripción", "@sage/xtrem-master-data/pages__team__id____title": "Id.", "@sage/xtrem-master-data/pages__team__mainBlock____title": "Detalles", "@sage/xtrem-master-data/pages__team__mainSection____title": "General", "@sage/xtrem-master-data/pages__team__name____title": "Nombre", "@sage/xtrem-master-data/pages__team__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__team__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__team__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__bulkActions__title": "Eliminar", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeFrom__title": "Activa desde", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__activeTo__title": "Activa hasta", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__postfix": "%", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__efficiency__title": "Rendimiento", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__image__title": "Imagen", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__line2__title": "Planta", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__location__title": "Ubicación", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__resourceGroup__title": "Grupo de recursos", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__listItem__weeklyShift__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__2": "Activas", "@sage/xtrem-master-data/pages__tool_resource____navigationPanel__optionsMenu__title__3": "Inactivas", "@sage/xtrem-master-data/pages__tool_resource____objectTypePlural": "Herramientas", "@sage/xtrem-master-data/pages__tool_resource____objectTypeSingular": "Herramienta", "@sage/xtrem-master-data/pages__tool_resource____title": "Herramienta", "@sage/xtrem-master-data/pages__tool_resource___id____title": "Id.", "@sage/xtrem-master-data/pages__tool_resource__activeFrom____title": "Activa desde", "@sage/xtrem-master-data/pages__tool_resource__activeTo____title": "Activa hasta", "@sage/xtrem-master-data/pages__tool_resource__addCostCategory____title": "Añadir categoría de coste", "@sage/xtrem-master-data/pages__tool_resource__blockDetails____title": "Configuración", "@sage/xtrem-master-data/pages__tool_resource__blockWeekly____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__tool_resource__cancelAction____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__cancelSidePanel____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__consumptionMode____title": "Modo de consumo", "@sage/xtrem-master-data/pages__tool_resource__costBlock____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__costSection____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__description____title": "Descripción", "@sage/xtrem-master-data/pages__tool_resource__efficiency____postfix": "%", "@sage/xtrem-master-data/pages__tool_resource__efficiency____title": "Rendimiento", "@sage/xtrem-master-data/pages__tool_resource__fullWeek____title": "24/7", "@sage/xtrem-master-data/pages__tool_resource__hoursTracked____title": "Tiempo de vida", "@sage/xtrem-master-data/pages__tool_resource__id____title": "Id.", "@sage/xtrem-master-data/pages__tool_resource__isActive____title": "Activa", "@sage/xtrem-master-data/pages__tool_resource__item____columns__title__category__name": "Categoría", "@sage/xtrem-master-data/pages__tool_resource__item____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__item____title": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__location____columns__title__locationType__id": "Tipo", "@sage/xtrem-master-data/pages__tool_resource__location____lookupDialogTitle": "Seleccionar ubicación", "@sage/xtrem-master-data/pages__tool_resource__location____title": "Ubicación", "@sage/xtrem-master-data/pages__tool_resource__name____title": "Nombre", "@sage/xtrem-master-data/pages__tool_resource__quantity____title": "Cantidad", "@sage/xtrem-master-data/pages__tool_resource__resourceCapacity____title": "Capacidad semanal", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title": "Tipo de categoría de coste", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costCategory__name__title__2": "Obligatoria", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__columns__costUnit__name__title__3": "Símbolo", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costCategory__name": "Categoría de coste", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__costUnit__name": "Unidad de coste", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__indirectCostSection__name": "Coste indirecto", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__runCost": "Operacional", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____columns__title__setupCost": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__tool_resource__resourceCostCategories____title": "Categorías de costes de recursos", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____lookupDialogTitle": "Seleccionar grupo de recursos", "@sage/xtrem-master-data/pages__tool_resource__resourceGroup____title": "Grupo de recursos", "@sage/xtrem-master-data/pages__tool_resource__resourceImage____title": "Imagen", "@sage/xtrem-master-data/pages__tool_resource__saveAction____title": "Guardar", "@sage/xtrem-master-data/pages__tool_resource__saveSidePanel____title": "Guardar", "@sage/xtrem-master-data/pages__tool_resource__section____title": "General", "@sage/xtrem-master-data/pages__tool_resource__site____columns__columns__legalCompany__id__columns__title__decimalDigits": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__site____columns__columns__legalCompany__id__title": "Símbolo", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__id": "Sociedad", "@sage/xtrem-master-data/pages__tool_resource__site____columns__title__legalCompany__name": "Sociedad", "@sage/xtrem-master-data/pages__tool_resource__site____lookupDialogTitle": "Seleccionar planta", "@sage/xtrem-master-data/pages__tool_resource__toolDetails____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__tool_resource__unitProduced____title": "Unidades producidas", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__capacity": "Capacidad", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__dailyShift": "Turno diario", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__day": "Día", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift1": "Turno 1", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift2": "Turno 2", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift3": "Turno 3", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift4": "Turno 4", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____columns__title__shift5": "Turno 5", "@sage/xtrem-master-data/pages__tool_resource__weeklyDetails____title": "Detalles de turno semanal", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____columns__title__formattedCapacity": "Capacidad", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____lookupDialogTitle": "Seleccionar turno semanal", "@sage/xtrem-master-data/pages__tool_resource__weeklyShift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line_4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line3__title": "Tipo de unidad", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__listItem__line4__title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure____navigationPanel__optionsMenu__title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypePlural": "Unidades de medida", "@sage/xtrem-master-data/pages__unit_of_measure____objectTypeSingular": "Unidad de medida", "@sage/xtrem-master-data/pages__unit_of_measure____title": "Unidad de medida", "@sage/xtrem-master-data/pages__unit_of_measure__addConversion____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionBlock____title": "Conversión", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title": "Nombre", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__customer__businessEntity__name__title__2": "Id.", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__columns__item__name__title": "Categoría", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__lookupDialogTitle__item__name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__2": "Unidad de destino", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__3": "=>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title___id__4": "Coeficiente inverso", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__coefficient": "Coeficiente", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__customer__businessEntity__name": "Cliente", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__fromUnit__name": "Unidad base", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__isStandard": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__item__name": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____columns__title__type": "Tipo de flujo", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____dropdownActions__title": "Eliminar", "@sage/xtrem-master-data/pages__unit_of_measure__conversionFactor____title": "Conversión", "@sage/xtrem-master-data/pages__unit_of_measure__conversionSection____title": "Conversión", "@sage/xtrem-master-data/pages__unit_of_measure__decimalDigits____title": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages__unit_of_measure__description____title": "Descripción", "@sage/xtrem-master-data/pages__unit_of_measure__generalSection____title": "General", "@sage/xtrem-master-data/pages__unit_of_measure__id____title": "Id.", "@sage/xtrem-master-data/pages__unit_of_measure__isActive____title": "Activa", "@sage/xtrem-master-data/pages__unit_of_measure__mainBlock____title": "General", "@sage/xtrem-master-data/pages__unit_of_measure__name____title": "Nombre", "@sage/xtrem-master-data/pages__unit_of_measure__save____title": "Guardar", "@sage/xtrem-master-data/pages__unit_of_measure__symbol____title": "Símbolo", "@sage/xtrem-master-data/pages__unit_of_measure__type____title": "Tipo de unidad", "@sage/xtrem-master-data/pages__utils__notification__custom_validation_error": "Ha habido errores de validación:\n{{#each errors}}\t- {{this}}{{#unless @last}}\n{{/unless}}{{/each}}", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__formattedCapacity__title": "Capacidad", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__listItem__isFullWeek__title": "Semana completa", "@sage/xtrem-master-data/pages__weekly_shift____navigationPanel__optionsMenu__title": "Todos", "@sage/xtrem-master-data/pages__weekly_shift____objectTypePlural": "<PERSON><PERSON> se<PERSON>", "@sage/xtrem-master-data/pages__weekly_shift____objectTypeSingular": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift___id____title": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__detailsBlock____title": "Detalles", "@sage/xtrem-master-data/pages__weekly_shift__formattedCapacity____title": "Capacidad", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____lookupDialogTitle": "Seleccionar turno de viernes", "@sage/xtrem-master-data/pages__weekly_shift__fridayShift____title": "<PERSON><PERSON> de v<PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__id____title": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__isFullWeek____title": "Semana completa", "@sage/xtrem-master-data/pages__weekly_shift__mainBlock____title": "General", "@sage/xtrem-master-data/pages__weekly_shift__mainSection____title": "General", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____lookupDialogTitle": "Seleccionar turno de lunes", "@sage/xtrem-master-data/pages__weekly_shift__mondayShift____title": "<PERSON><PERSON> <PERSON> lunes", "@sage/xtrem-master-data/pages__weekly_shift__name____title": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____lookupDialogTitle": "Seleccionar turno de <PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__saturdayShift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____lookupDialogTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> turno de domingo", "@sage/xtrem-master-data/pages__weekly_shift__sundayShift____title": "<PERSON><PERSON> de <PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____lookupDialogTitle": "Se<PERSON><PERSON><PERSON><PERSON> turno de jueves", "@sage/xtrem-master-data/pages__weekly_shift__thursdayShift____title": "<PERSON><PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____lookupDialogTitle": "Se<PERSON><PERSON>onar turno de martes", "@sage/xtrem-master-data/pages__weekly_shift__tuesdayShift____title": "<PERSON><PERSON> <PERSON>", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__id": "Id.", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____columns__title__name": "Nombre", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____lookupDialogTitle": "Seleccionar turno de miércoles", "@sage/xtrem-master-data/pages__weekly_shift__wednesdayShift____title": "<PERSON><PERSON> <PERSON> mi<PERSON>", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_content": "¿Quieres eliminar este registro?", "@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_title": "Confirmar eliminación", "@sage/xtrem-master-data/pages_currency_delete_inverse_rate_page_dialog_content": "La fecha indicada ya está vinculada a un tipo de cambio inverso. ¿Quieres mantener el tipo de cambio inverso o eliminarlo?", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_content": "¿Quieres eliminar este registro?", "@sage/xtrem-master-data/pages_currency_delete_page_dialog_title": "Confirmar eliminación", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_content": "¿Quieres eliminar este registro?", "@sage/xtrem-master-data/pages_sequence_number_assignment_delete_page_dialog_title": "Confirmar eliminación", "@sage/xtrem-master-data/pages_sidebar_block_title_definition": "Definición", "@sage/xtrem-master-data/pages_sidebar_block_title_price": "Elementos de precio", "@sage/xtrem-master-data/pages_sidebar_block_title_ranges": "Rangos", "@sage/xtrem-master-data/pages_sidebar_tab_title_definition": "Definición", "@sage/xtrem-master-data/pages_sidebar_tab_title_information": "Información", "@sage/xtrem-master-data/pages_sidebar_tab_title_prices": "Elementos de precio", "@sage/xtrem-master-data/pages_sidebar_tab_title_ranges": "Rangos", "@sage/xtrem-master-data/pages_site__address_mandatory": "Asigna al menos una dirección a la planta.", "@sage/xtrem-master-data/pages_supplier__address_mandatory": "<PERSON><PERSON>a al menos una dirección al proveedor.", "@sage/xtrem-master-data/pages-cancel-keep": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-apply": "Aplicar nuevo", "@sage/xtrem-master-data/pages-confirm-apply-new": "Aplicar nuevo", "@sage/xtrem-master-data/pages-confirm-cancel": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-continue": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/pages-confirm-delete": "Eliminar", "@sage/xtrem-master-data/pages-confirm-no": "No", "@sage/xtrem-master-data/pages-confirm-send": "Enviar", "@sage/xtrem-master-data/pages-confirm-yes": "Sí", "@sage/xtrem-master-data/permission__convert_from_to__name": "Convertir de unidad a unidad", "@sage/xtrem-master-data/permission__create__name": "<PERSON><PERSON><PERSON>", "@sage/xtrem-master-data/permission__create_bulk_license_plate_numbers__name": "<PERSON><PERSON>r númer<PERSON> de contenedor interno en masa", "@sage/xtrem-master-data/permission__create_bulk_locations__name": "Crear ubicaciones en masa", "@sage/xtrem-master-data/permission__delete__name": "Eliminar", "@sage/xtrem-master-data/permission__get_item_site_cost__name": "Obtener coste de artículo-planta", "@sage/xtrem-master-data/permission__get_locations__name": "Obtener ubicaciones", "@sage/xtrem-master-data/permission__get_purchase_unit__name": "Obtener unidad de compra", "@sage/xtrem-master-data/permission__get_unit_conversion_factor__name": "Obtener coeficiente de conversión de unidades", "@sage/xtrem-master-data/permission__get_valued_item_site__name": "Obtener artículo-planta valorado", "@sage/xtrem-master-data/permission__manage__name": "Gestionar", "@sage/xtrem-master-data/permission__read__name": "<PERSON><PERSON>", "@sage/xtrem-master-data/permission__update__name": "Actualizar", "@sage/xtrem-master-data/sales-to-stock-unit-must-be-one": "Establece el coeficiente de conversión de unidad de venta a stock en 1 cuando las unidades sean idénticas.", "@sage/xtrem-master-data/service_options__allocation_transfer_option__name": "Opción de transferencia de asignación", "@sage/xtrem-master-data/service_options__bill_of_material_revision_service_option__name": "Opción de servicio de revisión de estructura de materiales", "@sage/xtrem-master-data/service_options__customer_360_view_option__name": "Opción de vista de cliente 360", "@sage/xtrem-master-data/service_options__datev_option__name": "Opción de DATEV", "@sage/xtrem-master-data/service_options__fifo_valuation_method_option__name": "Opción de método de valoración FIFO", "@sage/xtrem-master-data/service_options__intersite_stock_transfer_option__name": "Opción de transferencia de stock interplanta", "@sage/xtrem-master-data/service_options__landed_cost_option__name": "Opción de gastos de entrega", "@sage/xtrem-master-data/service_options__landed_cost_order_option__name": "Opción de pedido de gastos de entrega", "@sage/xtrem-master-data/service_options__landed_cost_stock_transfer_option__name": "Opción de gastos de entrega en transferencia de stock", "@sage/xtrem-master-data/service_options__order_to_order_option__name": "Opción de pedido por pedido", "@sage/xtrem-master-data/service_options__phantom_item_option__name": "Opción de artículo fantasma", "@sage/xtrem-master-data/service_options__serial_number_option__name": "Opción de número de serie", "@sage/xtrem-master-data/site-etension-financial-currency-not-defined": "La divisa financiera no está definida.", "@sage/xtrem-master-data/site-extension-financial-currency-not-defined": "La divisa financiera no está definida.", "@sage/xtrem-master-data/telephone-validation-error": "El número de teléfono no es válido.", "@sage/xtrem-master-data/update-confirmation": "El registro se ha actualizado.", "@sage/xtrem-master-data/use-existing-business-entity": "Se ha encontrado una entidad empresarial con el nombre {{beName}}, el identificador {{beId}} y el identificador fiscal {{beTaxId}}. ¿Quieres utilizarla?", "@sage/xtrem-master-data/value-must-be-greater-than-current-sequence": "El valor actualizado es inferior al actual.", "@sage/xtrem-master-data/value-must-be-positive": "{{value}} debe ser un valor positivo.", "@sage/xtrem-master-data/value-must-not-exceed-the-length-of-sequence-number": "El valor actualizado debe ser inferior a la longitud del número de secuencia.", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__addresses__title": "Ver direcciones", "@sage/xtrem-master-data/widgets__customer_contact_list____callToActions__contacts__title": "Ver contactos", "@sage/xtrem-master-data/widgets__customer_contact_list____title": "Contactos de cliente", "@sage/xtrem-master-data/widgets__system_version____title": "Versión del sistema"}